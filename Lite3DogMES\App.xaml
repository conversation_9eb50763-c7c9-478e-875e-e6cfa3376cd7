&lt;Application x:Class="Lite3DogMES.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="UI/MainWindow.xaml">
    &lt;Application.Resources>
        &lt;!-- 全局样式资源 -->
        &lt;Style x:Key="GlobalButtonStyle" TargetType="Button">
            &lt;Setter Property="Background" Value="#FF007ACC"/>
            &lt;Setter Property="Foreground" Value="White"/>
            &lt;Setter Property="BorderThickness" Value="0"/>
            &lt;Setter Property="Padding" Value="15,8"/>
            &lt;Setter Property="Margin" Value="5"/>
            &lt;Setter Property="FontSize" Value="14"/>
            &lt;Setter Property="FontWeight" Value="SemiBold"/>
            &lt;Setter Property="Cursor" Value="Hand"/>
            &lt;Setter Property="Template">
                &lt;Setter.Value>
                    &lt;ControlTemplate TargetType="Button">
                        &lt;Border Background="{TemplateBinding Background}" 
                                CornerRadius="3"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            &lt;ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        &lt;/Border>
                        &lt;ControlTemplate.Triggers>
                            &lt;Trigger Property="IsMouseOver" Value="True">
                                &lt;Setter Property="Background" Value="#FF1E90FF"/>
                            &lt;/Trigger>
                            &lt;Trigger Property="IsPressed" Value="True">
                                &lt;Setter Property="Background" Value="#FF0066CC"/>
                            &lt;/Trigger>
                        &lt;/ControlTemplate.Triggers>
                    &lt;/ControlTemplate>
                &lt;/Setter.Value>
            &lt;/Setter>
        &lt;/Style>
    &lt;/Application.Resources>
&lt;/Application>
