﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0260152B-3630-4304-AC97-B12B2CB760D8}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MMIS</RootNamespace>
    <AssemblyName>MMIS</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>bitbug_favicon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Greenery.SocketClient.Protocol, Version=1.0.0.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Greenery.SocketClient.Protocol.1.0.0.4\lib\net40\Greenery.SocketClient.Protocol.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=1.2.13.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.3\lib\net40-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=14.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.6.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.ClientEngine, Version=0.8.0.7, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.ClientEngine.0.8.0.7\lib\net45\SuperSocket.ClientEngine.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.Common, Version=1.6.6.1, Culture=neutral, PublicKeyToken=6c80000676988ebb, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.1.6.6.1\lib\net45\SuperSocket.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.Facility, Version=1.6.6.1, Culture=neutral, PublicKeyToken=6c80000676988ebb, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.1.6.6.1\lib\net45\SuperSocket.Facility.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.ProtoBase, Version=1.7.0.12, Culture=neutral, PublicKeyToken=6c80000676988ebb, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.ProtoBase.1.7.0.12\lib\net35-client\SuperSocket.ProtoBase.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.SocketBase, Version=1.6.6.1, Culture=neutral, PublicKeyToken=6c80000676988ebb, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.1.6.6.1\lib\net45\SuperSocket.SocketBase.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.SocketEngine, Version=1.6.6.1, Culture=neutral, PublicKeyToken=6c80000676988ebb, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.Engine.1.6.6.1\lib\net45\SuperSocket.SocketEngine.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SuperSocket.SocketService, Version=1.6.6.1, Culture=neutral, PublicKeyToken=6c80000676988ebb, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.Engine.1.6.6.1\lib\net45\SuperSocket.SocketService.exe</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="AGVClient\AGVMsgHandle.cs" />
    <Compile Include="AGVClient\AGVUIAHandle.cs" />
    <Compile Include="Assist\DataTransform.cs" />
    <Compile Include="AGVClient\SendPackage.cs" />
    <Compile Include="Config\ConfigClass.cs" />
    <Compile Include="Config\ReadWriteXml.cs" />
    <Compile Include="Assist\DataBaseHandle.cs" />
    <Compile Include="Assist\ExportToExcel.cs" />
    <Compile Include="ExecuteOrder.cs" />
    <Compile Include="Assist\KuweiNumber.cs" />
    <Compile Include="Assist\KuweiShow.xaml.cs">
      <DependentUpon>KuweiShow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assist\OrderState.cs" />
    <Compile Include="LogInfoHelp.cs" />
    <Compile Include="LoginWindow.xaml.cs">
      <DependentUpon>LoginWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Server\ServerUIHandle.cs" />
    <Compile Include="Server\DTUSendPackage.cs" />
    <Compile Include="Server\DTUFixedHeaderReceiveFilter.cs" />
    <Compile Include="Server\DTUReceiveFilterFactory.cs" />
    <Compile Include="Server\DTURequestInfo.cs" />
    <Compile Include="Server\DTUServer.cs" />
    <Compile Include="Server\DTUSession.cs" />
    <Compile Include="AGVClient\PackageInfo.cs" />
    <Compile Include="AGVClient\SocketRequestFilter.cs" />
    <Compile Include="Server\StateMachine.cs" />
    <Compile Include="UI\ucDigitalClock.xaml.cs">
      <DependentUpon>ucDigitalClock.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\ucHDRTile.xaml.cs">
      <DependentUpon>ucHDRTile.xaml</DependentUpon>
    </Compile>
    <Compile Include="UserWindow.xaml.cs">
      <DependentUpon>UserWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="WHClient\WHPackageInfo.cs" />
    <Compile Include="WHClient\WHSocketRequestFilter.cs" />
    <Compile Include="WHClient\WHConfig.cs" />
    <Compile Include="WHClient\WHSendPackage.cs" />
    <Compile Include="Assist\WHPosition.cs" />
    <Compile Include="Assist\WHPositionConvert.cs" />
    <Compile Include="WHClient\WHMsgHandle.cs" />
    <Compile Include="WHClient\WHUIHandle.cs" />
    <Compile Include="WHClient\WHHandInfo.cs" />
    <Page Include="Assist\KuweiShow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="LoginWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="UI\ButtonStyle.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="UI\TabControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\TabControl2.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\ucDigitalClock.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\ucHDRTile.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UserWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Config\log4net.config" />
    <None Include="Config\log4net.unix.config" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <AppDesigner Include="Properties\" />
    <None Include="supersocket.cmd" />
    <None Include="supersocket.sh" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Config\Config.xml">
      <SubType>Designer</SubType>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Image\map2.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Image\map3.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Image\map4.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="bitbug_favicon.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Image\timg.jpg" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Image\common.jpg" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>