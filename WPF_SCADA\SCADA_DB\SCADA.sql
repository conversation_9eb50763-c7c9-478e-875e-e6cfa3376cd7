USE [master]
GO

/****** Object:  Database [SCADA]    Script Date: 02/16/2022 16:50:41 ******/
CREATE DATABASE [SCADA] ON  PRIMARY 
( NAME = N'SCADA', FILENAME = N'c:\Program Files\Microsoft SQL Server\MSSQL10_50.SQLEXPRESS\MSSQL\DATA\SCADA.mdf' , SIZE = 2304KB , MAXSIZE = UNLIMITED, FILEGROWTH = 1024KB )
 LOG ON 
( NAME = N'SCADA_log', FILENAME = N'c:\Program Files\Microsoft SQL Server\MSSQL10_50.SQLEXPRESS\MSSQL\DATA\SCADA_1.LDF' , SIZE = 832KB , MAXSIZE = 2048GB , FILEGROWTH = 10%)
GO

ALTER DATABASE [SCADA] SET COMPATIBILITY_LEVEL = 100
GO

IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
EXEC [SCADA].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO

ALTER DATABASE [SCADA] SET ANSI_NULL_DEFAULT OFF 
GO

ALTER DATABASE [SCADA] SET ANSI_NULLS OFF 
GO

ALTER DATABASE [SCADA] SET ANSI_PADDING OFF 
GO

ALTER DATABASE [SCADA] SET ANSI_WARNINGS OFF 
GO

ALTER DATABASE [SCADA] SET ARITHABORT OFF 
GO

ALTER DATABASE [SCADA] SET AUTO_CLOSE ON 
GO

ALTER DATABASE [SCADA] SET AUTO_CREATE_STATISTICS ON 
GO

ALTER DATABASE [SCADA] SET AUTO_SHRINK OFF 
GO

ALTER DATABASE [SCADA] SET AUTO_UPDATE_STATISTICS ON 
GO

ALTER DATABASE [SCADA] SET CURSOR_CLOSE_ON_COMMIT OFF 
GO

ALTER DATABASE [SCADA] SET CURSOR_DEFAULT  GLOBAL 
GO

ALTER DATABASE [SCADA] SET CONCAT_NULL_YIELDS_NULL OFF 
GO

ALTER DATABASE [SCADA] SET NUMERIC_ROUNDABORT OFF 
GO

ALTER DATABASE [SCADA] SET QUOTED_IDENTIFIER OFF 
GO

ALTER DATABASE [SCADA] SET RECURSIVE_TRIGGERS OFF 
GO

ALTER DATABASE [SCADA] SET  DISABLE_BROKER 
GO

ALTER DATABASE [SCADA] SET AUTO_UPDATE_STATISTICS_ASYNC OFF 
GO

ALTER DATABASE [SCADA] SET DATE_CORRELATION_OPTIMIZATION OFF 
GO

ALTER DATABASE [SCADA] SET TRUSTWORTHY OFF 
GO

ALTER DATABASE [SCADA] SET ALLOW_SNAPSHOT_ISOLATION OFF 
GO

ALTER DATABASE [SCADA] SET PARAMETERIZATION SIMPLE 
GO

ALTER DATABASE [SCADA] SET READ_COMMITTED_SNAPSHOT OFF 
GO

ALTER DATABASE [SCADA] SET HONOR_BROKER_PRIORITY OFF 
GO

ALTER DATABASE [SCADA] SET  READ_WRITE 
GO

ALTER DATABASE [SCADA] SET RECOVERY SIMPLE 
GO

ALTER DATABASE [SCADA] SET  MULTI_USER 
GO

ALTER DATABASE [SCADA] SET PAGE_VERIFY CHECKSUM  
GO

ALTER DATABASE [SCADA] SET DB_CHAINING OFF 
GO

