﻿namespace TagConfig
{
    partial class AlarmParam
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpLimit = new System.Windows.Forms.GroupBox();
            this.cboLOLO = new System.Windows.Forms.ComboBox();
            this.cboLO = new System.Windows.Forms.ComboBox();
            this.cboHI = new System.Windows.Forms.ComboBox();
            this.cboHIHI = new System.Windows.Forms.ComboBox();
            this.txtDelayLim = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtDebLim = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txtMsgLOLO = new System.Windows.Forms.TextBox();
            this.txtThLOLO = new System.Windows.Forms.TextBox();
            this.txtMsgLO = new System.Windows.Forms.TextBox();
            this.txtThLO = new System.Windows.Forms.TextBox();
            this.txtMsgHI = new System.Windows.Forms.TextBox();
            this.txtThHI = new System.Windows.Forms.TextBox();
            this.txtMsgHIHI = new System.Windows.Forms.TextBox();
            this.txtThHIHI = new System.Windows.Forms.TextBox();
            this.chkLOLO = new System.Windows.Forms.CheckBox();
            this.chkLO = new System.Windows.Forms.CheckBox();
            this.chkHI = new System.Windows.Forms.CheckBox();
            this.chkHIHI = new System.Windows.Forms.CheckBox();
            this.grpDev = new System.Windows.Forms.GroupBox();
            this.rdValue = new System.Windows.Forms.RadioButton();
            this.rdPercent = new System.Windows.Forms.RadioButton();
            this.cboMinDev = new System.Windows.Forms.ComboBox();
            this.cboMajDev = new System.Windows.Forms.ComboBox();
            this.txtParaDev = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.txtDelayDev = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.txtDebDev = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.txtMsgMin = new System.Windows.Forms.TextBox();
            this.txtThMinDev = new System.Windows.Forms.TextBox();
            this.chkMinDev = new System.Windows.Forms.CheckBox();
            this.txtMsgMaj = new System.Windows.Forms.TextBox();
            this.txtThMajDev = new System.Windows.Forms.TextBox();
            this.chkMajDev = new System.Windows.Forms.CheckBox();
            this.grpRate = new System.Windows.Forms.GroupBox();
            this.cboLORt = new System.Windows.Forms.ComboBox();
            this.cboHIRt = new System.Windows.Forms.ComboBox();
            this.txtDelayRt = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.txtDebRt = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.txtMsgLORt = new System.Windows.Forms.TextBox();
            this.txtThLORt = new System.Windows.Forms.TextBox();
            this.chkLORt = new System.Windows.Forms.CheckBox();
            this.txtMsgHIRt = new System.Windows.Forms.TextBox();
            this.txtThHIRt = new System.Windows.Forms.TextBox();
            this.chkHIRt = new System.Windows.Forms.CheckBox();
            this.grpDig = new System.Windows.Forms.GroupBox();
            this.cboDig = new System.Windows.Forms.ComboBox();
            this.radioButton2 = new System.Windows.Forms.RadioButton();
            this.rdDig = new System.Windows.Forms.RadioButton();
            this.chkDig = new System.Windows.Forms.CheckBox();
            this.txtDelayDig = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.txtMsgDig = new System.Windows.Forms.TextBox();
            this.grpQua = new System.Windows.Forms.GroupBox();
            this.cboQua = new System.Windows.Forms.ComboBox();
            this.chkQua = new System.Windows.Forms.CheckBox();
            this.txtDelayQua = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.txtMsgQua = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.grpLimit.SuspendLayout();
            this.grpDev.SuspendLayout();
            this.grpRate.SuspendLayout();
            this.grpDig.SuspendLayout();
            this.grpQua.SuspendLayout();
            this.SuspendLayout();
            // 
            // grpLimit
            // 
            this.grpLimit.Controls.Add(this.cboLOLO);
            this.grpLimit.Controls.Add(this.cboLO);
            this.grpLimit.Controls.Add(this.cboHI);
            this.grpLimit.Controls.Add(this.cboHIHI);
            this.grpLimit.Controls.Add(this.txtDelayLim);
            this.grpLimit.Controls.Add(this.label2);
            this.grpLimit.Controls.Add(this.txtDebLim);
            this.grpLimit.Controls.Add(this.label1);
            this.grpLimit.Controls.Add(this.txtMsgLOLO);
            this.grpLimit.Controls.Add(this.txtThLOLO);
            this.grpLimit.Controls.Add(this.txtMsgLO);
            this.grpLimit.Controls.Add(this.txtThLO);
            this.grpLimit.Controls.Add(this.txtMsgHI);
            this.grpLimit.Controls.Add(this.txtThHI);
            this.grpLimit.Controls.Add(this.txtMsgHIHI);
            this.grpLimit.Controls.Add(this.txtThHIHI);
            this.grpLimit.Controls.Add(this.chkLOLO);
            this.grpLimit.Controls.Add(this.chkLO);
            this.grpLimit.Controls.Add(this.chkHI);
            this.grpLimit.Controls.Add(this.chkHIHI);
            this.grpLimit.Location = new System.Drawing.Point(8, 28);
            this.grpLimit.Name = "grpLimit";
            this.grpLimit.Size = new System.Drawing.Size(613, 154);
            this.grpLimit.TabIndex = 0;
            this.grpLimit.TabStop = false;
            this.grpLimit.Text = "差限报警";
            // 
            // cboLOLO
            // 
            this.cboLOLO.FormattingEnabled = true;
            this.cboLOLO.Location = new System.Drawing.Point(511, 120);
            this.cboLOLO.Name = "cboLOLO";
            this.cboLOLO.Size = new System.Drawing.Size(95, 21);
            this.cboLOLO.TabIndex = 23;
            // 
            // cboLO
            // 
            this.cboLO.FormattingEnabled = true;
            this.cboLO.Location = new System.Drawing.Point(511, 93);
            this.cboLO.Name = "cboLO";
            this.cboLO.Size = new System.Drawing.Size(95, 21);
            this.cboLO.TabIndex = 22;
            // 
            // cboHI
            // 
            this.cboHI.FormattingEnabled = true;
            this.cboHI.Location = new System.Drawing.Point(511, 67);
            this.cboHI.Name = "cboHI";
            this.cboHI.Size = new System.Drawing.Size(95, 21);
            this.cboHI.TabIndex = 21;
            // 
            // cboHIHI
            // 
            this.cboHIHI.FormattingEnabled = true;
            this.cboHIHI.Location = new System.Drawing.Point(511, 39);
            this.cboHIHI.Name = "cboHIHI";
            this.cboHIHI.Size = new System.Drawing.Size(95, 21);
            this.cboHIHI.TabIndex = 20;
            // 
            // txtDelayLim
            // 
            this.txtDelayLim.Location = new System.Drawing.Point(284, 13);
            this.txtDelayLim.Name = "txtDelayLim";
            this.txtDelayLim.Size = new System.Drawing.Size(93, 20);
            this.txtDelayLim.TabIndex = 19;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(243, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(43, 13);
            this.label2.TabIndex = 18;
            this.label2.Text = "延时：";
            // 
            // txtDebLim
            // 
            this.txtDebLim.Location = new System.Drawing.Point(133, 13);
            this.txtDebLim.Name = "txtDebLim";
            this.txtDebLim.Size = new System.Drawing.Size(93, 20);
            this.txtDebLim.TabIndex = 17;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(92, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(43, 13);
            this.label1.TabIndex = 16;
            this.label1.Text = "死区：";
            // 
            // txtMsgLOLO
            // 
            this.txtMsgLOLO.Location = new System.Drawing.Point(168, 119);
            this.txtMsgLOLO.Name = "txtMsgLOLO";
            this.txtMsgLOLO.Size = new System.Drawing.Size(337, 20);
            this.txtMsgLOLO.TabIndex = 14;
            // 
            // txtThLOLO
            // 
            this.txtThLOLO.Location = new System.Drawing.Point(62, 119);
            this.txtThLOLO.Name = "txtThLOLO";
            this.txtThLOLO.Size = new System.Drawing.Size(100, 20);
            this.txtThLOLO.TabIndex = 13;
            // 
            // txtMsgLO
            // 
            this.txtMsgLO.Location = new System.Drawing.Point(168, 93);
            this.txtMsgLO.Name = "txtMsgLO";
            this.txtMsgLO.Size = new System.Drawing.Size(337, 20);
            this.txtMsgLO.TabIndex = 11;
            // 
            // txtThLO
            // 
            this.txtThLO.Location = new System.Drawing.Point(62, 93);
            this.txtThLO.Name = "txtThLO";
            this.txtThLO.Size = new System.Drawing.Size(100, 20);
            this.txtThLO.TabIndex = 10;
            // 
            // txtMsgHI
            // 
            this.txtMsgHI.Location = new System.Drawing.Point(168, 67);
            this.txtMsgHI.Name = "txtMsgHI";
            this.txtMsgHI.Size = new System.Drawing.Size(337, 20);
            this.txtMsgHI.TabIndex = 8;
            // 
            // txtThHI
            // 
            this.txtThHI.Location = new System.Drawing.Point(62, 67);
            this.txtThHI.Name = "txtThHI";
            this.txtThHI.Size = new System.Drawing.Size(100, 20);
            this.txtThHI.TabIndex = 7;
            // 
            // txtMsgHIHI
            // 
            this.txtMsgHIHI.Location = new System.Drawing.Point(168, 41);
            this.txtMsgHIHI.Name = "txtMsgHIHI";
            this.txtMsgHIHI.Size = new System.Drawing.Size(337, 20);
            this.txtMsgHIHI.TabIndex = 5;
            // 
            // txtThHIHI
            // 
            this.txtThHIHI.Location = new System.Drawing.Point(62, 41);
            this.txtThHIHI.Name = "txtThHIHI";
            this.txtThHIHI.Size = new System.Drawing.Size(100, 20);
            this.txtThHIHI.TabIndex = 4;
            // 
            // chkLOLO
            // 
            this.chkLOLO.AutoSize = true;
            this.chkLOLO.Location = new System.Drawing.Point(6, 123);
            this.chkLOLO.Name = "chkLOLO";
            this.chkLOLO.Size = new System.Drawing.Size(50, 17);
            this.chkLOLO.TabIndex = 3;
            this.chkLOLO.Text = "低低";
            this.chkLOLO.UseVisualStyleBackColor = true;
            // 
            // chkLO
            // 
            this.chkLO.AutoSize = true;
            this.chkLO.Location = new System.Drawing.Point(6, 96);
            this.chkLO.Name = "chkLO";
            this.chkLO.Size = new System.Drawing.Size(38, 17);
            this.chkLO.TabIndex = 2;
            this.chkLO.Text = "低";
            this.chkLO.UseVisualStyleBackColor = true;
            // 
            // chkHI
            // 
            this.chkHI.AutoSize = true;
            this.chkHI.Location = new System.Drawing.Point(6, 67);
            this.chkHI.Name = "chkHI";
            this.chkHI.Size = new System.Drawing.Size(38, 17);
            this.chkHI.TabIndex = 1;
            this.chkHI.Text = "高";
            this.chkHI.UseVisualStyleBackColor = true;
            // 
            // chkHIHI
            // 
            this.chkHIHI.AutoSize = true;
            this.chkHIHI.Location = new System.Drawing.Point(6, 41);
            this.chkHIHI.Name = "chkHIHI";
            this.chkHIHI.Size = new System.Drawing.Size(50, 17);
            this.chkHIHI.TabIndex = 0;
            this.chkHIHI.Text = "高高";
            this.chkHIHI.UseVisualStyleBackColor = true;
            // 
            // grpDev
            // 
            this.grpDev.Controls.Add(this.rdValue);
            this.grpDev.Controls.Add(this.rdPercent);
            this.grpDev.Controls.Add(this.cboMinDev);
            this.grpDev.Controls.Add(this.cboMajDev);
            this.grpDev.Controls.Add(this.txtParaDev);
            this.grpDev.Controls.Add(this.label8);
            this.grpDev.Controls.Add(this.txtDelayDev);
            this.grpDev.Controls.Add(this.label3);
            this.grpDev.Controls.Add(this.txtDebDev);
            this.grpDev.Controls.Add(this.label4);
            this.grpDev.Controls.Add(this.txtMsgMin);
            this.grpDev.Controls.Add(this.txtThMinDev);
            this.grpDev.Controls.Add(this.chkMinDev);
            this.grpDev.Controls.Add(this.txtMsgMaj);
            this.grpDev.Controls.Add(this.txtThMajDev);
            this.grpDev.Controls.Add(this.chkMajDev);
            this.grpDev.Location = new System.Drawing.Point(8, 184);
            this.grpDev.Name = "grpDev";
            this.grpDev.Size = new System.Drawing.Size(613, 99);
            this.grpDev.TabIndex = 1;
            this.grpDev.TabStop = false;
            this.grpDev.Text = "偏差报警";
            // 
            // rdValue
            // 
            this.rdValue.AutoSize = true;
            this.rdValue.Checked = true;
            this.rdValue.Location = new System.Drawing.Point(514, 9);
            this.rdValue.Name = "rdValue";
            this.rdValue.Size = new System.Drawing.Size(61, 17);
            this.rdValue.TabIndex = 30;
            this.rdValue.TabStop = true;
            this.rdValue.Text = "绝对值";
            this.rdValue.UseVisualStyleBackColor = false;
            // 
            // rdPercent
            // 
            this.rdPercent.AutoSize = true;
            this.rdPercent.Location = new System.Drawing.Point(515, 23);
            this.rdPercent.Name = "rdPercent";
            this.rdPercent.Size = new System.Drawing.Size(61, 17);
            this.rdPercent.TabIndex = 29;
            this.rdPercent.Text = "百分比";
            this.rdPercent.UseVisualStyleBackColor = true;
            // 
            // cboMinDev
            // 
            this.cboMinDev.FormattingEnabled = true;
            this.cboMinDev.Location = new System.Drawing.Point(512, 68);
            this.cboMinDev.Name = "cboMinDev";
            this.cboMinDev.Size = new System.Drawing.Size(95, 21);
            this.cboMinDev.TabIndex = 27;
            // 
            // cboMajDev
            // 
            this.cboMajDev.FormattingEnabled = true;
            this.cboMajDev.Location = new System.Drawing.Point(511, 42);
            this.cboMajDev.Name = "cboMajDev";
            this.cboMajDev.Size = new System.Drawing.Size(95, 21);
            this.cboMajDev.TabIndex = 26;
            // 
            // txtParaDev
            // 
            this.txtParaDev.Location = new System.Drawing.Point(438, 16);
            this.txtParaDev.Name = "txtParaDev";
            this.txtParaDev.Size = new System.Drawing.Size(67, 20);
            this.txtParaDev.TabIndex = 25;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(397, 19);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(43, 13);
            this.label8.TabIndex = 24;
            this.label8.Text = "参数：";
            // 
            // txtDelayDev
            // 
            this.txtDelayDev.Location = new System.Drawing.Point(284, 16);
            this.txtDelayDev.Name = "txtDelayDev";
            this.txtDelayDev.Size = new System.Drawing.Size(93, 20);
            this.txtDelayDev.TabIndex = 23;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(243, 19);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(43, 13);
            this.label3.TabIndex = 22;
            this.label3.Text = "延时：";
            // 
            // txtDebDev
            // 
            this.txtDebDev.Location = new System.Drawing.Point(133, 16);
            this.txtDebDev.Name = "txtDebDev";
            this.txtDebDev.Size = new System.Drawing.Size(93, 20);
            this.txtDebDev.TabIndex = 21;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(92, 19);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 13);
            this.label4.TabIndex = 20;
            this.label4.Text = "死区：";
            // 
            // txtMsgMin
            // 
            this.txtMsgMin.Location = new System.Drawing.Point(168, 68);
            this.txtMsgMin.Name = "txtMsgMin";
            this.txtMsgMin.Size = new System.Drawing.Size(337, 20);
            this.txtMsgMin.TabIndex = 13;
            // 
            // txtThMinDev
            // 
            this.txtThMinDev.Location = new System.Drawing.Point(62, 68);
            this.txtThMinDev.Name = "txtThMinDev";
            this.txtThMinDev.Size = new System.Drawing.Size(100, 20);
            this.txtThMinDev.TabIndex = 12;
            // 
            // chkMinDev
            // 
            this.chkMinDev.AutoSize = true;
            this.chkMinDev.Location = new System.Drawing.Point(6, 68);
            this.chkMinDev.Name = "chkMinDev";
            this.chkMinDev.Size = new System.Drawing.Size(62, 17);
            this.chkMinDev.TabIndex = 11;
            this.chkMinDev.Text = "下偏差";
            this.chkMinDev.UseVisualStyleBackColor = true;
            // 
            // txtMsgMaj
            // 
            this.txtMsgMaj.Location = new System.Drawing.Point(168, 42);
            this.txtMsgMaj.Name = "txtMsgMaj";
            this.txtMsgMaj.Size = new System.Drawing.Size(337, 20);
            this.txtMsgMaj.TabIndex = 9;
            // 
            // txtThMajDev
            // 
            this.txtThMajDev.Location = new System.Drawing.Point(62, 42);
            this.txtThMajDev.Name = "txtThMajDev";
            this.txtThMajDev.Size = new System.Drawing.Size(100, 20);
            this.txtThMajDev.TabIndex = 8;
            // 
            // chkMajDev
            // 
            this.chkMajDev.AutoSize = true;
            this.chkMajDev.Location = new System.Drawing.Point(6, 42);
            this.chkMajDev.Name = "chkMajDev";
            this.chkMajDev.Size = new System.Drawing.Size(62, 17);
            this.chkMajDev.TabIndex = 7;
            this.chkMajDev.Text = "上偏差";
            this.chkMajDev.UseVisualStyleBackColor = true;
            // 
            // grpRate
            // 
            this.grpRate.Controls.Add(this.cboLORt);
            this.grpRate.Controls.Add(this.cboHIRt);
            this.grpRate.Controls.Add(this.txtDelayRt);
            this.grpRate.Controls.Add(this.label5);
            this.grpRate.Controls.Add(this.txtDebRt);
            this.grpRate.Controls.Add(this.label6);
            this.grpRate.Controls.Add(this.txtMsgLORt);
            this.grpRate.Controls.Add(this.txtThLORt);
            this.grpRate.Controls.Add(this.chkLORt);
            this.grpRate.Controls.Add(this.txtMsgHIRt);
            this.grpRate.Controls.Add(this.txtThHIRt);
            this.grpRate.Controls.Add(this.chkHIRt);
            this.grpRate.Location = new System.Drawing.Point(8, 285);
            this.grpRate.Name = "grpRate";
            this.grpRate.Size = new System.Drawing.Size(613, 97);
            this.grpRate.TabIndex = 2;
            this.grpRate.TabStop = false;
            this.grpRate.Text = "变化率报警";
            // 
            // cboLORt
            // 
            this.cboLORt.FormattingEnabled = true;
            this.cboLORt.Location = new System.Drawing.Point(511, 67);
            this.cboLORt.Name = "cboLORt";
            this.cboLORt.Size = new System.Drawing.Size(95, 21);
            this.cboLORt.TabIndex = 25;
            // 
            // cboHIRt
            // 
            this.cboHIRt.FormattingEnabled = true;
            this.cboHIRt.Location = new System.Drawing.Point(512, 41);
            this.cboHIRt.Name = "cboHIRt";
            this.cboHIRt.Size = new System.Drawing.Size(95, 21);
            this.cboHIRt.TabIndex = 24;
            // 
            // txtDelayRt
            // 
            this.txtDelayRt.Location = new System.Drawing.Point(284, 15);
            this.txtDelayRt.Name = "txtDelayRt";
            this.txtDelayRt.Size = new System.Drawing.Size(93, 20);
            this.txtDelayRt.TabIndex = 23;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(243, 18);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(43, 13);
            this.label5.TabIndex = 22;
            this.label5.Text = "延时：";
            // 
            // txtDebRt
            // 
            this.txtDebRt.Location = new System.Drawing.Point(133, 15);
            this.txtDebRt.Name = "txtDebRt";
            this.txtDebRt.Size = new System.Drawing.Size(93, 20);
            this.txtDebRt.TabIndex = 21;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(92, 18);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(43, 13);
            this.label6.TabIndex = 20;
            this.label6.Text = "死区：";
            // 
            // txtMsgLORt
            // 
            this.txtMsgLORt.Location = new System.Drawing.Point(168, 67);
            this.txtMsgLORt.Name = "txtMsgLORt";
            this.txtMsgLORt.Size = new System.Drawing.Size(337, 20);
            this.txtMsgLORt.TabIndex = 13;
            // 
            // txtThLORt
            // 
            this.txtThLORt.Location = new System.Drawing.Point(62, 67);
            this.txtThLORt.Name = "txtThLORt";
            this.txtThLORt.Size = new System.Drawing.Size(100, 20);
            this.txtThLORt.TabIndex = 12;
            // 
            // chkLORt
            // 
            this.chkLORt.AutoSize = true;
            this.chkLORt.Location = new System.Drawing.Point(6, 67);
            this.chkLORt.Name = "chkLORt";
            this.chkLORt.Size = new System.Drawing.Size(38, 17);
            this.chkLORt.TabIndex = 11;
            this.chkLORt.Text = "低";
            this.chkLORt.UseVisualStyleBackColor = true;
            // 
            // txtMsgHIRt
            // 
            this.txtMsgHIRt.Location = new System.Drawing.Point(168, 41);
            this.txtMsgHIRt.Name = "txtMsgHIRt";
            this.txtMsgHIRt.Size = new System.Drawing.Size(337, 20);
            this.txtMsgHIRt.TabIndex = 9;
            // 
            // txtThHIRt
            // 
            this.txtThHIRt.Location = new System.Drawing.Point(62, 41);
            this.txtThHIRt.Name = "txtThHIRt";
            this.txtThHIRt.Size = new System.Drawing.Size(100, 20);
            this.txtThHIRt.TabIndex = 8;
            // 
            // chkHIRt
            // 
            this.chkHIRt.AutoSize = true;
            this.chkHIRt.Location = new System.Drawing.Point(6, 41);
            this.chkHIRt.Name = "chkHIRt";
            this.chkHIRt.Size = new System.Drawing.Size(38, 17);
            this.chkHIRt.TabIndex = 7;
            this.chkHIRt.Text = "高";
            this.chkHIRt.UseVisualStyleBackColor = true;
            // 
            // grpDig
            // 
            this.grpDig.Controls.Add(this.cboDig);
            this.grpDig.Controls.Add(this.radioButton2);
            this.grpDig.Controls.Add(this.rdDig);
            this.grpDig.Controls.Add(this.chkDig);
            this.grpDig.Controls.Add(this.txtDelayDig);
            this.grpDig.Controls.Add(this.label7);
            this.grpDig.Controls.Add(this.txtMsgDig);
            this.grpDig.Location = new System.Drawing.Point(8, 388);
            this.grpDig.Name = "grpDig";
            this.grpDig.Size = new System.Drawing.Size(613, 68);
            this.grpDig.TabIndex = 3;
            this.grpDig.TabStop = false;
            this.grpDig.Text = "开关量报警";
            // 
            // cboDig
            // 
            this.cboDig.FormattingEnabled = true;
            this.cboDig.Location = new System.Drawing.Point(511, 37);
            this.cboDig.Name = "cboDig";
            this.cboDig.Size = new System.Drawing.Size(95, 21);
            this.cboDig.TabIndex = 29;
            // 
            // radioButton2
            // 
            this.radioButton2.AutoSize = true;
            this.radioButton2.Location = new System.Drawing.Point(77, 12);
            this.radioButton2.Name = "radioButton2";
            this.radioButton2.Size = new System.Drawing.Size(61, 17);
            this.radioButton2.TabIndex = 28;
            this.radioButton2.Text = "负触发";
            this.radioButton2.UseVisualStyleBackColor = false;
            // 
            // rdDig
            // 
            this.rdDig.AutoSize = true;
            this.rdDig.Checked = true;
            this.rdDig.Location = new System.Drawing.Point(77, 37);
            this.rdDig.Name = "rdDig";
            this.rdDig.Size = new System.Drawing.Size(61, 17);
            this.rdDig.TabIndex = 27;
            this.rdDig.TabStop = true;
            this.rdDig.Text = "正触发";
            this.rdDig.UseVisualStyleBackColor = true;
            // 
            // chkDig
            // 
            this.chkDig.AutoSize = true;
            this.chkDig.Location = new System.Drawing.Point(9, 38);
            this.chkDig.Name = "chkDig";
            this.chkDig.Size = new System.Drawing.Size(62, 17);
            this.chkDig.TabIndex = 26;
            this.chkDig.Text = "开关量";
            this.chkDig.UseVisualStyleBackColor = true;
            // 
            // txtDelayDig
            // 
            this.txtDelayDig.Location = new System.Drawing.Point(284, 13);
            this.txtDelayDig.Name = "txtDelayDig";
            this.txtDelayDig.Size = new System.Drawing.Size(93, 20);
            this.txtDelayDig.TabIndex = 24;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(243, 16);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(43, 13);
            this.label7.TabIndex = 22;
            this.label7.Text = "延时：";
            // 
            // txtMsgDig
            // 
            this.txtMsgDig.Location = new System.Drawing.Point(168, 38);
            this.txtMsgDig.Name = "txtMsgDig";
            this.txtMsgDig.Size = new System.Drawing.Size(337, 20);
            this.txtMsgDig.TabIndex = 9;
            // 
            // grpQua
            // 
            this.grpQua.Controls.Add(this.cboQua);
            this.grpQua.Controls.Add(this.chkQua);
            this.grpQua.Controls.Add(this.txtDelayQua);
            this.grpQua.Controls.Add(this.label10);
            this.grpQua.Controls.Add(this.txtMsgQua);
            this.grpQua.Location = new System.Drawing.Point(8, 462);
            this.grpQua.Name = "grpQua";
            this.grpQua.Size = new System.Drawing.Size(613, 74);
            this.grpQua.TabIndex = 4;
            this.grpQua.TabStop = false;
            this.grpQua.Text = "质量戳报警";
            // 
            // cboQua
            // 
            this.cboQua.FormattingEnabled = true;
            this.cboQua.Location = new System.Drawing.Point(511, 44);
            this.cboQua.Name = "cboQua";
            this.cboQua.Size = new System.Drawing.Size(95, 21);
            this.cboQua.TabIndex = 26;
            // 
            // chkQua
            // 
            this.chkQua.AutoSize = true;
            this.chkQua.Location = new System.Drawing.Point(9, 46);
            this.chkQua.Name = "chkQua";
            this.chkQua.Size = new System.Drawing.Size(50, 17);
            this.chkQua.TabIndex = 25;
            this.chkQua.Text = "质量";
            this.chkQua.UseVisualStyleBackColor = true;
            // 
            // txtDelayQua
            // 
            this.txtDelayQua.Location = new System.Drawing.Point(284, 19);
            this.txtDelayQua.Name = "txtDelayQua";
            this.txtDelayQua.Size = new System.Drawing.Size(93, 20);
            this.txtDelayQua.TabIndex = 24;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(243, 23);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(43, 13);
            this.label10.TabIndex = 20;
            this.label10.Text = "延时：";
            // 
            // txtMsgQua
            // 
            this.txtMsgQua.Location = new System.Drawing.Point(168, 46);
            this.txtMsgQua.Name = "txtMsgQua";
            this.txtMsgQua.Size = new System.Drawing.Size(337, 20);
            this.txtMsgQua.TabIndex = 9;
            // 
            // label11
            // 
            this.label11.BackColor = System.Drawing.SystemColors.ActiveCaption;
            this.label11.Location = new System.Drawing.Point(70, 8);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(100, 13);
            this.label11.TabIndex = 19;
            this.label11.Text = "阈值";
            this.label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.BackColor = System.Drawing.SystemColors.ActiveCaption;
            this.label12.Location = new System.Drawing.Point(12, 8);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(55, 13);
            this.label12.TabIndex = 20;
            this.label12.Text = "报警类型";
            // 
            // label13
            // 
            this.label13.BackColor = System.Drawing.SystemColors.ActiveCaption;
            this.label13.Location = new System.Drawing.Point(176, 8);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(337, 13);
            this.label13.TabIndex = 21;
            this.label13.Text = "消息文本";
            this.label13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // label14
            // 
            this.label14.BackColor = System.Drawing.SystemColors.ActiveCaption;
            this.label14.Location = new System.Drawing.Point(519, 8);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(95, 13);
            this.label14.TabIndex = 22;
            this.label14.Text = "优先级";
            // 
            // AlarmParam
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(626, 541);
            this.Controls.Add(this.label14);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.grpQua);
            this.Controls.Add(this.grpDig);
            this.Controls.Add(this.grpRate);
            this.Controls.Add(this.grpDev);
            this.Controls.Add(this.grpLimit);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AlarmParam";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "报警设置";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.AlarmParam_FormClosing);
            this.Load += new System.EventHandler(this.AlarmParam_Load);
            this.grpLimit.ResumeLayout(false);
            this.grpLimit.PerformLayout();
            this.grpDev.ResumeLayout(false);
            this.grpDev.PerformLayout();
            this.grpRate.ResumeLayout(false);
            this.grpRate.PerformLayout();
            this.grpDig.ResumeLayout(false);
            this.grpDig.PerformLayout();
            this.grpQua.ResumeLayout(false);
            this.grpQua.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox grpLimit;
        private System.Windows.Forms.GroupBox grpDev;
        private System.Windows.Forms.GroupBox grpRate;
        private System.Windows.Forms.GroupBox grpDig;
        private System.Windows.Forms.GroupBox grpQua;
        private System.Windows.Forms.TextBox txtMsgLOLO;
        private System.Windows.Forms.TextBox txtThLOLO;
        private System.Windows.Forms.TextBox txtMsgLO;
        private System.Windows.Forms.TextBox txtThLO;
        private System.Windows.Forms.TextBox txtMsgHI;
        private System.Windows.Forms.TextBox txtThHI;
        private System.Windows.Forms.TextBox txtMsgHIHI;
        private System.Windows.Forms.TextBox txtThHIHI;
        private System.Windows.Forms.CheckBox chkLOLO;
        private System.Windows.Forms.CheckBox chkLO;
        private System.Windows.Forms.CheckBox chkHI;
        private System.Windows.Forms.CheckBox chkHIHI;
        private System.Windows.Forms.TextBox txtMsgMin;
        private System.Windows.Forms.TextBox txtThMinDev;
        private System.Windows.Forms.CheckBox chkMinDev;
        private System.Windows.Forms.TextBox txtMsgMaj;
        private System.Windows.Forms.TextBox txtThMajDev;
        private System.Windows.Forms.CheckBox chkMajDev;
        private System.Windows.Forms.TextBox txtMsgLORt;
        private System.Windows.Forms.TextBox txtThLORt;
        private System.Windows.Forms.CheckBox chkLORt;
        private System.Windows.Forms.TextBox txtMsgHIRt;
        private System.Windows.Forms.TextBox txtThHIRt;
        private System.Windows.Forms.CheckBox chkHIRt;
        private System.Windows.Forms.TextBox txtMsgDig;
        private System.Windows.Forms.TextBox txtMsgQua;
        private System.Windows.Forms.TextBox txtDelayLim;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtDebLim;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtDelayDev;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox txtDebDev;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtDelayRt;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtDebRt;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.RadioButton rdDig;
        private System.Windows.Forms.CheckBox chkDig;
        private System.Windows.Forms.TextBox txtDelayDig;
        private System.Windows.Forms.CheckBox chkQua;
        private System.Windows.Forms.TextBox txtDelayQua;
        private System.Windows.Forms.TextBox txtParaDev;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.RadioButton radioButton2;
        private System.Windows.Forms.ComboBox cboLOLO;
        private System.Windows.Forms.ComboBox cboLO;
        private System.Windows.Forms.ComboBox cboHI;
        private System.Windows.Forms.ComboBox cboHIHI;
        private System.Windows.Forms.ComboBox cboMinDev;
        private System.Windows.Forms.ComboBox cboMajDev;
        private System.Windows.Forms.ComboBox cboLORt;
        private System.Windows.Forms.ComboBox cboHIRt;
        private System.Windows.Forms.ComboBox cboDig;
        private System.Windows.Forms.ComboBox cboQua;
        private System.Windows.Forms.RadioButton rdValue;
        private System.Windows.Forms.RadioButton rdPercent;
    }
}