﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7B353BC5-028F-40D4-84D7-3D3344B6FB94}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HMIControl.VisualStudio.Design</RootNamespace>
    <AssemblyName>HMIControl.VisualStudio.Design</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\HMIControl\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\HMIControl\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Windows.Design.Extensibility, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\dll\Microsoft.Windows.Design.Extensibility.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Windows.Design.Interaction, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\dll\Microsoft.Windows.Design.Interaction.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ControlAdornerProvider.cs" />
    <Compile Include="metadata.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="TagComplexEditor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TagComplexEditor.Designer.cs">
      <DependentUpon>TagComplexEditor.cs</DependentUpon>
    </Compile>
    <Compile Include="TagData.cs" />
    <Compile Include="TagList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TagList.Designer.cs">
      <DependentUpon>TagList.cs</DependentUpon>
    </Compile>
    <Compile Include="TagWindowEditor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TagWindowEditor.Designer.cs">
      <DependentUpon>TagWindowEditor.cs</DependentUpon>
    </Compile>
    <Compile Include="TagWriteEditor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TagWriteEditor.Designer.cs">
      <DependentUpon>TagWriteEditor.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="TagComplexEditor.resx">
      <DependentUpon>TagComplexEditor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TagList.resx">
      <DependentUpon>TagList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TagWindowEditor.resx">
      <DependentUpon>TagWindowEditor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TagWriteEditor.resx">
      <DependentUpon>TagWriteEditor.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DataHelper\DataHelper.csproj">
      <Project>{755c5459-bca4-4729-a93c-0c73a41bdf3c}</Project>
      <Name>DataHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\HMIControl\HMIControl.csproj">
      <Project>{F9F0BFA7-0C4A-4C8C-B81C-2A0477CBF637}</Project>
      <Name>HMIControl</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>