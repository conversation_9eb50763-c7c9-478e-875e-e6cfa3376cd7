﻿<Window x:Class="MMIS.KuweiShow"
        xmlns:local="clr-namespace:MMIS"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="KuweiShow" Height="800" Width="550" Loaded="Window_Loaded" WindowStartupLocation="CenterScreen">
    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#330033" Offset="0"/>
            <GradientStop Color="#330033" Offset="0.93"/>
        </LinearGradientBrush>
    </Window.Background>
    <Window.Resources>
        <local:WHPositionConvert x:Key="PosConvert"/>
        <local:WHPositionTOColor x:Key="PosToColor"/>
    </Window.Resources>
    <Grid>
        <TextBox x:Name="Position_34" Text="{Binding POS_34, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="229,14,0,0"  TextWrapping="Wrap" Background="{Binding POS_34, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_35" Text="{Binding POS_35, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="269,14,0,0" TextWrapping="Wrap" Background="{Binding POS_35, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_36" Text="{Binding POS_36, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="309,14,0,0" TextWrapping="Wrap" Background="{Binding POS_36, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_37" Text="{Binding POS_37, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="349,14,0,0" TextWrapping="Wrap" Background="{Binding POS_37, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_38" Text="{Binding POS_38, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="389,14,0,0" TextWrapping="Wrap" Background="{Binding POS_38, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_39" Text="{Binding POS_39, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="429,14,0,0" TextWrapping="Wrap" Background="{Binding POS_39, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_40" Text="{Binding POS_40, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="469,14,0,0" TextWrapping="Wrap" Background="{Binding POS_40, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_27" Text="{Binding POS_27, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="229,73,0,0" TextWrapping="Wrap" Background="{Binding POS_27, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_28" Text="{Binding POS_28, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="269,73,0,0" TextWrapping="Wrap" Background="{Binding POS_28, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_29" Text="{Binding POS_29, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="309,73,0,0" TextWrapping="Wrap" Background="{Binding POS_29, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_30" Text="{Binding POS_30, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="349,73,0,0" TextWrapping="Wrap" Background="{Binding POS_30, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_31" Text="{Binding POS_31, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="389,73,0,0" TextWrapping="Wrap" Background="{Binding POS_31, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_32" Text="{Binding POS_32, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="429,73,0,0" TextWrapping="Wrap" Background="{Binding POS_32, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_33" Text="{Binding POS_33, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="469,73,0,0" TextWrapping="Wrap" Background="{Binding POS_33, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_20" Text="{Binding POS_20, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="229,134,0,0"  TextWrapping="Wrap" Background="{Binding POS_20, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_21" Text="{Binding POS_21, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="269,134,0,0" TextWrapping="Wrap" Background="{Binding POS_21, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_22" Text="{Binding POS_22, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="309,134,0,0" TextWrapping="Wrap" Background="{Binding POS_22, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_23" Text="{Binding POS_23, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="349,134,0,0" TextWrapping="Wrap" Background="{Binding POS_23, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_24" Text="{Binding POS_24, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="389,134,0,0" TextWrapping="Wrap" Background="{Binding POS_24, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_25" Text="{Binding POS_25, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="429,134,0,0" TextWrapping="Wrap" Background="{Binding POS_25, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_26" Text="{Binding POS_26, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="469,134,0,0" TextWrapping="Wrap" Background="{Binding POS_26, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_13" Text="{Binding POS_13, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="229,193,0,0"  TextWrapping="Wrap" Background="{Binding POS_13, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_14" Text="{Binding POS_14, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="269,193,0,0" TextWrapping="Wrap" Background="{Binding POS_14, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_15" Text="{Binding POS_15, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="309,193,0,0" TextWrapping="Wrap" Background="{Binding POS_15, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_16" Text="{Binding POS_16, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="349,193,0,0" TextWrapping="Wrap" Background="{Binding POS_16, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_17" Text="{Binding POS_17, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="389,193,0,0" TextWrapping="Wrap" Background="{Binding POS_17, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_18" Text="{Binding POS_18, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="429,193,0,0" TextWrapping="Wrap" Background="{Binding POS_18, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_19" Text="{Binding POS_19, Converter={StaticResource PosConvert}, Mode=OneWay}" HorizontalAlignment="Left" Margin="469,193,0,0" TextWrapping="Wrap" Background="{Binding POS_19, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_7" Text="{Binding POS_7, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="268,254,0,0" TextWrapping="Wrap" Background="{Binding POS_7, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_8" Text="{Binding POS_8, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="309,254,0,0" TextWrapping="Wrap" Background="{Binding POS_8, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_9" Text="{Binding POS_9, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="349,254,0,0" TextWrapping="Wrap" Background="{Binding POS_9, Converter={StaticResource PosToColor}, Mode=OneWay}"  Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_10" Text="{Binding POS_10, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="389,254,0,0" TextWrapping="Wrap" Background="{Binding POS_10, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_11" Text="{Binding POS_11, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="429,254,0,0" TextWrapping="Wrap" Background="{Binding POS_11, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_12" Text="{Binding POS_12, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="469,254,0,0" TextWrapping="Wrap" Background="{Binding POS_12, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_1" Text="{Binding POS_1, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="269,313,0,0" TextWrapping="Wrap" Background="{Binding POS_1, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_2" Text="{Binding POS_2, Converter={StaticResource PosConvert},Mode=TwoWay}" HorizontalAlignment="Left" Margin="309,313,0,0" TextWrapping="Wrap" Background="{Binding POS_2, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_3" Text="{Binding POS_3, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="349,313,0,0" TextWrapping="Wrap" Background="{Binding POS_3, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_4" Text="{Binding POS_4, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="389,313,0,0" TextWrapping="Wrap" Background="{Binding POS_4, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_5" Text="{Binding POS_5, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="429,313,0,0" TextWrapping="Wrap" Background="{Binding POS_5, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_6" Text="{Binding POS_6, Converter={StaticResource PosConvert}, Mode=TwoWay}" HorizontalAlignment="Left" Margin="469,313,0,0" TextWrapping="Wrap" Background="{Binding POS_6, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" VerticalAlignment="Top" Width="25" Height="25"/>
        <Label Content="第一货架:" Foreground="White" FontSize="24" HorizontalAlignment="Left" Height="40" Margin="32,16,0,0" VerticalAlignment="Top" Width="112" />
        <Label Content="第二货架:" Foreground="White" FontSize="24" HorizontalAlignment="Left" Height="40" Margin="22,397,0,0" VerticalAlignment="Top" Width="112" />
        <Button x:Name="btn_Save" Style="{StaticResource ButtonStyle1}" Content="修改完成" VerticalContentAlignment="Center" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="32" Margin="74,720,0,0" VerticalAlignment="Top" Width="70" Click="btn_Save_Click"/>
        <Label Content="34" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="35" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="36" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="37" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="38" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="39" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="40" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,44,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="27" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="28" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="29" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="30" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="31" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="32" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="33" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,103,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="20" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="21" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="22" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="23" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="24" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="25" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="26" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,163,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="13" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="14" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="15" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="16" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="17" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="18" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="19" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,223,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="7" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,284,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="8" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,284,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="9" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,284,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="10" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,284,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="11" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,284,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="12" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,284,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="1" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,343,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="2" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,343,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="3" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,343,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="4" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,343,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="5" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,343,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="6" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,343,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <TextBox x:Name="Position_74" Text="{Binding POS_74, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_74, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="228,392,0,0"  TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_75" Text="{Binding POS_75, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_75, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="268,392,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_76" Text="{Binding POS_76, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_76, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="308,392,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_77" Text="{Binding POS_77, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_77, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="348,392,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_78" Text="{Binding POS_78, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_78, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="388,392,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_79" Text="{Binding POS_79, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_79, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="428,392,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_80" Text="{Binding POS_80, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_80, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="468,392,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_67" Text="{Binding POS_67, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_67, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="228,452,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_68" Text="{Binding POS_68, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_68, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="268,452,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_69" Text="{Binding POS_69, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_69, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="308,452,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_70" Text="{Binding POS_70, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_70, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="348,452,0,0" TextWrapping="Wrap" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_71" Text="{Binding POS_71, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_71, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="388,452,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_72" Text="{Binding POS_72, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_72, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="428,452,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_73" Text="{Binding POS_73, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_73, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="468,452,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_60" Text="{Binding POS_60, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_60, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="228,512,0,0"  TextWrapping="Wrap" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_61" Text="{Binding POS_61, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_61, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="268,512,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_62" Text="{Binding POS_62, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_62, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="308,512,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_63" Text="{Binding POS_63, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_63, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="348,512,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_64" Text="{Binding POS_64, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_64, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="388,512,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_65" Text="{Binding POS_65, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_65, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="428,512,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_66" Text="{Binding POS_66, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_66, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="468,512,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_53" Text="{Binding POS_53, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_53, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="228,572,0,0"  TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_54" Text="{Binding POS_54, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_54, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="268,572,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_55" Text="{Binding POS_55, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_55, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="308,572,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_56" Text="{Binding POS_56, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_56, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="348,572,0,0" TextWrapping="Wrap" VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_57" Text="{Binding POS_57, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_57, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="388,572,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_58" Text="{Binding POS_58, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_58, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="428,572,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_59" Text="{Binding POS_59, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_59, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="468,572,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_47" Text="{Binding POS_47, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_47, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="268,632,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_48" Text="{Binding POS_48, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_48, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="308,632,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_49" Text="{Binding POS_49, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_49, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="348,632,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_50" Text="{Binding POS_50, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_50, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="388,632,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_51" Text="{Binding POS_51, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_51, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="428,632,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_52" Text="{Binding POS_52, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_52, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="468,632,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_41" Text="{Binding POS_41, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_41, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="268,692,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_42" Text="{Binding POS_42, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_42, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="308,692,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_43" Text="{Binding POS_43, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_43, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="348,692,0,0" TextWrapping="Wrap"  VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_44" Text="{Binding POS_44, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_44, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="388,692,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_45" Text="{Binding POS_45, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_45, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="428,692,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <TextBox x:Name="Position_46" Text="{Binding POS_46, Converter={StaticResource PosConvert}, Mode=OneWay}" Background="{Binding POS_46, Converter={StaticResource PosToColor}, Mode=OneWay}" Foreground="White" HorizontalAlignment="Left" Margin="468,692,0,0" TextWrapping="Wrap"   VerticalAlignment="Top" Width="25" Height="25"/>
        <Label Content="74" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="75" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="76" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="77" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="78" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="79" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="80" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,424,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="67" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="68" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="69" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="70" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="71" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="72" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="73" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,483,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="60" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="61" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="62" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="63" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="64" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="65" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="66" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,543,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="53" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="229,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="54" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="55" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="56" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="57" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="58" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="59" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,603,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="47" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,664,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="48" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,664,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="49" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,664,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="50" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,664,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="51" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,664,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="52" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,664,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="41" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="269,723,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="42" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="309,723,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="43" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="349,723,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="44" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="389,723,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="45" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="429,723,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="46" HorizontalContentAlignment="Center" HorizontalAlignment="Left" Height="25" Margin="469,723,0,0" VerticalAlignment="Top" Width="25" Foreground="White"/>
        <Label Content="修改库位:" Foreground="White" HorizontalAlignment="Left" Margin="2,618,0,0" VerticalAlignment="Top" Width="62"/>
        <TextBox x:Name="xiugai_position" HorizontalContentAlignment="Center" Foreground="White" Background="Transparent" HorizontalAlignment="Left" Height="26" Margin="72,618,0,0" TextWrapping="Wrap" Text=" " VerticalAlignment="Top" Width="135"/>
        <Label Content="修改托盘:" Foreground="White" HorizontalAlignment="Left" Margin="2,658,0,0" VerticalAlignment="Top" Width="62"/>
        <ComboBox x:Name="xiugai_traystyle" SelectedIndex="2" FontFamily="FZLTZHUNHK" FontSize="18" Foreground="White" BorderBrush="#FF565656" Padding="65,0,0,0" IsEditable="False" Style="{DynamicResource ComboBoxStyle}" Margin="72,654,337,85.4" RenderTransformOrigin="0.139,0.837">
            <ComboBoxItem Content="空库位"/>
            <ComboBoxItem Content="加工空托盘A0"/>
            <ComboBoxItem Content="加工毛坯托盘A1"/>
            <ComboBoxItem Content="加工成品托盘A2"/>
            <ComboBoxItem Content="检测完成托盘A3"/>
            <ComboBoxItem Content="轴承压装毛坯托盘B1"/>
            <ComboBoxItem Content="轴承压装成品托盘B2"/>
            <ComboBoxItem Content="轴承压装空托盘B0"/>
            <ComboBoxItem Content="拧螺钉空托盘C0"/>
            <ComboBoxItem Content="拧螺钉毛坯托盘C1"/>
            <ComboBoxItem Content="拧螺钉完成托盘C2"/>
            <ComboBoxItem Content="轴承托盘D"/>
            <ComboBoxItem Content="螺钉托盘E"/>
        </ComboBox>
    </Grid>
</Window>
