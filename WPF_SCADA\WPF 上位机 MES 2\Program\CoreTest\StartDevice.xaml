﻿<Window x:Class="CoreTest.StartDevice"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="设备启停" WindowStyle="None" Background="Black" ResizeMode="NoResize" ShowInTaskbar="False" Topmost="True">
    <Window.Resources>

    </Window.Resources>
    <Grid Name="grd" Background="#FFD8D8D8">
        <Grid.RowDefinitions>
            <RowDefinition Height="30" />
            <RowDefinition Height="30" />
            <RowDefinition Height="10" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="80"/>
            <ColumnDefinition Width="80"/>
            <ColumnDefinition Width="80"/>
        </Grid.ColumnDefinitions>

        <Border BorderBrush="#FF005DC5" BorderThickness="2" Grid.ColumnSpan="3" Grid.RowSpan="3" Height="70" HorizontalAlignment="Left" Name="border1" VerticalAlignment="Top" Width="240" CornerRadius="0" />
        <TextBlock Background="{x:Null}" Margin="3,6,3,0" TextAlignment="Center" FontSize="15" FontWeight="Bold" Name="devicename" Grid.ColumnSpan="3" Width="Auto" Text="设备启停" Foreground="Red" />
        <Button Content="关闭" Grid.Column="2" Grid.Row="1" Height="Auto" HorizontalAlignment="Stretch" Name="btnStop" VerticalAlignment="Stretch" Width="Auto" Margin="3" FontWeight="Bold" Foreground="#FF6C6C6C" FontSize="13" />
        <Button Content="退出" Grid.Column="1" Grid.Row="1" Height="Auto" HorizontalAlignment="Stretch" Name="btnExit" VerticalAlignment="Stretch" Width="Auto" Margin="3" FontWeight="Bold" Foreground="#FF6C6C6C" IsCancel="True" FontSize="13" Click="btnExit_Click" />
        <Button Content="开启" Grid.Column="0" Grid.Row="1" Height="Auto" HorizontalAlignment="Stretch" Name="btnStart" VerticalAlignment="Stretch" Width="Auto" Margin="3" FontWeight="Bold" Foreground="#FF6C6C6C" FontSize="13" />
    </Grid>
</Window>
