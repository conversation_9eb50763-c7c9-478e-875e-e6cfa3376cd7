using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using Lite3DogMES.ProcessManager;
using Lite3DogMES.QRCodeManager;
using Lite3DogMES.TraceabilitySystem;

namespace Lite3DogMES.UI
{
    public partial class MainWindow : Window
    {
        private readonly string _connectionString;
        private readonly ProcessFlowController _processController;
        private readonly ProcessStatusMonitor _statusMonitor;
        private readonly ProductTraceabilityManager _traceabilityManager;
        private readonly QRCodeScanner _qrScanner;
        private readonly Timer _refreshTimer;
        
        private ObservableCollection<ProductStatus> _productStatuses;
        private string _currentUser = "操作员";
        private string _currentOperatorID = "operator1";

        public MainWindow()
        {
            InitializeComponent();
            
            // 初始化连接字符串
            _connectionString = ConfigurationManager.ConnectionStrings["Lite3DogMES"]?.ConnectionString 
                ?? "Server=.;Database=Lite3DogMES;Integrated Security=true;";
            
            // 初始化业务组件
            _processController = new ProcessFlowController(_connectionString);
            _statusMonitor = new ProcessStatusMonitor(_connectionString);
            _traceabilityManager = new ProductTraceabilityManager(_connectionString);
            _qrScanner = new QRCodeScanner();
            
            // 初始化数据集合
            _productStatuses = new ObservableCollection<ProductStatus>();
            dgProductStatus.ItemsSource = _productStatuses;
            
            // 初始化定时器
            _refreshTimer = new Timer(5000); // 5秒刷新一次
            _refreshTimer.Elapsed += RefreshTimer_Elapsed;
            
            // 初始化界面
            InitializeUI();
            
            // 启动监控
            StartMonitoring();
        }

        private void InitializeUI()
        {
            txtCurrentUser.Text = $"当前用户: {_currentUser}";
            txtCurrentTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            txtStatusMessage.Text = "系统初始化完成";
            
            // 启动时间更新定时器
            var timeTimer = new Timer(1000);
            timeTimer.Elapsed += (s, e) => 
            {
                Dispatcher.Invoke(() => 
                {
                    txtCurrentTime.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                });
            };
            timeTimer.Start();
        }

        private void StartMonitoring()
        {
            try
            {
                _statusMonitor.ProcessStatusChanged += StatusMonitor_ProcessStatusChanged;
                _statusMonitor.ProcessAlert += StatusMonitor_ProcessAlert;
                _statusMonitor.StartMonitoring();
                
                _refreshTimer.Start();
                
                // 初始加载数据
                RefreshProductStatus();
                
                txtConnectionStatus.Text = "数据库连接: 正常";
                txtConnectionStatus.Foreground = System.Windows.Media.Brushes.LightGreen;
            }
            catch (Exception ex)
            {
                txtConnectionStatus.Text = "数据库连接: 异常";
                txtConnectionStatus.Foreground = System.Windows.Media.Brushes.Red;
                ShowMessage($"启动监控失败: {ex.Message}", MessageType.Error);
            }
        }

        private void RefreshTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            Dispatcher.Invoke(() => RefreshProductStatus());
        }

        private void StatusMonitor_ProcessStatusChanged(object sender, ProcessStatusEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                _productStatuses.Clear();
                foreach (var status in e.ProductStatuses)
                {
                    _productStatuses.Add(status);
                }
            });
        }

        private void StatusMonitor_ProcessAlert(object sender, ProcessAlertEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                foreach (var alert in e.Alerts)
                {
                    ShowMessage($"告警: {alert.AlertMessage}", MessageType.Warning);
                }
            });
        }

        private void RefreshProductStatus()
        {
            try
            {
                var statuses = _statusMonitor.GetAllProductStatus();
                _productStatuses.Clear();
                foreach (var status in statuses)
                {
                    _productStatuses.Add(status);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"刷新产品状态失败: {ex.Message}", MessageType.Error);
            }
        }

        // 工序操作事件处理
        private async void BtnScanQR_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var scanWindow = new QRCodeScanWindow(_qrScanner);
                if (scanWindow.ShowDialog() == true)
                {
                    txtProductSN.Text = scanWindow.ScannedProductSN;
                    ShowMessage($"扫描成功: {scanWindow.ScannedProductSN}", MessageType.Success);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"扫描二维码失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnStartAssembly_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var result = _processController.StartAssembly(txtProductSN.Text, _currentOperatorID, "装配区");
                if (result.Success)
                {
                    ShowMessage(result.Message, MessageType.Success);
                    RefreshProductStatus();
                }
                else
                {
                    ShowMessage(result.Message, MessageType.Error);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"开始装配失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnCompleteAssembly_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                // 检查是否已扫描二维码
                bool qrScanned = !string.IsNullOrWhiteSpace(txtProductSN.Text);
                
                var result = _processController.CompleteAssembly(txtProductSN.Text, qrScanned, _currentOperatorID);
                if (result.Success)
                {
                    ShowMessage(result.Message, MessageType.Success);
                    RefreshProductStatus();
                }
                else
                {
                    ShowMessage(result.Message, MessageType.Error);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"完成装配失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnCalibration_Click(object sender, RoutedEventArgs e)
        {
            await ExecuteSimpleProcess("标零", () => 
                _processController.ExecuteCalibration(txtProductSN.Text, _currentOperatorID));
        }

        private async void BtnFirstTest_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                // 打开测试窗口
                var testWindow = new TestWindow("一测", txtProductSN.Text);
                if (testWindow.ShowDialog() == true)
                {
                    var result = _processController.CompleteFirstTest(txtProductSN.Text, 
                        testWindow.TestResults, _currentOperatorID);
                    
                    if (result.Success)
                    {
                        ShowMessage(result.Message, MessageType.Success);
                        RefreshProductStatus();
                    }
                    else
                    {
                        ShowMessage(result.Message, MessageType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"一测失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnMaintenance_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var maintenanceWindow = new MaintenanceWindow(txtProductSN.Text);
                if (maintenanceWindow.ShowDialog() == true)
                {
                    var result = _processController.ExecuteMaintenance(txtProductSN.Text, 
                        maintenanceWindow.MaintenanceActions, _currentOperatorID);
                    
                    if (result.Success)
                    {
                        ShowMessage(result.Message, MessageType.Success);
                        RefreshProductStatus();
                    }
                    else
                    {
                        ShowMessage(result.Message, MessageType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"维护失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnSecondTest_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var testWindow = new TestWindow("二测", txtProductSN.Text);
                if (testWindow.ShowDialog() == true)
                {
                    var result = _processController.CompleteSecondTest(txtProductSN.Text, 
                        testWindow.TestResults, _currentOperatorID);
                    
                    if (result.Success)
                    {
                        ShowMessage(result.Message, MessageType.Success);
                        RefreshProductStatus();
                    }
                    else
                    {
                        ShowMessage(result.Message, MessageType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"二测失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnPackaging_Click(object sender, RoutedEventArgs e)
        {
            await ExecuteSimpleProcess("打包", () => 
                _processController.ExecutePackaging(txtProductSN.Text, _currentOperatorID));
        }

        private async void BtnShipping_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var shippingWindow = new ShippingWindow(txtProductSN.Text);
                if (shippingWindow.ShowDialog() == true)
                {
                    var result = _processController.ExecuteShipping(txtProductSN.Text, 
                        shippingWindow.ShippingInfo, _currentOperatorID);
                    
                    if (result.Success)
                    {
                        ShowMessage(result.Message, MessageType.Success);
                        RefreshProductStatus();
                    }
                    else
                    {
                        ShowMessage(result.Message, MessageType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"发货失败: {ex.Message}", MessageType.Error);
            }
        }

        // 追溯查询事件处理
        private async void BtnQueryTrace_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtTraceProductSN.Text))
            {
                ShowMessage("请输入要查询的产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var traceChain = _traceabilityManager.GetProductTraceChain(txtTraceProductSN.Text);
                if (traceChain != null)
                {
                    // 显示追溯时间线
                    var timeline = GenerateTimeline(traceChain);
                    lvTraceTimeline.ItemsSource = timeline;
                    
                    // 显示工序记录
                    dgProcessRecords.ItemsSource = traceChain.ProcessRecords;
                    
                    // 显示质量记录
                    dgQualityRecords.ItemsSource = traceChain.QualityRecords;
                    
                    ShowMessage("追溯查询完成", MessageType.Success);
                }
                else
                {
                    ShowMessage("未找到该产品的追溯信息", MessageType.Warning);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"追溯查询失败: {ex.Message}", MessageType.Error);
            }
        }

        private async void BtnGenerateReport_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtTraceProductSN.Text))
            {
                ShowMessage("请输入要生成报告的产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var report = _traceabilityManager.GenerateTraceabilityReport(txtTraceProductSN.Text);
                if (report.IsValid)
                {
                    var reportWindow = new TraceabilityReportWindow(report);
                    reportWindow.Show();
                    ShowMessage("追溯报告生成完成", MessageType.Success);
                }
                else
                {
                    ShowMessage(report.ErrorMessage, MessageType.Error);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"生成报告失败: {ex.Message}", MessageType.Error);
            }
        }

        private void BtnLogout_Click(object sender, RoutedEventArgs e)
        {
            if (MessageBox.Show("确定要退出登录吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        // 辅助方法
        private async Task ExecuteSimpleProcess(string processName, Func<ProcessResult> processAction)
        {
            if (string.IsNullOrWhiteSpace(txtProductSN.Text))
            {
                ShowMessage("请输入产品序列号", MessageType.Warning);
                return;
            }

            try
            {
                var result = processAction();
                if (result.Success)
                {
                    ShowMessage(result.Message, MessageType.Success);
                    RefreshProductStatus();
                }
                else
                {
                    ShowMessage(result.Message, MessageType.Error);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"{processName}失败: {ex.Message}", MessageType.Error);
            }
        }

        private List<TimelineEvent> GenerateTimeline(ProductTraceChain traceChain)
        {
            var timeline = new List<TimelineEvent>();

            // 添加工序事件
            foreach (var process in traceChain.ProcessRecords)
            {
                if (process.StartTime.HasValue)
                {
                    timeline.Add(new TimelineEvent
                    {
                        EventTime = process.StartTime.Value,
                        EventType = "工序开始",
                        EventDescription = $"开始{process.ProcessName}",
                        Location = process.Location,
                        OperatorID = process.OperatorID
                    });
                }

                if (process.EndTime.HasValue)
                {
                    timeline.Add(new TimelineEvent
                    {
                        EventTime = process.EndTime.Value,
                        EventType = "工序完成",
                        EventDescription = $"完成{process.ProcessName}，结果：{process.QualityResult}",
                        Location = process.Location,
                        OperatorID = process.OperatorID
                    });
                }
            }

            // 添加追溯事件
            foreach (var traceEvent in traceChain.TraceEvents)
            {
                timeline.Add(new TimelineEvent
                {
                    EventTime = traceEvent.EventTime,
                    EventType = traceEvent.EventType,
                    EventDescription = traceEvent.EventDescription,
                    Location = traceEvent.Location,
                    OperatorID = traceEvent.OperatorID
                });
            }

            return timeline.OrderBy(t => t.EventTime).ToList();
        }

        private void ShowMessage(string message, MessageType type)
        {
            txtStatusMessage.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
            
            switch (type)
            {
                case MessageType.Success:
                    txtStatusMessage.Foreground = System.Windows.Media.Brushes.LightGreen;
                    break;
                case MessageType.Warning:
                    txtStatusMessage.Foreground = System.Windows.Media.Brushes.Orange;
                    break;
                case MessageType.Error:
                    txtStatusMessage.Foreground = System.Windows.Media.Brushes.Red;
                    break;
                default:
                    txtStatusMessage.Foreground = System.Windows.Media.Brushes.White;
                    break;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _statusMonitor?.StopMonitoring();
            _statusMonitor?.Dispose();
            _qrScanner?.Dispose();
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            base.OnClosed(e);
        }
    }

    public enum MessageType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
