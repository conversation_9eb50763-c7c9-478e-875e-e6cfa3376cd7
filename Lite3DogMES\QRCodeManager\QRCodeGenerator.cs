using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Text;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;
using ZXing.QrCode.Internal;

namespace Lite3DogMES.QRCodeManager
{
    /// <summary>
    /// 二维码生成器
    /// </summary>
    public class QRCodeGenerator
    {
        private readonly int _width;
        private readonly int _height;
        private readonly int _margin;

        public QRCodeGenerator(int width = 300, int height = 300, int margin = 1)
        {
            _width = width;
            _height = height;
            _margin = margin;
        }

        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="content">二维码内容</param>
        /// <param name="logoPath">Logo图片路径（可选）</param>
        /// <returns>二维码图片字节数组</returns>
        public byte[] GenerateQRCode(string content, string logoPath = null)
        {
            try
            {
                var writer = new BarcodeWriter
                {
                    Format = BarcodeFormat.QR_CODE,
                    Options = new EncodingOptions
                    {
                        Width = _width,
                        Height = _height,
                        Margin = _margin,
                        CharacterSet = "UTF-8"
                    }
                };

                // 设置二维码参数
                var qrCodeWriter = new QrCodeWriter();
                var hints = new Dictionary<EncodeHintType, object>
                {
                    { EncodeHintType.CHARACTER_SET, "UTF-8" },
                    { EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H },
                    { EncodeHintType.MARGIN, _margin }
                };

                writer.Options = new QrCodeEncodingOptions
                {
                    CharacterSet = "UTF-8",
                    Width = _width,
                    Height = _height,
                    Margin = _margin,
                    ErrorCorrection = ErrorCorrectionLevel.H
                };

                using (var bitmap = writer.Write(content))
                {
                    // 如果有Logo，添加Logo
                    if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                    {
                        AddLogoToQRCode(bitmap, logoPath);
                    }

                    // 转换为字节数组
                    using (var stream = new MemoryStream())
                    {
                        bitmap.Save(stream, ImageFormat.Png);
                        return stream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"生成二维码失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 在二维码中添加Logo
        /// </summary>
        /// <param name="qrBitmap">二维码图片</param>
        /// <param name="logoPath">Logo路径</param>
        private void AddLogoToQRCode(Bitmap qrBitmap, string logoPath)
        {
            try
            {
                using (var logo = new Bitmap(logoPath))
                using (var graphics = Graphics.FromImage(qrBitmap))
                {
                    // 计算Logo大小（二维码的1/5）
                    int logoSize = Math.Min(qrBitmap.Width, qrBitmap.Height) / 5;
                    int logoX = (qrBitmap.Width - logoSize) / 2;
                    int logoY = (qrBitmap.Height - logoSize) / 2;

                    // 绘制白色背景
                    graphics.FillRectangle(Brushes.White, logoX - 5, logoY - 5, logoSize + 10, logoSize + 10);

                    // 绘制Logo
                    graphics.DrawImage(logo, logoX, logoY, logoSize, logoSize);
                }
            }
            catch (Exception ex)
            {
                // Logo添加失败不影响二维码生成
                Console.WriteLine($"添加Logo失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成产品二维码内容
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="productModel">产品型号</param>
        /// <param name="productionDate">生产日期</param>
        /// <returns>二维码内容</returns>
        public static string GenerateProductQRContent(string productSN, string productModel, DateTime productionDate)
        {
            var qrData = new
            {
                ProductSN = productSN,
                ProductModel = productModel,
                ProductionDate = productionDate.ToString("yyyy-MM-dd"),
                Company = "云深处科技",
                Timestamp = DateTimeOffset.Now.ToUnixTimeSeconds()
            };

            return Newtonsoft.Json.JsonConvert.SerializeObject(qrData);
        }

        /// <summary>
        /// 批量生成二维码
        /// </summary>
        /// <param name="contents">二维码内容列表</param>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>生成的文件路径列表</returns>
        public List<string> GenerateBatchQRCodes(List<string> contents, string outputDirectory)
        {
            var filePaths = new List<string>();

            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            for (int i = 0; i < contents.Count; i++)
            {
                try
                {
                    var qrBytes = GenerateQRCode(contents[i]);
                    var fileName = $"QR_{DateTime.Now:yyyyMMdd}_{i + 1:D4}.png";
                    var filePath = Path.Combine(outputDirectory, fileName);

                    File.WriteAllBytes(filePath, qrBytes);
                    filePaths.Add(filePath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"生成第{i + 1}个二维码失败: {ex.Message}");
                }
            }

            return filePaths;
        }

        /// <summary>
        /// 验证二维码内容格式
        /// </summary>
        /// <param name="content">二维码内容</param>
        /// <returns>是否有效</returns>
        public static bool ValidateQRContent(string content)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(content))
                    return false;

                // 尝试解析JSON格式的二维码内容
                var qrData = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(content);
                
                // 检查必要字段
                return qrData?.ProductSN != null && 
                       qrData?.ProductModel != null && 
                       qrData?.ProductionDate != null;
            }
            catch
            {
                // 如果不是JSON格式，检查是否为简单的产品序列号格式
                return content.Length >= 5 && content.Length <= 50;
            }
        }
    }
}
