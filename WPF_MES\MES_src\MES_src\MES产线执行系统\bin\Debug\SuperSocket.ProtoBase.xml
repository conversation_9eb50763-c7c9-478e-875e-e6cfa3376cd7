<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SuperSocket.ProtoBase</name>
    </assembly>
    <members>
        <member name="T:SuperSocket.ProtoBase.BasicStringParser">
            <summary>
            the string parser who use SPACE to separate string into many parts
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.BasicStringParser.DefaultInstance">
            <summary>
            The default singlegton instance
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BasicStringParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.BasicStringParser"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BasicStringParser.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.BasicStringParser"/> class.
            </summary>
            <param name="spliter">The spliter between command name and command parameters.</param>
            <param name="parameterSpliter">The parameter spliter.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.BasicStringParser.Parse(System.String,System.String@,System.String@,System.String[]@)">
            <summary>
            parse the source string into key, body and parameters parts
            </summary>
            <param name="source">the source string</param>
            <param name="key">the parsed key</param>
            <param name="body">the parsed body</param>
            <param name="parameters">the parsed parameter</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.BufferedPackageInfo`1">
            <summary>
            Buffered package info
            
            </summary>
            <typeparam name="TKey">The type of the key.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferedPackageInfo`1.Key">
            <summary>
            Gets the key.
            </summary>
            <value>
            The key.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferedPackageInfo`1.#ctor(`0,System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.BufferedPackageInfo`1"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="data">The data.</param>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferedPackageInfo`1.Data">
            <summary>
            Gets the buffered data.
            </summary>
            <value>
            The buffered data.
            </value>
        </member>
        <member name="T:SuperSocket.ProtoBase.BufferedPackageInfo">
            <summary>
            Buffered package info
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferedPackageInfo.#ctor(System.String,System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.BufferedPackageInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="data">The data.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.BufferList">
            <summary>
            The receive cache
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferList.Last">
            <summary>
            Gets the last buffer segment.
            </summary>
            <value>
            The last.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.IndexOf(System.ArraySegment{System.Byte})">
            <summary>
            Determines the index of a specific item in the <see cref="T:System.Collections.Generic.IList`1" />.
            </summary>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.IList`1" />.</param>
            <returns>
            The index of <paramref name="item" /> if found in the list; otherwise, -1.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.Insert(System.Int32,System.ArraySegment{System.Byte})">
            <summary>
            Inserts an item to the <see cref="T:System.Collections.Generic.IList`1" /> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
            <param name="item">The object to insert into the <see cref="T:System.Collections.Generic.IList`1" />.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.RemoveAt(System.Int32)">
            <summary>
            Removes the <see cref="T:System.Collections.Generic.IList`1" /> item at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferList.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.Add(System.ArraySegment{System.Byte})">
            <summary>
            Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.SetLastItemLength(System.Int32)">
            <summary>
            Sets the last length of the item.
            </summary>
            <param name="length">The length.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.Contains(System.ArraySegment{System.Byte})">
            <summary>
            Determines whether the <see cref="T:System.Collections.Generic.ICollection`1" /> contains a specific value.
            </summary>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            <returns>
            true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.CopyTo(System.ArraySegment{System.Byte}[],System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferList.Total">
            <summary>
            Gets the total length.
            </summary>
            <value>
            The total.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferList.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <returns>
            The number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.
              </returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferList.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.
              </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.Remove(System.ArraySegment{System.Byte})">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
            <returns>
            true if <paramref name="item" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false. This method also returns false if <paramref name="item" /> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferList.GetAllCachedItems">
            <summary>
            Gets all cached items.
            </summary>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.IBufferStream">
            <summary>
            The interface for the stream class whose data is consistent of many data segments
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            read data from the stream to the specific buffer
            </summary>
            <param name="buffer">the target buffer</param>
            <param name="offset">offset you want to read</param>
            <param name="count">total count you want to read</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadByte">
            <summary>
            Read one byte from the stream
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadInt16">
            <summary>
            Reads a Int16 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadInt16(System.Boolean)">
            <summary>
            Reads a Int16 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> read the value as little endian, otherwise big endian.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadUInt16">
            <summary>
            Reads a UInt16 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadUInt16(System.Boolean)">
            <summary>
            Reads a UInt16 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> read the value as little endian, otherwise big endian.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadInt32">
            <summary>
            Reads a Int32 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadInt32(System.Boolean)">
            <summary>
            Reads a Int32 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> read the value as little endian, otherwise big endian.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadUInt32">
            <summary>
            Reads a UInt32 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadUInt32(System.Boolean)">
            <summary>
            Reads a UInt32 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> read the value as little endian, otherwise big endian.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadInt64">
            <summary>
            Reads a Int64 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadInt64(System.Boolean)">
            <summary>
            Reads a Int64 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> read the value as little endian, otherwise big endian.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadUInt64">
            <summary>
            Reads a UInt64 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadUInt64(System.Boolean)">
            <summary>
            Reads a UInt64 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> read the value as little endian, otherwise big endian.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.Take(System.Int32)">
            <summary>
            Takes the data of the specified length.
            </summary>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.ReadString(System.Int32,System.Text.Encoding)">
            <summary>
            Reads a string from the current data source
            </summary>
            <param name="length">The length of the string in bytes.</param>
            <param name="encoding">The encoding.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.Skip(System.Int32)">
            <summary>
            Skips the specified count bytes from the data source.
            </summary>
            <param name="count">The number of bytes to skip.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferStream.GetCurrentStream">
            <summary>
            Get current buffer as Stream
            </summary>
            <returns>the stream represent the current buffer</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.IBufferStream.Length">
            <summary>
            Total length of the buffered data
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.IBufferStream.Buffers">
            <summary>
            All buffered binary segments in the stream
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.BufferStream">
            <summary>
            The default buffer stream
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.BufferStream.m_Buffer">
            <summary>
            Buffer used for temporary storage before conversion into primitives
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.BufferStream"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Initialize(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Initializes the specified segments.
            </summary>
            <param name="segments">The segments.</param>
            <exception cref="T:System.ArgumentException">The length of segments must be greater than zero.</exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.GetCurrent">
            <summary>
            Gets the current buffer stream from the thread context
            </summary>
            <value>
            The current buffer stream
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.GetCurrent``1">
            <summary>
            Gets the current buffer stream from the thread context
            </summary>
            <typeparam name="TStream">The type of the stream.</typeparam>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferStream.Buffers">
            <summary>
            All buffered binary segments in the stream
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.GetCurrentStream">
            <summary>
            Get current buffer as Stream
            </summary>
            <returns>the stream represent the current buffer</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Reset">
            <summary>
            Resets this stream.
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferStream.CanRead">
            <summary>
            When overridden in a derived class, gets a value indicating whether the current stream supports reading.
            </summary>
            <returns>true if the stream supports reading; otherwise, false.</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferStream.CanSeek">
            <summary>
            When overridden in a derived class, gets a value indicating whether the current stream supports seeking.
            </summary>
            <returns>true if the stream supports seeking; otherwise, false.</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferStream.CanWrite">
            <summary>
            When overridden in a derived class, gets a value indicating whether the current stream supports writing.
            </summary>
            <returns>true if the stream supports writing; otherwise, false.</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Flush">
            <summary>
            When overridden in a derived class, clears all buffers for this stream and causes any buffered data to be written to the underlying device.
            </summary>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferStream.Length">
            <summary>
            When overridden in a derived class, gets the length in bytes of the stream.
            </summary>
            <returns>A long value representing the length of the stream in bytes.</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.BufferStream.Position">
            <summary>
            When overridden in a derived class, gets or sets the position within the current stream.
            </summary>
            <returns>The current position within the stream.</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            When overridden in a derived class, reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" /> - 1) replaced by the bytes read from the current source.</param>
            <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            When overridden in a derived class, sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the <paramref name="origin" /> parameter.</param>
            <param name="origin">A value of type <see cref="T:System.IO.SeekOrigin" /> indicating the reference point used to obtain the new position.</param>
            <returns>
            The new position within the current stream.
            </returns>
            <exception cref="T:System.ArgumentException">Cannot support seek from the end.</exception>
            <exception cref="T:System.Exception">Exceed the stream's end</exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.SetLength(System.Int64)">
            <summary>
            When overridden in a derived class, sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            When overridden in a derived class, writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">An array of bytes. This method copies <paramref name="count" /> bytes from <paramref name="buffer" /> to the current stream.</param>
            <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to be written to the current stream.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.CheckInitialized">
            <summary>
            Check to see if this instance is initialized.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            This instance is not initialized.s
            </exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Skip(System.Int32)">
            <summary>
            Skips the specified count bytes from the data source.
            </summary>
            <param name="count">The count.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            count;count cannot be negative
            or
            count;exceed the total length
            </exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.Take(System.Int32)">
            <summary>
            Takes the data of the specified length.
            </summary>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadString(System.Int32,System.Text.Encoding)">
            <summary>
            Reads a string from the current data source
            </summary>
            <param name="length">The length of the string in bytes.</param>
            <param name="encoding">The encoding.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentOutOfRangeException">length;there is no enougth data</exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.FillBuffer(System.Int32)">
            <summary>
            Fills the buffer.
            </summary>
            <param name="length">The length.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            length;the length must between 1 and 8
            or
            length;there is no enough data to read
            </exception>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadInt16">
            <summary>
            Reads a Int16 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadInt16(System.Boolean)">
            <summary>
            Reads a Int16 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> [little endian].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadUInt16">
            <summary>
            Reads a UInt16 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadUInt16(System.Boolean)">
            <summary>
            Reads a UInt16 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> [little endian].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadInt32">
            <summary>
            Reads a Int32 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadInt32(System.Boolean)">
            <summary>
            Reads a Int32 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> [little endian].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadUInt32">
            <summary>
            Reads a UInt32 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadUInt32(System.Boolean)">
            <summary>
            Reads a UInt32 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> [little endian].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadInt64">
            <summary>
            Reads a Int64 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadInt64(System.Boolean)">
            <summary>
            Reads a Int64 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> [little endian].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadUInt64">
            <summary>
            Reads a UInt64 number from the current data source.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.ReadUInt64(System.Boolean)">
            <summary>
            Reads a UInt64 number from the current data source.
            </summary>
            <param name="littleEndian">if set to <c>true</c> [little endian].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.BigEndianFromBytes(System.Byte[],System.Int32)">
            <summary>
            Gets a <see cref="T:System.Int64"/> value by reading the buffer as a big endian integer.
            </summary>
            <param name="buffer">The buffer to read the data from.</param>
            <param name="bytesToConvert">The number bytes to convert.</param>
            <returns></returns>
            <remarks>
            Code taken from Jon Skeet's Miscellaneous Utility Library
            &lt;a href="http://www.yoda.arachsys.com/csharp/miscutil/"&gt;Jon Skeet's Miscellaneous Utility Library&lt;/a&gt;
            </remarks>
        </member>
        <member name="M:SuperSocket.ProtoBase.BufferStream.LittleEndianFromBytes(System.Byte[],System.Int32)">
            <summary>
            Gets a <see cref="T:System.Int64"/> value by reading the buffer as a little endian integer.
            </summary>
            <param name="buffer">The buffer to read the data from.</param>
            <param name="bytesToConvert">The number bytes to convert.</param>
            <returns></returns>
            <remarks>
            Code taken from Jon Skeet's Miscellaneous Utility Library
            &lt;a href="http://www.yoda.arachsys.com/csharp/miscutil/"&gt;Jon Skeet's Miscellaneous Utility Library&lt;/a&gt;
            </remarks>
        </member>
        <member name="T:SuperSocket.ProtoBase.IBufferManager">
            <summary>
            The buffer manager interface
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferManager.GetBuffer(System.Int32)">
            <summary>
            Gets the buffer.
            </summary>
            <param name="size">The size of the resired buffer.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IBufferManager.ReturnBuffer(System.Byte[])">
            <summary>
            Returns the buffer.
            </summary>
            <param name="buffer">The buffer.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.NullBufferManager">
            <summary>
            Runtime memory assignment buffer manager (no buffer)
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.NullBufferManager.GetBuffer(System.Int32)">
            <summary>
            Gets the buffer.
            </summary>
            <param name="size">The size of the resired buffer.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.NullBufferManager.ReturnBuffer(System.Byte[])">
            <summary>
            Returns the buffer.
            </summary>
            <param name="buffer">The buffer.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.CloseReason">
            <summary>
            CloseReason enum
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.Unknown">
            <summary>
            The socket is closed for unknown reason
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.ServerShutdown">
            <summary>
            Close for server shutdown
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.ClientClosing">
            <summary>
            The client close the socket
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.ServerClosing">
            <summary>
            The server side close the socket
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.ApplicationError">
            <summary>
            Application error
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.SocketError">
            <summary>
            The socket is closed for a socket error
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.TimeOut">
            <summary>
            The socket is closed by server for timeout
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.ProtocolError">
            <summary>
            Protocol error 
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.CloseReason.InternalError">
            <summary>
            SuperSocket internal error
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.DefaultPipelineProcessor`1">
            <summary>
            The default pipeline processor
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.DefaultPipelineProcessor`1.#ctor(SuperSocket.ProtoBase.IReceiveFilter{`0},System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.DefaultPipelineProcessor`1"/> class.
            </summary>
            <param name="receiveFilter">The initializing receive filter.</param>
            <param name="maxPackageLength">The max package size.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.DefaultPipelineProcessor`1.Process(System.ArraySegment{System.Byte})">
            <summary>
            Processes the input segment.
            </summary>
            <param name="segment">The input segment.</param>
            <returns>
            the processing result
            </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.DefaultPipelineProcessor`1.CleanUp">
            <summary>
            cleanup the cached the buffer by resolving them into one package at the end of the piple line
            </summary>
            <returns>return the processing result</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.DefaultPipelineProcessor`1.Cache">
            <summary>
            Gets the received cache.
            </summary>
            <value>
            The cache.
            </value>
        </member>
        <member name="T:SuperSocket.ProtoBase.DefaultProtoHandler">
            <summary>
            Default implementation of ProtoHandler
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.DefaultProtoHandler.CanSend">
            <summary>
            Determines whether this instance can send.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.DefaultProtoHandler.Close(SuperSocket.ProtoBase.ICommunicationChannel,SuperSocket.ProtoBase.CloseReason)">
            <summary>
            Closes the specified channel.
            </summary>
            <param name="channel">The channel.</param>
            <param name="reason">The reason.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IProtoDataEncoder">
            <summary>
            The interface for protocol encoder of binary data
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IProtoDataEncoder.EncodeData(SuperSocket.ProtoBase.IOutputBuffer,System.ArraySegment{System.Byte})">
            <summary>
            Encode the binary data
            </summary>
            <param name="output">the output buffer</param>
            <param name="data">the binary data to be encoded</param>
            <returns>the output binary data</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.IProtoDataEncoder.EncodeData(SuperSocket.ProtoBase.IOutputBuffer,System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Encode the binary segments
            </summary>
            <param name="output">the output buffer</param>
            <param name="data">the binary segments to be encoded</param>
            <returns>the output binary data</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.Encoder.IProtoObjectEncoder">
            <summary>
            The object protocol encoder
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.Encoder.IProtoObjectEncoder.EncodeObject(SuperSocket.ProtoBase.IOutputBuffer,System.Object)">
            <summary>
            Encode object
            </summary>
            <param name="output">the output buffer</param>
            <param name="target">the object to be encoded</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IProtoTextEncoder">
            <summary>
            The interface for protocol encoder of text messages
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IProtoTextEncoder.EncodeText(SuperSocket.ProtoBase.IOutputBuffer,System.String)">
            <summary>
            Encode text message
            </summary>
            <param name="output">the output buffer</param>
            <param name="message">the message to be encoded</param>
            <returns>the output binary data</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.Extensions">
            <summary>
            Extentions class
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.Extensions.GetString(System.Text.Encoding,System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Gets string from the binary segments data.
            </summary>
            <param name="encoding">The text encoding to decode the binary data.</param>
            <param name="data">The binary segments data.</param>
            <returns>the decoded string</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.Extensions.GetString(System.Text.Encoding,System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.Int32,System.Int32)">
            <summary>
            Gets string from the binary segments data.
            </summary>
            <param name="encoding">The text encoding to decode the binary data.</param>
            <param name="data">The binary segments data.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns>
            the decoded string
            </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.Extensions.GetBufferStream``1(SuperSocket.ProtoBase.IReceiveFilter{``0},System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Gets the buffer stream instance which can be reused.
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
            <param name="receiveFilter">The receive filter.</param>
            <param name="data">The data.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.Extensions.GetBufferStream``2(SuperSocket.ProtoBase.IReceiveFilter{``1},System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Gets the buffer stream instance which can be reused.
            </summary>
            <typeparam name="TStream">The type of the stream.</typeparam>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
            <param name="receiveFilter">The receive filter.</param>
            <param name="data">The data.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.Extensions.CopyTo(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.ArraySegment{System.Byte})">
            <summary>
            Copies data of data segment list to a data segment.
            </summary>
            <param name="packageData">The package data.</param>
            <param name="data">The data.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.Extensions.CopyTo(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.ArraySegment{System.Byte},System.Int32,System.Int32)">
            <summary>
            Copies data of data segment list to a data segment.
            </summary>
            <param name="packageData">The source segments data.</param>
            <param name="data">The destination segment.</param>
            <param name="srcOffset">The source offset.</param>
            <param name="length">The length.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.FilterState">
            <summary>
            Filter state enum
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.FilterState.Normal">
            <summary>
            Normal state
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.FilterState.Error">
            <summary>
            Error state
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.HttpHeaderInfo">
            <summary>
            Http header info
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderInfo.#ctor">
            <summary>
            default constructor of HttpHeaderInfo
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpHeaderInfo.Method">
            <summary>
            Gets the method.
            </summary>
            <value>
            The method.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpHeaderInfo.Path">
            <summary>
            Gets the path.
            </summary>
            <value>
            The path.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpHeaderInfo.Version">
            <summary>
            Gets the version.
            </summary>
            <value>
            The version.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderInfo.Get(System.String)">
            <summary>
            Get item from header with key
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1">
            <summary>
            The http receive filter base class
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="F:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.NewLine">
            <summary>
            Http header terminator
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.HeaderEncoding">
            <summary>
            Header part text encoding
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1" /> class.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.#ctor(System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1" /> class.
            </summary>
            <param name="headerEncoding">Header part text encoding</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.#ctor(System.Text.Encoding,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1" /> class.
            </summary>
            <param name="headerEncoding">Header part text encoding</param>
            <param name="terminator">the terminator of the header part</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.GetBodyReceiveFilter(SuperSocket.ProtoBase.HttpHeaderInfo,System.Int32)">
            <summary>
            Gets the receive filter for body.
            </summary>
            <param name="header">The header.</param>
            <param name="headerSize">Size of the header.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.ResolveHttpPackageWithoutBody(SuperSocket.ProtoBase.HttpHeaderInfo)">
            <summary>
            Resolves the HTTP package without body.
            </summary>
            <param name="header">The http header.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpHeaderReceiveFilterBase`1.ResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.IHttpPackageInfo">
            <summary>
            IHttpRequestInfo
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.IHttpPackageInfo.Header">
            <summary>
            Gets the http header.
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.HttpPackageInfoBase">
            <summary>
            HttpRequestInfoBase
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpPackageInfoBase.Key">
            <summary>
            Gets the key of this request.
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpPackageInfoBase.Header">
            <summary>
            Gets the http header.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpPackageInfoBase.#ctor(System.String,SuperSocket.ProtoBase.HttpHeaderInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.HttpPackageInfoBase"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="header">The header.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.HttpPackageInfoBase`1">
            <summary>
            HttpPackageInfoBase
            </summary>
            <typeparam name="TRequestBody">The type of the request body.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.HttpPackageInfoBase`1.Body">
            <summary>
            Gets the body.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.HttpPackageInfoBase`1.#ctor(System.String,SuperSocket.ProtoBase.HttpHeaderInfo,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.HttpPackageInfoBase`1"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="header">The header.</param>
            <param name="body">The body.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IBufferedPackageInfo">
            <summary>
            The interface for buffered package info
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.IBufferedPackageInfo.Data">
            <summary>
            Gets the buffered data.
            </summary>
            <value>
            The buffered data.
            </value>
        </member>
        <member name="T:SuperSocket.ProtoBase.ICommunicationChannel">
            <summary>
            The interface for communication channel
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.ICommunicationChannel.Send(System.ArraySegment{System.Byte})">
            <summary>
            Send the binary segment to the other endpoint through this communication channel
            </summary>
            <param name="segment">the data segment to be sent</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.ICommunicationChannel.Close(SuperSocket.ProtoBase.CloseReason)">
            <summary>
            Close the communication channel
            </summary>
            <param name="reason">The reason.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IOutputBuffer">
            <summary>
            The output buffer interface
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IOutputBuffer.Add(System.ArraySegment{System.Byte})">
            <summary>
            add single one item into the queue
            </summary>
            <param name="item">the item to be inserted</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.IOutputBuffer.AddRange(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            add multiple items into the queue
            </summary>
            <param name="items">the multiple items to be inserted</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IPackageHandler`1">
            <summary>
            Package handler interface
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.IPackageHandler`1.Handle(`0)">
            <summary>
            Handles the specified received package.
            </summary>
            <param name="package">The received package.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IPackageInfo">
            <summary>
            The basic package info interface
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.IPackageInfo`1">
            <summary>
            The basic package info interface with key
            </summary>
            <typeparam name="TKey">The type of the key.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.IPackageInfo`1.Key">
            <summary>
            Gets the key of the package info
            </summary>
            <value>
            The key.
            </value>
        </member>
        <member name="T:SuperSocket.ProtoBase.IPackageResolver`1">
            <summary>
            The package resolver interface
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.IPackageResolver`1.ResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.IPipelineProcessor">
            <summary>
            The pipeline data processor
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IPipelineProcessor.Process(System.ArraySegment{System.Byte})">
            <summary>
            Processes the input segment.
            </summary>
            <param name="segment">The input segment.</param>
            <returns>the processing result</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.IPipelineProcessor.Cache">
            <summary>
            Gets the received cache.
            </summary>
            <value>
            The cache.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.IPipelineProcessor.CleanUp">
            <summary>
            cleanup the cached the buffer by resolving them into one package at the end of the piple line
            </summary>
            <returns>return the processing result</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.IProtoHandler">
            <summary>
            the protocol handler interface
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IProtoHandler.CanSend">
            <summary>
            Determines whether this instance can send.
            </summary>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.IProtoHandler.DataEncoder">
            <summary>
            Gets the data encoder.
            </summary>
            <value>
            The data encoder.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.IProtoHandler.Close(SuperSocket.ProtoBase.ICommunicationChannel,SuperSocket.ProtoBase.CloseReason)">
            <summary>
            Closes the specified channel.
            </summary>
            <param name="channel">The channel.</param>
            <param name="reason">The reason.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.IReceiveFilter`1">
            <summary>
            The receive filter interface
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.IReceiveFilter`1.Filter(SuperSocket.ProtoBase.BufferList,System.Int32@)">
            <summary>
            Filters the received data.
            </summary>
            <param name="data">The received data.</param>
            <param name="rest">The length of the rest data after filtering.</param>
            <returns>the received packageInfo instance</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.IReceiveFilter`1.NextReceiveFilter">
            <summary>
            Gets the next receive filter which will be used when the next network data is received
            </summary>
            <value>
            The next receive filter.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.IReceiveFilter`1.State">
            <summary>
            Gets the state of the current filter.
            </summary>
            <value>
            The filter state.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.IReceiveFilter`1.Reset">
            <summary>
            Resets this receive filter.
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.ICleanupReceiveFilter`1">
            <summary>
            the interface to support resolving the cached buffers into package without basic network protocol resolving
            </summary>
            <typeparam name="TPackageInfo"></typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.ICleanupReceiveFilter`1.ResolvePackage(SuperSocket.ProtoBase.BufferList)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="data">The received buffer.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.IStringParser">
            <summary>
            the interface for the tools to parse string into key, body and parameters parts
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.IStringParser.Parse(System.String,System.String@,System.String@,System.String[]@)">
            <summary>
            parse the source string into key, body and parameters parts
            </summary>
            <param name="source">the source string</param>
            <param name="key">the parsed key</param>
            <param name="body">the parsed body</param>
            <param name="parameters">the parsed parameter</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.MimeHeaderHelper">
            <summary>
            MimeHeader Helper class
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.MimeHeaderHelper.ParseHttpHeader(System.String,SuperSocket.ProtoBase.HttpHeaderInfo)">
            <summary>
            Parses the HTTP header.
            </summary>
            <param name="headerData">The header data.</param>
            <param name="header">The header.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.PackageInfo`2">
            <summary>
            Pakcage info class template with key and body parts
            </summary>
            <typeparam name="TKey">The type of the key.</typeparam>
            <typeparam name="TBody">The type of the body.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.PackageInfo`2.Key">
            <summary>
            Gets the key.
            </summary>
            <value>
            The key.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.PackageInfo`2.Body">
            <summary>
            Gets the body.
            </summary>
            <value>
            The body.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.PackageInfo`2.#ctor(`0,`1)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.PackageInfo`2"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="body">The body.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.ProcessResult">
            <summary>
            The pipeline data processing result
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.ProcessResult.State">
            <summary>
            Gets the state.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.ProcessResult.Message">
            <summary>
            Gets the message.
            </summary>
            <value>
            The message.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.ProcessResult.Packages">
            <summary>
            the all packages which are resolved by this round processing
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.ProcessResult.Create(SuperSocket.ProtoBase.ProcessState)">
            <summary>
            Creates a processing result with the specified state.
            </summary>
            <param name="state">The state.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.ProcessResult.Create(SuperSocket.ProtoBase.ProcessState,System.String)">
            <summary>
            Creates a processing result with the specified state.
            </summary>
            <param name="state">The state.</param>
            <param name="message">The message.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.ProcessResult.Create(SuperSocket.ProtoBase.ProcessState,System.Collections.Generic.IList{SuperSocket.ProtoBase.IPackageInfo})">
            <summary>
            Creates a processing result with the specified state.
            </summary>
            <param name="state">The state.</param>
            <param name="packages">The packages which were processed in this round.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.ProcessResult.Create(SuperSocket.ProtoBase.ProcessState,System.String,System.Collections.Generic.IList{SuperSocket.ProtoBase.IPackageInfo})">
            <summary>
            Creates a processing result with the specified state.
            </summary>
            <param name="state">The state.</param>
            <param name="message">The message.</param>
            <param name="packages">The packages which were processed in this round.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.ProcessState">
            <summary>
            The processing state
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.ProcessState.Completed">
            <summary>
            The being processed data was processed completely
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.ProcessState.Cached">
            <summary>
            The being processed data was cached
            </summary>
        </member>
        <member name="F:SuperSocket.ProtoBase.ProcessState.Error">
            <summary>
            The processor is in error state
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.ProtoHandlerBase">
            <summary>
            ProtoHandler's base class
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.ProtoHandlerBase.DataEncoder">
            <summary>
            Gets the binary data encoder.
            </summary>
            <value>
            The binary data encoder.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.ProtoHandlerBase.CanSend">
            <summary>
            Determines whether this instance can send.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.ProtoHandlerBase.Close(SuperSocket.ProtoBase.ICommunicationChannel,SuperSocket.ProtoBase.CloseReason)">
            <summary>
            Closes the specified channel.
            </summary>
            <param name="channel">The channel.</param>
            <param name="reason">The reason.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1">
            <summary>
            The receive filter which is designed for the protocol with begin and end mark within each message
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.#ctor(System.Byte[],System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1"/> class.
            </summary>
            <param name="beginMark">The begin mark.</param>
            <param name="endMark">The end mark.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.ChangeBeginMark(System.Byte[])">
            <summary>
            Changes the begin mark.
            </summary>
            <param name="beginMark">The begin mark.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.ChangeEndMark(System.Byte[])">
            <summary>
            Changes the end mark.
            </summary>
            <param name="endMark">The end mark.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.ResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.Filter(SuperSocket.ProtoBase.BufferList,System.Int32@)">
            <summary>
            Filters the received data.
            </summary>
            <param name="data">The received data.</param>
            <param name="rest">The length of the rest data after filtering.</param>
            <returns>the received packageInfo instance</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.NextReceiveFilter">
            <summary>
            Gets or sets the next receive filter. The next receive filter will be used when the next network data is received.
            </summary>
            <value>
            The next receive filter.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.State">
            <summary>
            Gets or sets the filter state.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.BeginEndMarkReceiveFilter`1.Reset">
            <summary>
            Resets this receive filter.
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1">
            <summary>
            This Receive filter is designed for this kind protocol:
            each request has fixed count part which splited by a char(byte)
            for instance, request is defined like this "#12122#23343#4545456565#343435446#",
            because this request is splited into many parts by 5 '#', we can create a Receive filter by CountSpliterRequestFilter((byte)'#', 5)
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1.#ctor(System.Byte[],System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1"/> class.
            </summary>
            <param name="spliter">The spliter.</param>
            <param name="spliterCount">The spliter count.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1.Filter(SuperSocket.ProtoBase.BufferList,System.Int32@)">
            <summary>
            Filters the received data.
            </summary>
            <param name="data">The received data.</param>
            <param name="rest">The length of the rest data after filtering.</param>
            <returns>the received packageInfo instance</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1.NextReceiveFilter">
            <summary>
            Gets or sets the next receive filter. The next receive filter will be used when the next network data is received.
            </summary>
            <value>
            The next receive filter.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1.State">
            <summary>
            Gets the state of the current filter.
            </summary>
            <value>
            The filter state.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1.Reset">
            <summary>
            Resets this receive filter.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.CountSpliterReceiveFilter`1.ResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1">
            <summary>
            FixedHeaderReceiveFilter,
            it is the Receive filter base for the protocol which define fixed length header and the header contains the request body length,
            you can implement your own Receive filter for this kind protocol easily by inheriting this class
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1.HeaderSize">
            <summary>
            Gets the size of the header.
            </summary>
            <value>
            The size of the header.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1"/> class.
            </summary>
            <param name="headerSize">Size of the header.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1.GetBodyLengthFromHeader(SuperSocket.ProtoBase.IBufferStream,System.Int32)">
            <summary>
            Gets the body length from header.
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <param name="length">The length of the header.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1.CanResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Determines whether this instance [can resolve package] the specified package data.
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>
              <c>true</c> if this instance [can resolve package] the specified package data; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedHeaderReceiveFilter`1.Reset">
            <summary>
            Resets this receive filters.
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1">
            <summary>
            The receive filter which is designed for the protocol all messages are in the same fixed size
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.Size">
            <summary>
            The total size of the package the receive filter should receive
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1"/> class.
            </summary>
            <param name="size">The size.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.Filter(SuperSocket.ProtoBase.BufferList,System.Int32@)">
            <summary>
            Filters the received data.
            </summary>
            <param name="data">The received data.</param>
            <param name="rest">The length of the rest data after filtering.</param>
            <returns>the received packageInfo instance</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.NextReceiveFilter">
            <summary>
            Gets or sets the next receive filter which will be used when the next network data is received.
            </summary>
            <value>
            The next receive filter.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.State">
            <summary>
            Gets/sets the state of the current filter.
            </summary>
            <value>
            The filter state.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.ResetSize(System.Int32)">
            <summary>
            Resets the size.
            </summary>
            <param name="newSize">The new size.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.Reset">
            <summary>
            Resets this receive filter.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.CanResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Determines whether this instance [can resolve package] the specified package data.
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>
              <c>true</c> if this instance [can resolve package] the specified package data; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.FixedSizeReceiveFilter`1.ResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.SearchMarkState`1">
            <summary>
            SearchMarkState
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.ProtoBase.SearchMarkState`1.#ctor(`0[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.SearchMarkState`1"/> class.
            </summary>
            <param name="mark">The mark.</param>
        </member>
        <member name="P:SuperSocket.ProtoBase.SearchMarkState`1.Mark">
            <summary>
            Gets the mark.
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.SearchMarkState`1.Matched">
            <summary>
            Gets or sets whether matched already.
            </summary>
            <value>
            The matched.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.SearchMarkState`1.Change(`0[])">
            <summary>
            Changes the search mark.
            </summary>
            <param name="mark">The mark.</param>
        </member>
        <member name="T:SuperSocket.ProtoBase.StateFullSearch">
            <summary>
            Statefull search util class
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.IndexOf``1(System.Collections.Generic.IList{``0},``0,System.Int32,System.Int32)">
            <summary>
            Search target from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
            <param name="pos">The pos.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},``0[],System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},``0[])">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[])">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[],System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[],System.Int32)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <param name="matched">The matched.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[],System.Int32,System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <param name="matched">The matched.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,SuperSocket.ProtoBase.SearchMarkState{``0},System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="searchState">State of the search.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.StartsWith``1(System.Collections.Generic.IList{``0},``0[])">
            <summary>
            Startses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.StartsWith``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[])">
            <summary>
            Startses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.EndsWith``1(System.Collections.Generic.IList{``0},``0[])">
            <summary>
            Endses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.EndsWith``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[])">
            <summary>
            Endses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.StateFullSearch.CloneRange``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32)">
            <summary>
            Clones the elements in the specific range.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.ProtoBase.TerminatorReceiveFilter`1">
            <summary>
            The receive filter which is designed for the protocol whose messages must have a same terminator
            </summary>
            <typeparam name="TPackageInfo">The type of the package info.</typeparam>
        </member>
        <member name="P:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.SearchState">
            <summary>
            Gets the state of the search.
            </summary>
            <value>
            The state of the search.
            </value>
        </member>
        <member name="F:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.NullPackageInfo">
            <summary>
            The null package info
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.TerminatorReceiveFilter`1"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.Filter(SuperSocket.ProtoBase.BufferList,System.Int32@)">
            <summary>
            Filters the received data.
            </summary>
            <param name="data">The received data.</param>
            <param name="rest">The length of the rest data after filtering.</param>
            <returns>the received packageInfo instance</returns>
        </member>
        <member name="M:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.ResolvePackage(SuperSocket.ProtoBase.IBufferStream)">
            <summary>
            Resolves the package binary data to package instance
            </summary>
            <param name="bufferStream">The received buffer stream.</param>
            <returns>the resolved package instance</returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.NextReceiveFilter">
            <summary>
            Gets/sets the next receive filter which will be used when the next network data is received
            </summary>
            <value>
            The next receive filter.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.State">
            <summary>
            Gets or sets the filter state.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.TerminatorReceiveFilter`1.Reset">
            <summary>
            Resets this filter.
            </summary>
        </member>
        <member name="T:SuperSocket.ProtoBase.StringPackageInfo">
            <summary>
            String package info class
            </summary>
        </member>
        <member name="P:SuperSocket.ProtoBase.StringPackageInfo.Key">
            <summary>
            Gets the key.
            </summary>
            <value>
            The key.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.StringPackageInfo.Body">
            <summary>
            Gets the body.
            </summary>
            <value>
            The body.
            </value>
        </member>
        <member name="P:SuperSocket.ProtoBase.StringPackageInfo.Parameters">
            <summary>
            Gets the parameters.
            </summary>
            <value>
            The parameters.
            </value>
        </member>
        <member name="M:SuperSocket.ProtoBase.StringPackageInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.StringPackageInfo"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.ProtoBase.StringPackageInfo.#ctor(System.String,System.String,System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.StringPackageInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="body">The body.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.StringPackageInfo.#ctor(System.String,SuperSocket.ProtoBase.IStringParser)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.ProtoBase.StringPackageInfo"/> class.
            </summary>
            <param name="source">The source.</param>
            <param name="sourceParser">The source parser.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.StringPackageInfo.InitializeData(System.String,SuperSocket.ProtoBase.IStringParser)">
            <summary>
            Initializes the string package's data.
            </summary>
            <param name="source">The source string.</param>
            <param name="sourceParser">The source parser.</param>
        </member>
        <member name="M:SuperSocket.ProtoBase.StringPackageInfo.GetFirstParam">
            <summary>
            Gets the first param.
            </summary>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.ProtoBase.StringPackageInfo.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:System.String"/> at the specified index.
            </summary>
        </member>
    </members>
</doc>
