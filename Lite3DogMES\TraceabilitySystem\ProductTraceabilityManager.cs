using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using Newtonsoft.Json;

namespace Lite3DogMES.TraceabilitySystem
{
    /// <summary>
    /// 产品追溯管理器
    /// </summary>
    public class ProductTraceabilityManager
    {
        private readonly string _connectionString;

        public ProductTraceabilityManager(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 记录追溯事件
        /// </summary>
        /// <param name="traceEvent">追溯事件</param>
        /// <returns>是否成功</returns>
        public bool RecordTraceEvent(TraceEvent traceEvent)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        INSERT INTO ProductTraceability 
                        (ProductID, EventType, EventDescription, EventTime, Location, OperatorID, RelatedData)
                        VALUES 
                        (@ProductID, @EventType, @EventDescription, @EventTime, @Location, @OperatorID, @RelatedData)";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@ProductID", traceEvent.ProductID);
                        command.Parameters.AddWithValue("@EventType", traceEvent.EventType);
                        command.Parameters.AddWithValue("@EventDescription", traceEvent.EventDescription);
                        command.Parameters.AddWithValue("@EventTime", traceEvent.EventTime);
                        command.Parameters.AddWithValue("@Location", traceEvent.Location ?? "");
                        command.Parameters.AddWithValue("@OperatorID", traceEvent.OperatorID ?? "");
                        command.Parameters.AddWithValue("@RelatedData", 
                            traceEvent.RelatedData != null ? JsonConvert.SerializeObject(traceEvent.RelatedData) : "");

                        command.ExecuteNonQuery();
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录追溯事件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取产品完整追溯链
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <returns>追溯链信息</returns>
        public ProductTraceChain GetProductTraceChain(string productSN)
        {
            var traceChain = new ProductTraceChain();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 获取产品基本信息
                    traceChain.ProductInfo = GetProductBasicInfo(connection, productSN);
                    if (traceChain.ProductInfo == null)
                    {
                        return null;
                    }

                    // 获取追溯事件
                    traceChain.TraceEvents = GetTraceEvents(connection, traceChain.ProductInfo.ProductID);
                    
                    // 获取工序记录
                    traceChain.ProcessRecords = GetProcessRecords(connection, traceChain.ProductInfo.ProductID);
                    
                    // 获取质量检测记录
                    traceChain.QualityRecords = GetQualityRecords(connection, traceChain.ProductInfo.ProductID);
                    
                    // 获取扫描记录
                    traceChain.ScanRecords = GetScanRecords(connection, traceChain.ProductInfo.ProductID);
                    
                    // 获取发货信息
                    traceChain.ShippingInfo = GetShippingInfo(connection, traceChain.ProductInfo.ProductID);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品追溯链失败: {ex.Message}");
                return null;
            }

            return traceChain;
        }

        /// <summary>
        /// 根据时间范围查询追溯记录
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="eventType">事件类型（可选）</param>
        /// <returns>追溯记录列表</returns>
        public List<TraceRecord> QueryTraceRecords(DateTime startTime, DateTime endTime, string eventType = null)
        {
            var records = new List<TraceRecord>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT 
                            pt.TraceID,
                            pt.ProductID,
                            p.ProductSN,
                            p.ProductModel,
                            pt.EventType,
                            pt.EventDescription,
                            pt.EventTime,
                            pt.Location,
                            pt.OperatorID,
                            u.RealName as OperatorName,
                            pt.RelatedData
                        FROM ProductTraceability pt
                        INNER JOIN Products p ON pt.ProductID = p.ProductID
                        LEFT JOIN Users u ON pt.OperatorID = u.UserID
                        WHERE pt.EventTime >= @StartTime AND pt.EventTime <= @EndTime";

                    if (!string.IsNullOrEmpty(eventType))
                    {
                        sql += " AND pt.EventType = @EventType";
                    }

                    sql += " ORDER BY pt.EventTime DESC";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@StartTime", startTime);
                        command.Parameters.AddWithValue("@EndTime", endTime);
                        
                        if (!string.IsNullOrEmpty(eventType))
                        {
                            command.Parameters.AddWithValue("@EventType", eventType);
                        }

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                records.Add(new TraceRecord
                                {
                                    TraceID = Convert.ToInt32(reader["TraceID"]),
                                    ProductID = reader["ProductID"].ToString(),
                                    ProductSN = reader["ProductSN"].ToString(),
                                    ProductModel = reader["ProductModel"].ToString(),
                                    EventType = reader["EventType"].ToString(),
                                    EventDescription = reader["EventDescription"].ToString(),
                                    EventTime = Convert.ToDateTime(reader["EventTime"]),
                                    Location = reader["Location"].ToString(),
                                    OperatorID = reader["OperatorID"].ToString(),
                                    OperatorName = reader["OperatorName"].ToString(),
                                    RelatedData = reader["RelatedData"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询追溯记录失败: {ex.Message}");
            }

            return records;
        }

        /// <summary>
        /// 生成追溯报告
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <returns>追溯报告</returns>
        public TraceabilityReport GenerateTraceabilityReport(string productSN)
        {
            var report = new TraceabilityReport();
            var traceChain = GetProductTraceChain(productSN);

            if (traceChain == null)
            {
                report.IsValid = false;
                report.ErrorMessage = "产品不存在或无追溯数据";
                return report;
            }

            report.IsValid = true;
            report.ProductSN = productSN;
            report.GeneratedTime = DateTime.Now;
            report.ProductInfo = traceChain.ProductInfo;

            // 生成工序摘要
            report.ProcessSummary = GenerateProcessSummary(traceChain.ProcessRecords);
            
            // 生成质量摘要
            report.QualitySummary = GenerateQualitySummary(traceChain.QualityRecords);
            
            // 生成时间线
            report.Timeline = GenerateTimeline(traceChain);
            
            // 计算总耗时
            if (traceChain.ProcessRecords.Any())
            {
                var firstProcess = traceChain.ProcessRecords.OrderBy(p => p.StartTime).FirstOrDefault();
                var lastProcess = traceChain.ProcessRecords.OrderByDescending(p => p.EndTime).FirstOrDefault();
                
                if (firstProcess?.StartTime != null && lastProcess?.EndTime != null)
                {
                    report.TotalProcessTime = lastProcess.EndTime.Value - firstProcess.StartTime.Value;
                }
            }

            return report;
        }

        /// <summary>
        /// 批量查询产品当前位置
        /// </summary>
        /// <param name="productSNs">产品序列号列表</param>
        /// <returns>产品位置信息</returns>
        public List<ProductLocation> GetProductLocations(List<string> productSNs)
        {
            var locations = new List<ProductLocation>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT 
                            p.ProductSN,
                            p.CurrentLocation,
                            p.CurrentStatus,
                            p.UpdatedTime,
                            latest_scan.ScanTime as LastScanTime,
                            latest_scan.ScanLocation as LastScanLocation
                        FROM Products p
                        LEFT JOIN (
                            SELECT 
                                ProductID,
                                ScanTime,
                                ScanLocation,
                                ROW_NUMBER() OVER (PARTITION BY ProductID ORDER BY ScanTime DESC) as rn
                            FROM ScanRecords
                        ) latest_scan ON p.ProductID = latest_scan.ProductID AND latest_scan.rn = 1
                        WHERE p.ProductSN IN ({0})";

                    var parameters = string.Join(",", productSNs.Select((sn, index) => $"@ProductSN{index}"));
                    sql = string.Format(sql, parameters);

                    using (var command = new SqlCommand(sql, connection))
                    {
                        for (int i = 0; i < productSNs.Count; i++)
                        {
                            command.Parameters.AddWithValue($"@ProductSN{i}", productSNs[i]);
                        }

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                locations.Add(new ProductLocation
                                {
                                    ProductSN = reader["ProductSN"].ToString(),
                                    CurrentLocation = reader["CurrentLocation"].ToString(),
                                    CurrentStatus = reader["CurrentStatus"].ToString(),
                                    UpdatedTime = Convert.ToDateTime(reader["UpdatedTime"]),
                                    LastScanTime = reader["LastScanTime"] == DBNull.Value ? 
                                        null : (DateTime?)reader["LastScanTime"],
                                    LastScanLocation = reader["LastScanLocation"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品位置失败: {ex.Message}");
            }

            return locations;
        }

        /// <summary>
        /// 记录产品移动事件
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="fromLocation">起始位置</param>
        /// <param name="toLocation">目标位置</param>
        /// <param name="operatorID">操作员ID</param>
        /// <param name="reason">移动原因</param>
        /// <returns>是否成功</returns>
        public bool RecordProductMovement(string productSN, string fromLocation, string toLocation, 
            string operatorID, string reason = "")
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 获取产品ID
                    var productID = GetProductIDBySerialNumber(connection, productSN);
                    if (string.IsNullOrEmpty(productID))
                    {
                        return false;
                    }

                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 更新产品位置
                            var updateSql = @"
                                UPDATE Products 
                                SET CurrentLocation = @ToLocation, UpdatedTime = GETDATE()
                                WHERE ProductID = @ProductID";

                            using (var command = new SqlCommand(updateSql, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@ProductID", productID);
                                command.Parameters.AddWithValue("@ToLocation", toLocation);
                                command.ExecuteNonQuery();
                            }

                            // 记录追溯事件
                            var traceEvent = new TraceEvent
                            {
                                ProductID = productID,
                                EventType = "产品移动",
                                EventDescription = $"产品从{fromLocation}移动到{toLocation}。原因：{reason}",
                                EventTime = DateTime.Now,
                                Location = toLocation,
                                OperatorID = operatorID,
                                RelatedData = new
                                {
                                    FromLocation = fromLocation,
                                    ToLocation = toLocation,
                                    Reason = reason
                                }
                            };

                            RecordTraceEventInternal(connection, transaction, traceEvent);
                            
                            transaction.Commit();
                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录产品移动失败: {ex.Message}");
                return false;
            }
        }

        // 私有辅助方法
        private ProductBasicInfo GetProductBasicInfo(SqlConnection connection, string productSN)
        {
            var sql = @"
                SELECT ProductID, ProductSN, QRCode, ProductModel, ProductionDate,
                       CurrentStatus, CurrentLocation, IsCompleted, CreatedTime
                FROM Products
                WHERE ProductSN = @ProductSN";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductSN", productSN);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        return new ProductBasicInfo
                        {
                            ProductID = reader["ProductID"].ToString(),
                            ProductSN = reader["ProductSN"].ToString(),
                            QRCode = reader["QRCode"].ToString(),
                            ProductModel = reader["ProductModel"].ToString(),
                            ProductionDate = Convert.ToDateTime(reader["ProductionDate"]),
                            CurrentStatus = reader["CurrentStatus"].ToString(),
                            CurrentLocation = reader["CurrentLocation"].ToString(),
                            IsCompleted = Convert.ToBoolean(reader["IsCompleted"]),
                            CreatedTime = Convert.ToDateTime(reader["CreatedTime"])
                        };
                    }
                }
            }

            return null;
        }

        private List<TraceEventInfo> GetTraceEvents(SqlConnection connection, string productID)
        {
            var events = new List<TraceEventInfo>();

            var sql = @"
                SELECT EventType, EventDescription, EventTime, Location, OperatorID, RelatedData
                FROM ProductTraceability
                WHERE ProductID = @ProductID
                ORDER BY EventTime";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        events.Add(new TraceEventInfo
                        {
                            EventType = reader["EventType"].ToString(),
                            EventDescription = reader["EventDescription"].ToString(),
                            EventTime = Convert.ToDateTime(reader["EventTime"]),
                            Location = reader["Location"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            RelatedData = reader["RelatedData"].ToString()
                        });
                    }
                }
            }

            return events;
        }

        private List<ProcessRecordInfo> GetProcessRecords(SqlConnection connection, string productID)
        {
            var records = new List<ProcessRecordInfo>();

            var sql = @"
                SELECT pd.ProcessName, pd.ProcessCode, pr.StartTime, pr.EndTime,
                       pr.Status, pr.QualityResult, pr.OperatorID, pr.Location, pr.Remarks
                FROM ProcessRecords pr
                INNER JOIN ProcessDefinitions pd ON pr.ProcessID = pd.ProcessID
                WHERE pr.ProductID = @ProductID
                ORDER BY pd.ProcessOrder";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        records.Add(new ProcessRecordInfo
                        {
                            ProcessName = reader["ProcessName"].ToString(),
                            ProcessCode = reader["ProcessCode"].ToString(),
                            StartTime = reader["StartTime"] == DBNull.Value ? null : (DateTime?)reader["StartTime"],
                            EndTime = reader["EndTime"] == DBNull.Value ? null : (DateTime?)reader["EndTime"],
                            Status = reader["Status"].ToString(),
                            QualityResult = reader["QualityResult"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            Location = reader["Location"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        });
                    }
                }
            }

            return records;
        }

        private List<QualityRecordInfo> GetQualityRecords(SqlConnection connection, string productID)
        {
            var records = new List<QualityRecordInfo>();

            var sql = @"
                SELECT qt.TestType, qt.TestParameter, qt.StandardValue, qt.ActualValue,
                       qt.TestResult, qt.TestTime, qt.TesterID, qt.TestEquipment, qt.Remarks
                FROM QualityTests qt
                WHERE qt.ProductID = @ProductID
                ORDER BY qt.TestTime";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        records.Add(new QualityRecordInfo
                        {
                            TestType = reader["TestType"].ToString(),
                            TestParameter = reader["TestParameter"].ToString(),
                            StandardValue = Convert.ToDecimal(reader["StandardValue"]),
                            ActualValue = Convert.ToDecimal(reader["ActualValue"]),
                            TestResult = reader["TestResult"].ToString(),
                            TestTime = Convert.ToDateTime(reader["TestTime"]),
                            TesterID = reader["TesterID"].ToString(),
                            TestEquipment = reader["TestEquipment"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        });
                    }
                }
            }

            return records;
        }

        private List<ScanRecordInfo> GetScanRecords(SqlConnection connection, string productID)
        {
            var records = new List<ScanRecordInfo>();

            var sql = @"
                SELECT sr.ScanTime, sr.ScanLocation, sr.OperatorID, sr.ScanResult,
                       sr.DeviceID, sr.Remarks
                FROM ScanRecords sr
                WHERE sr.ProductID = @ProductID
                ORDER BY sr.ScanTime";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        records.Add(new ScanRecordInfo
                        {
                            ScanTime = Convert.ToDateTime(reader["ScanTime"]),
                            ScanLocation = reader["ScanLocation"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            ScanResult = reader["ScanResult"].ToString(),
                            DeviceID = reader["DeviceID"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        });
                    }
                }
            }

            return records;
        }
    }

        // 私有辅助方法
        private ProductBasicInfo GetProductBasicInfo(SqlConnection connection, string productSN)
        {
            var sql = @"
                SELECT ProductID, ProductSN, QRCode, ProductModel, ProductionDate,
                       CurrentStatus, CurrentLocation, IsCompleted, CreatedTime
                FROM Products
                WHERE ProductSN = @ProductSN";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductSN", productSN);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        return new ProductBasicInfo
                        {
                            ProductID = reader["ProductID"].ToString(),
                            ProductSN = reader["ProductSN"].ToString(),
                            QRCode = reader["QRCode"].ToString(),
                            ProductModel = reader["ProductModel"].ToString(),
                            ProductionDate = Convert.ToDateTime(reader["ProductionDate"]),
                            CurrentStatus = reader["CurrentStatus"].ToString(),
                            CurrentLocation = reader["CurrentLocation"].ToString(),
                            IsCompleted = Convert.ToBoolean(reader["IsCompleted"]),
                            CreatedTime = Convert.ToDateTime(reader["CreatedTime"])
                        };
                    }
                }
            }

            return null;
        }

        private List<TraceEventInfo> GetTraceEvents(SqlConnection connection, string productID)
        {
            var events = new List<TraceEventInfo>();

            var sql = @"
                SELECT EventType, EventDescription, EventTime, Location, OperatorID, RelatedData
                FROM ProductTraceability
                WHERE ProductID = @ProductID
                ORDER BY EventTime";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        events.Add(new TraceEventInfo
                        {
                            EventType = reader["EventType"].ToString(),
                            EventDescription = reader["EventDescription"].ToString(),
                            EventTime = Convert.ToDateTime(reader["EventTime"]),
                            Location = reader["Location"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            RelatedData = reader["RelatedData"].ToString()
                        });
                    }
                }
            }

            return events;
        }

        private List<ProcessRecordInfo> GetProcessRecords(SqlConnection connection, string productID)
        {
            var records = new List<ProcessRecordInfo>();

            var sql = @"
                SELECT pd.ProcessName, pd.ProcessCode, pr.StartTime, pr.EndTime,
                       pr.Status, pr.QualityResult, pr.OperatorID, pr.Location, pr.Remarks
                FROM ProcessRecords pr
                INNER JOIN ProcessDefinitions pd ON pr.ProcessID = pd.ProcessID
                WHERE pr.ProductID = @ProductID
                ORDER BY pd.ProcessOrder";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        records.Add(new ProcessRecordInfo
                        {
                            ProcessName = reader["ProcessName"].ToString(),
                            ProcessCode = reader["ProcessCode"].ToString(),
                            StartTime = reader["StartTime"] == DBNull.Value ? null : (DateTime?)reader["StartTime"],
                            EndTime = reader["EndTime"] == DBNull.Value ? null : (DateTime?)reader["EndTime"],
                            Status = reader["Status"].ToString(),
                            QualityResult = reader["QualityResult"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            Location = reader["Location"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        });
                    }
                }
            }

            return records;
        }

        private List<QualityRecordInfo> GetQualityRecords(SqlConnection connection, string productID)
        {
            var records = new List<QualityRecordInfo>();

            var sql = @"
                SELECT qt.TestType, qt.TestParameter, qt.StandardValue, qt.ActualValue,
                       qt.TestResult, qt.TestTime, qt.TesterID, qt.TestEquipment, qt.Remarks
                FROM QualityTests qt
                WHERE qt.ProductID = @ProductID
                ORDER BY qt.TestTime";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        records.Add(new QualityRecordInfo
                        {
                            TestType = reader["TestType"].ToString(),
                            TestParameter = reader["TestParameter"].ToString(),
                            StandardValue = Convert.ToDecimal(reader["StandardValue"]),
                            ActualValue = Convert.ToDecimal(reader["ActualValue"]),
                            TestResult = reader["TestResult"].ToString(),
                            TestTime = Convert.ToDateTime(reader["TestTime"]),
                            TesterID = reader["TesterID"].ToString(),
                            TestEquipment = reader["TestEquipment"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        });
                    }
                }
            }

            return records;
        }

        private List<ScanRecordInfo> GetScanRecords(SqlConnection connection, string productID)
        {
            var records = new List<ScanRecordInfo>();

            var sql = @"
                SELECT sr.ScanTime, sr.ScanLocation, sr.OperatorID, sr.ScanResult,
                       sr.DeviceID, sr.Remarks
                FROM ScanRecords sr
                WHERE sr.ProductID = @ProductID
                ORDER BY sr.ScanTime";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        records.Add(new ScanRecordInfo
                        {
                            ScanTime = Convert.ToDateTime(reader["ScanTime"]),
                            ScanLocation = reader["ScanLocation"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            ScanResult = reader["ScanResult"].ToString(),
                            DeviceID = reader["DeviceID"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        });
                    }
                }
            }

            return records;
        }

        private ShippingInfoDetail GetShippingInfo(SqlConnection connection, string productID)
        {
            var sql = @"
                SELECT CustomerName, CustomerAddress, ShippingDate, TrackingNumber,
                       ShippingCompany, ShippingStatus, OperatorID, Remarks
                FROM ShippingInfo
                WHERE ProductID = @ProductID";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        return new ShippingInfoDetail
                        {
                            CustomerName = reader["CustomerName"].ToString(),
                            CustomerAddress = reader["CustomerAddress"].ToString(),
                            ShippingDate = reader["ShippingDate"] == DBNull.Value ? null : (DateTime?)reader["ShippingDate"],
                            TrackingNumber = reader["TrackingNumber"].ToString(),
                            ShippingCompany = reader["ShippingCompany"].ToString(),
                            ShippingStatus = reader["ShippingStatus"].ToString(),
                            OperatorID = reader["OperatorID"].ToString(),
                            Remarks = reader["Remarks"].ToString()
                        };
                    }
                }
            }

            return null;
        }

        private string GetProductIDBySerialNumber(SqlConnection connection, string productSN)
        {
            var sql = "SELECT ProductID FROM Products WHERE ProductSN = @ProductSN";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductSN", productSN);
                return command.ExecuteScalar()?.ToString();
            }
        }

        private void RecordTraceEventInternal(SqlConnection connection, SqlTransaction transaction, TraceEvent traceEvent)
        {
            var sql = @"
                INSERT INTO ProductTraceability
                (ProductID, EventType, EventDescription, EventTime, Location, OperatorID, RelatedData)
                VALUES
                (@ProductID, @EventType, @EventDescription, @EventTime, @Location, @OperatorID, @RelatedData)";

            using (var command = new SqlCommand(sql, connection, transaction))
            {
                command.Parameters.AddWithValue("@ProductID", traceEvent.ProductID);
                command.Parameters.AddWithValue("@EventType", traceEvent.EventType);
                command.Parameters.AddWithValue("@EventDescription", traceEvent.EventDescription);
                command.Parameters.AddWithValue("@EventTime", traceEvent.EventTime);
                command.Parameters.AddWithValue("@Location", traceEvent.Location ?? "");
                command.Parameters.AddWithValue("@OperatorID", traceEvent.OperatorID ?? "");
                command.Parameters.AddWithValue("@RelatedData",
                    traceEvent.RelatedData != null ? JsonConvert.SerializeObject(traceEvent.RelatedData) : "");

                command.ExecuteNonQuery();
            }
        }

        private ProcessSummary GenerateProcessSummary(List<ProcessRecordInfo> processRecords)
        {
            var summary = new ProcessSummary();
            summary.TotalProcesses = processRecords.Count;
            summary.CompletedProcesses = processRecords.Count(p => p.Status == "完成");
            summary.PassedProcesses = processRecords.Count(p => p.QualityResult == "合格");

            if (summary.TotalProcesses > 0)
            {
                summary.CompletionRate = (decimal)summary.CompletedProcesses / summary.TotalProcesses * 100;
                summary.PassRate = (decimal)summary.PassedProcesses / summary.TotalProcesses * 100;
            }

            return summary;
        }

        private QualitySummary GenerateQualitySummary(List<QualityRecordInfo> qualityRecords)
        {
            var summary = new QualitySummary();
            summary.TotalTests = qualityRecords.Count;
            summary.PassedTests = qualityRecords.Count(q => q.TestResult == "合格");

            if (summary.TotalTests > 0)
            {
                summary.PassRate = (decimal)summary.PassedTests / summary.TotalTests * 100;
            }

            summary.TestDetails = qualityRecords.GroupBy(q => q.TestType)
                .Select(g => new TestTypeSummary
                {
                    TestType = g.Key,
                    TotalCount = g.Count(),
                    PassCount = g.Count(t => t.TestResult == "合格"),
                    PassRate = g.Count() > 0 ? (decimal)g.Count(t => t.TestResult == "合格") / g.Count() * 100 : 0
                }).ToList();

            return summary;
        }

        private List<TimelineEvent> GenerateTimeline(ProductTraceChain traceChain)
        {
            var timeline = new List<TimelineEvent>();

            // 添加工序事件
            foreach (var process in traceChain.ProcessRecords)
            {
                if (process.StartTime.HasValue)
                {
                    timeline.Add(new TimelineEvent
                    {
                        EventTime = process.StartTime.Value,
                        EventType = "工序开始",
                        EventDescription = $"开始{process.ProcessName}",
                        Location = process.Location,
                        OperatorID = process.OperatorID
                    });
                }

                if (process.EndTime.HasValue)
                {
                    timeline.Add(new TimelineEvent
                    {
                        EventTime = process.EndTime.Value,
                        EventType = "工序完成",
                        EventDescription = $"完成{process.ProcessName}，结果：{process.QualityResult}",
                        Location = process.Location,
                        OperatorID = process.OperatorID
                    });
                }
            }

            // 添加追溯事件
            foreach (var traceEvent in traceChain.TraceEvents)
            {
                timeline.Add(new TimelineEvent
                {
                    EventTime = traceEvent.EventTime,
                    EventType = traceEvent.EventType,
                    EventDescription = traceEvent.EventDescription,
                    Location = traceEvent.Location,
                    OperatorID = traceEvent.OperatorID
                });
            }

            return timeline.OrderBy(t => t.EventTime).ToList();
        }
    }
