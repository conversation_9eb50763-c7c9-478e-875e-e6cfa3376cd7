<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SuperSocket.SocketEngine</name>
    </assembly>
    <members>
        <member name="T:SuperSocket.SocketEngine.AppDomainAppServer">
            <summary>
            AppDomainAppServer
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.IsolationAppServer.InitializeLifetimeService">
            <summary>
            Obtains a lifetime service object to control the lifetime policy for this instance.
            Return null, never expired
            </summary>
            <returns>
            An object of type <see cref="T:System.Runtime.Remoting.Lifetime.ILease" /> used to control the lifetime policy for this instance. This is the current lifetime service object for this instance if one exists; otherwise, a new lifetime service object initialized to the value of the <see cref="P:System.Runtime.Remoting.Lifetime.LifetimeServices.LeaseManagerPollTime" /> property.
            </returns>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="RemotingConfiguration, Infrastructure" />
              </PermissionSet>
        </member>
        <member name="P:SuperSocket.SocketEngine.IsolationAppServer.StatusMetadataExtended">
            <summary>
            Gets a value indicating whether [status metadata extended].
            </summary>
            <value>
            <c>true</c> if [status metadata extended]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainAppServer.#ctor(System.String,SuperSocket.SocketBase.Metadata.StatusInfoAttribute[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.AppDomainAppServer"/> class.
            </summary>
            <param name="serverTypeName">Name of the server type.</param>
            <param name="serverStatusMetadata">The server status metadata.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainAppServer.Start">
            <summary>
            Starts this server instance.
            </summary>
            <returns>
            return true if start successfull, else false
            </returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.WorkItemFactoryInfoLoader.ValidateProviderType(System.String)">
            <summary>
            Validates the type of the provider, needn't validate in default mode, because it will be validate later when initializing.
            </summary>
            <param name="typeName">Name of the type.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.WorkItemFactoryInfoLoader.GetServerTypeMetadata(System.String)">
            <summary>
            Gets the app server type's metadata, the return value is not required in this mode.
            </summary>
            <param name="typeName">Name of the type.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.WorkItemFactoryInfoLoader.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.DefaultBootstrap">
            <summary>
            SuperSocket default bootstrap
            </summary>
        </member>
        <member name="F:SuperSocket.SocketEngine.DefaultBootstrap.m_Initialized">
            <summary>
            Indicates whether the bootstrap is initialized
            </summary>
        </member>
        <member name="F:SuperSocket.SocketEngine.DefaultBootstrap.m_Config">
            <summary>
            Global configuration
            </summary>
        </member>
        <member name="F:SuperSocket.SocketEngine.DefaultBootstrap.m_GlobalLog">
            <summary>
            Global log
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.#ctor(System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.IWorkItem})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.DefaultBootstrap"/> class.
            </summary>
            <param name="appServers">The app servers.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.#ctor(SuperSocket.SocketBase.Config.IRootConfig,System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.IWorkItem})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.DefaultBootstrap"/> class.
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="appServers">The app servers.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.#ctor(SuperSocket.SocketBase.Config.IRootConfig,System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.IWorkItem},SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.DefaultBootstrap"/> class.
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="appServers">The app servers.</param>
            <param name="logFactory">The log factory.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.#ctor(SuperSocket.SocketBase.Config.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.DefaultBootstrap"/> class.
            </summary>
            <param name="config">The config.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.#ctor(SuperSocket.SocketBase.Config.IConfigurationSource,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.DefaultBootstrap"/> class.
            </summary>
            <param name="config">The config.</param>
            <param name="startupConfigFile">The startup config file.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.CreateWorkItemInstance(System.String,SuperSocket.SocketBase.Metadata.StatusInfoAttribute[])">
            <summary>
            Creates the work item instance.
            </summary>
            <param name="serviceTypeName">Name of the service type.</param>
            <param name="serverStatusMetadata">The server status metadata.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.GetWorkItemFactoryInfoLoader(SuperSocket.SocketBase.Config.IConfigurationSource,SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Gets the work item factory info loader.
            </summary>
            <param name="config">The config.</param>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Initialize(System.Collections.Generic.IDictionary{System.String,System.Net.IPEndPoint})">
            <summary>
            Initializes the bootstrap with a listen endpoint replacement dictionary
            </summary>
            <param name="listenEndPointReplacement">The listen end point replacement.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Initialize(System.Func{SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Config.IServerConfig},SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes the bootstrap with the configuration, config resolver and log factory.
            </summary>
            <param name="serverConfigResolver">The server config resolver.</param>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Initialize(System.Func{SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Config.IServerConfig})">
            <summary>
            Initializes the bootstrap with the configuration and config resolver.
            </summary>
            <param name="serverConfigResolver">The server config resolver.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Initialize(SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Initialize">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Start">
            <summary>
            Starts this bootstrap.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Stop">
            <summary>
            Stops this bootstrap.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.RegisterRemotingService">
            <summary>
            Registers the bootstrap remoting access service.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.DefaultBootstrap.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.SuperSocket#SocketBase#ILoggerProvider#Logger">
            <summary>
            Gets the bootstrap logger.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.LogFactory">
            <summary>
            Gets the log factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.AppServers">
            <summary>
            Gets all the app servers running in this bootstrap
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.Config">
            <summary>
            Gets the config.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.StartupConfigFile">
            <summary>
            Gets the startup config file.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.PerfMonitor">
            <summary>
            Gets the <see cref="T:SuperSocket.SocketEngine.PerformanceMonitor"/> class.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.DefaultBootstrap.BaseDirectory">
            <summary>
            Gets the base directory.
            </summary>
            <value>
            The base directory.
            </value>
        </member>
        <member name="T:SuperSocket.SocketEngine.AppDomainBootstrap">
            <summary>
            AppDomainBootstrap
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.#ctor(SuperSocket.SocketBase.Config.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.AppDomainBootstrap"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Initialize">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Initialize(System.Func{SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Config.IServerConfig})">
            <summary>
            Initializes the bootstrap with the configuration and config resolver.
            </summary>
            <param name="serverConfigResolver">The server config resolver.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Initialize(SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes the bootstrap with the configuration and config resolver.
            </summary>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Initialize(System.Collections.Generic.IDictionary{System.String,System.Net.IPEndPoint})">
            <summary>
            Initializes the bootstrap with a listen endpoint replacement dictionary
            </summary>
            <param name="listenEndPointReplacement">The listen end point replacement.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Initialize(System.Func{SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Config.IServerConfig},SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <param name="serverConfigResolver">The server config resolver.</param>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Start">
            <summary>
            Starts this bootstrap.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.AppDomainBootstrap.Stop">
            <summary>
            Stops this bootstrap.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.AppDomainBootstrap.AppServers">
            <summary>
            Gets all the app servers running in this bootstrap
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.AppDomainBootstrap.Config">
            <summary>
            Gets the config.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.AppDomainBootstrap.SuperSocket#SocketBase#ILoggerProvider#Logger">
            <summary>
            Gets the bootstrap logger.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.AppDomainBootstrap.StartupConfigFile">
            <summary>
            Gets the startup config file.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.AssemblyImport">
            <summary>
            AssemblyImport, used for importing assembly to the current AppDomain
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.AssemblyImport.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.AssemblyImport"/> class.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.BootstrapFactory">
            <summary>
            Bootstrap Factory
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.BootstrapFactory.CreateBootstrap(SuperSocket.SocketBase.Config.IConfigurationSource)">
            <summary>
            Creates the bootstrap.
            </summary>
            <param name="config">The config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.BootstrapFactory.CreateBootstrap">
            <summary>
            Creates the bootstrap from app configuration's socketServer section.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.BootstrapFactory.CreateBootstrap(System.String)">
            <summary>
            Creates the bootstrap.
            </summary>
            <param name="configSectionName">Name of the config section.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.BootstrapFactory.CreateBootstrapFromConfigFile(System.String)">
            <summary>
            Creates the bootstrap from configuration file.
            </summary>
            <param name="configFile">The configuration file.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketEngine.ConfigurationWatcher">
            <summary>
            The configuration file watcher, it is used for hot configuration updating
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.ConfigurationWatcher.Watch(System.Configuration.ConfigurationSection,SuperSocket.SocketBase.IBootstrap)">
            <summary>
            Watches the specified configuration section.
            </summary>
            <param name="configSection">The configuration section.</param>
            <param name="bootstrap">The bootstrap.</param>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.CommandAssembly">
            <summary>
            Command assembly configuration element
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CommandAssembly.Assembly">
            <summary>
            Gets the assembly name.
            </summary>
            <value>
            The assembly.
            </value>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.CommandAssemblyCollection">
            <summary>
            Command assembly configuation collection
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.Server">
            <summary>
            Server, the port which is compatible with .Net 4.5 or higher
            </summary>
            <summary>
            Server configuration
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.Server.GetChildConfig``1(System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.Server.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Gets a value indicating whether an unknown attribute is encountered during deserialization.
            To keep compatible with old configuration
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns>
            true when an unknown attribute is encountered while deserializing; otherwise, false.
            </returns>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.DefaultCulture">
            <summary>
            Gets/sets the default culture for this server.
            </summary>
            <value>
            The default culture.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ServerTypeName">
            <summary>
            Gets the name of the server type this appServer want to use.
            </summary>
            <value>
            The name of the server type.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ServerType">
            <summary>
            Gets the type definition of the appserver.
            </summary>
            <value>
            The type of the server.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ReceiveFilterFactory">
            <summary>
            Gets the Receive filter factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Ip">
            <summary>
            Gets the ip.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Port">
            <summary>
            Gets the port.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Mode">
            <summary>
            Gets the mode.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Disabled">
            <summary>
            Gets a value indicating whether this <see cref="T:SuperSocket.SocketBase.Config.IServerConfig"/> is disabled.
            </summary>
            <value>
              <c>true</c> if disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.SendTimeOut">
            <summary>
            Gets the send time out.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.MaxConnectionNumber">
            <summary>
            Gets the max connection number.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ReceiveBufferSize">
            <summary>
            Gets the size of the receive buffer.
            </summary>
            <value>
            The size of the receive buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.SendBufferSize">
            <summary>
            Gets the size of the send buffer.
            </summary>
            <value>
            The size of the send buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.SyncSend">
            <summary>
            Gets a value indicating whether sending is in synchronous mode.
            </summary>
            <value>
              <c>true</c> if [sync send]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.LogCommand">
            <summary>
            Gets a value indicating whether log command in log file.
            </summary>
            <value><c>true</c> if log command; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.LogBasicSessionActivity">
            <summary>
            Gets a value indicating whether [log basic session activity like connected and disconnected].
            </summary>
            <value>
            	<c>true</c> if [log basic session activity]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.LogAllSocketException">
            <summary>
            Gets a value indicating whether [log all socket exception].
            </summary>
            <value>
            <c>true</c> if [log all socket exception]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ClearIdleSession">
            <summary>
            Gets a value indicating whether clear idle session.
            </summary>
            <value><c>true</c> if clear idle session; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ClearIdleSessionInterval">
            <summary>
            Gets the clear idle session interval, in seconds.
            </summary>
            <value>The clear idle session interval.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.IdleSessionTimeOut">
            <summary>
            Gets the idle session timeout time length, in seconds.
            </summary>
            <value>The idle session time out.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.CertificateConfig">
            <summary>
            Gets the certificate config.
            </summary>
            <value>The certificate config.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Certificate">
            <summary>
            Gets X509Certificate configuration.
            </summary>
            <value>
            X509Certificate configuration.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Security">
            <summary>
            Gets the security protocol, X509 certificate.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.MaxRequestLength">
            <summary>
            Gets the max allowed length of request.
            </summary>
            <value>
            The max allowed length of request.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.DisableSessionSnapshot">
            <summary>
            Gets a value indicating whether [disable session snapshot]
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.SessionSnapshotInterval">
            <summary>
            Gets the interval to taking snapshot for all live sessions.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ConnectionFilter">
            <summary>
            Gets the connection filters used by this server instance.
            </summary>
            <value>
            The connection filters's name list, seperated by comma
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.CommandLoader">
            <summary>
            Gets the command loader, multiple values should be separated by comma.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.KeepAliveTime">
            <summary>
            Gets the start keep alive time, in seconds
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.KeepAliveInterval">
            <summary>
            Gets the keep alive interval, in seconds.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.ListenBacklog">
            <summary>
            Gets the backlog size of socket listening.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.StartupOrder">
            <summary>
            Gets the startup order of the server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.SendingQueueSize">
            <summary>
            Gets/sets the size of the sending queue.
            </summary>
            <value>
            The size of the sending queue.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.LogFactory">
            <summary>
            Gets the logfactory name of the server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.TextEncoding">
            <summary>
            Gets the default text encoding.
            </summary>
            <value>
            The text encoding.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.Listeners">
            <summary>
            Gets the listeners' configuration.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.SuperSocket#SocketBase#Config#IServerConfig#Listeners">
            <summary>
            Gets the listeners' configuration.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Server.CommandAssemblies">
            <summary>
            Gets the command assemblies configuration.
            </summary>
            <value>
            The command assemblies.
            </value>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.SocketServiceConfig">
            <summary>
            SocketServiceConfig, the part which is compatible with .Net 4.5 or higher
            </summary>
            <summary>
            SuperSocket's root configuration node
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Gets a value indicating whether an unknown element is encountered during deserialization.
            To keep compatible with old configuration
            </summary>
            <param name="elementName">The name of the unknown subelement.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> being used for deserialization.</param>
            <returns>
            true when an unknown element is encountered while deserializing; otherwise, false.
            </returns>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element identified by <paramref name="elementName"/> is locked.- or -One or more of the element's attributes is locked.- or -<paramref name="elementName"/> is unrecognized, or the element has an unrecognized attribute.- or -The element has a Boolean attribute with an invalid value.- or -An attempt was made to deserialize a property more than once.- or -An attempt was made to deserialize a property that is not a valid member of the element.- or -The element cannot contain a CDATA or text element.</exception>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Gets a value indicating whether an unknown attribute is encountered during deserialization.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns>
            true when an unknown attribute is encountered while deserializing; otherwise, false.
            </returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.GetChildConfig``1(System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.DefaultCulture">
            <summary>
            Gets/sets the default culture for all server instances.
            </summary>
            <value>
            The default culture.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.Servers">
            <summary>
            Gets all the server configurations
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.ServerTypes">
            <summary>
            Gets the service configurations
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.ConnectionFilters">
            <summary>
            Gets all the connection filter configurations.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.LogFactories">
            <summary>
            Gets the defined log factory types.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.ReceiveFilterFactories">
            <summary>
            Gets the logfactory name of the bootstrap.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.CommandLoaders">
            <summary>
            Gets the command loaders definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.MaxWorkingThreads">
            <summary>
            Gets the max working threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.MinWorkingThreads">
            <summary>
            Gets the min working threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.MaxCompletionPortThreads">
            <summary>
            Gets the max completion port threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.MinCompletionPortThreads">
            <summary>
            Gets the min completion port threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.PerformanceDataCollectInterval">
            <summary>
            Gets the performance data collect interval, in seconds.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.DisablePerformanceDataCollector">
            <summary>
            Gets a value indicating whether [disable performance data collector].
            </summary>
            <value>
            	<c>true</c> if [disable performance data collector]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.Isolation">
            <summary>
            Gets the isolation mode.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.LogFactory">
            <summary>
            Gets the logfactory name of the bootstrap.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.SocketServiceConfig.OptionElements">
            <summary>
            Gets the option elements.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.IPerformanceMonitor">
            <summary>
            Interface of IPerformanceMonitor
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.IPerformanceMonitor.Start">
            <summary>
            Start PerformanceMonitor.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.IPerformanceMonitor.Stop">
            <summary>
            Stop PerformanceMonitor.
            </summary>
        </member>
        <member name="E:SuperSocket.SocketEngine.IPerformanceMonitor.OnStatusUpdate">
            <summary>
            Invokes when status update.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.IPerformanceMonitor.StatusUpdateInterval">
            <summary>
            Get or Set status update time in seconds.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.IProcessServer">
            <summary>
            the interface for server instance which works as a process
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.IProcessServer.ProcessId">
            <summary>
            Gets the process id.
            </summary>
            <value>
            The process id. If the process id is zero, the server instance is not running
            </value>
        </member>
        <member name="T:SuperSocket.SocketEngine.IRemoteWorkItem">
            <summary>
            IRemoteWorkItem
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.IRemoteWorkItem.Setup(System.String,System.String,System.String,SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Provider.ProviderFactoryInfo[],System.String)">
            <summary>
            Setups the specified config.
            </summary>
            <param name="serverType">Type of the server.</param>
            <param name="bootstrapUri">The bootstrap URI.</param>
            <param name="assemblyImportRoot">The assembly import root.</param>
            <param name="config">The config.</param>
            <param name="factories">The factories.</param>
            <param name="startupConfigFile">The startup configuration file path</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.ProcessAppServer.#ctor(System.String,SuperSocket.SocketBase.Metadata.StatusInfoAttribute[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.ProcessAppServer"/> class.
            </summary>
            <param name="serverTypeName">Name of the server type.</param>
            <param name="serverStatusMetadata">The server status metadata.</param>
        </member>
        <member name="P:SuperSocket.SocketEngine.ProcessAppServer.ProcessId">
            <summary>
            Gets the process id.
            </summary>
            <value>
            The process id. If the process id is zero, the server instance is not running
            </value>
        </member>
        <member name="M:SuperSocket.SocketEngine.ProcessBootstrap.#ctor(SuperSocket.SocketBase.Config.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.ProcessBootstrap"/> class.
            </summary>
            <param name="config">The config.</param>
        </member>
        <member name="T:SuperSocket.SocketEngine.SocketSession">
            <summary>
            Socket Session, all application session should base on this class
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.LogError(System.Exception,System.String,System.String,System.Int32)">
            <summary>
            Logs the error, skip the ignored exception
            </summary>
            <param name="exception">The exception.</param>
            <param name="caller">The caller.</param>
            <param name="callerFilePath">The caller file path.</param>
            <param name="callerLineNumber">The caller line number.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.LogError(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            Logs the error, skip the ignored exception
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="caller">The caller.</param>
            <param name="callerFilePath">The caller file path.</param>
            <param name="callerLineNumber">The caller line number.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.LogError(System.Int32,System.String,System.String,System.Int32)">
            <summary>
            Logs the socket error, skip the ignored error
            </summary>
            <param name="socketErrorCode">The socket error code.</param>
            <param name="caller">The caller.</param>
            <param name="callerFilePath">The caller file path.</param>
            <param name="callerLineNumber">The caller line number.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.Start">
            <summary>
            Starts this session.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.StartSession">
            <summary>
            Says the welcome information when a client connectted.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.OnClosed(SuperSocket.SocketBase.CloseReason)">
            <summary>
            Called when [close].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.TrySend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Tries to send array segment.
            </summary>
            <param name="segments">The segments.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.TrySend(System.ArraySegment{System.Byte})">
            <summary>
            Tries to send array segment.
            </summary>
            <param name="segment">The segment.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.SendAsync(SuperSocket.Common.SendingQueue)">
            <summary>
            Sends in async mode.
            </summary>
            <param name="queue">The queue.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.SendSync(SuperSocket.Common.SendingQueue)">
            <summary>
            Sends in sync mode.
            </summary>
            <param name="queue">The queue.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketSession.ValidateNotInSendingReceiving">
            <summary>
            Validates the socket is not in the sending or receiving operation.
            </summary>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.SessionID">
            <summary>
            Gets or sets the session ID.
            </summary>
            <value>The session ID.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.Config">
            <summary>
            Gets or sets the config.
            </summary>
            <value>
            The config.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.Closed">
            <summary>
            Occurs when [closed].
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.Client">
            <summary>
            Gets or sets the client.
            </summary>
            <value>The client.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.LocalEndPoint">
            <summary>
            Gets the local end point.
            </summary>
            <value>The local end point.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.RemoteEndPoint">
            <summary>
            Gets the remote end point.
            </summary>
            <value>The remote end point.</value>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketSession.SecureProtocol">
            <summary>
            Gets or sets the secure protocol.
            </summary>
            <value>The secure protocol.</value>
        </member>
        <member name="M:SuperSocket.SocketEngine.MarshalAppServer.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.AppDomainAppServer"/> class.
            </summary>
            <param name="serviceTypeName">Name of the service type.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.MarshalAppServer.Setup(SuperSocket.SocketBase.IBootstrap,SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Provider.ProviderFactoryInfo[])">
            <summary>
            Setups the specified root config.
            </summary>
            <param name="bootstrap">The bootstrap.</param>
            <param name="config">The socket server instance config.</param>
            <param name="factories">The providers.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.MarshalAppServer.ReportPotentialConfigChange(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Reports the potential configuration change.
            </summary>
            <param name="config">The server config which may be changed.</param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:SuperSocket.SocketEngine.MarshalAppServer.Start">
            <summary>
            Starts this server instance.
            </summary>
            <returns>
            return true if start successfull, else false
            </returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.MarshalAppServer.Stop">
            <summary>
            Stops this server instance.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.MarshalAppServer.InitializeLifetimeService">
            <summary>
            Obtains a lifetime service object to control the lifetime policy for this instance.
            </summary>
            <returns>
            An object of type <see cref="T:System.Runtime.Remoting.Lifetime.ILease" /> used to control the lifetime policy for this instance. This is the current lifetime service object for this instance if one exists; otherwise, a new lifetime service object initialized to the value of the <see cref="P:System.Runtime.Remoting.Lifetime.LifetimeServices.LeaseManagerPollTime" /> property.
            </returns>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="RemotingConfiguration, Infrastructure" />
              </PermissionSet>
        </member>
        <member name="P:SuperSocket.SocketEngine.MarshalAppServer.Name">
            <summary>
            Gets the name of the server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.MarshalAppServer.Config">
            <summary>
            Gets the server's config.
            </summary>
            <value>
            The server's config.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.MarshalAppServer.State">
            <summary>
            Gets the current state of the work item.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.MarshalAppServer.SessionCount">
            <summary>
            Gets the total session count.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.ISocketListener">
            <summary>
            The interface for socket listener
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.ISocketListener.Start(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Starts to listen
            </summary>
            <param name="config">The server config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.ISocketListener.Stop">
            <summary>
            Stops listening
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.ISocketListener.Info">
            <summary>
            Gets the info of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.ISocketListener.EndPoint">
            <summary>
            Gets the end point the listener is working on
            </summary>
        </member>
        <member name="E:SuperSocket.SocketEngine.ISocketListener.NewClientAccepted">
            <summary>
            Occurs when new client accepted.
            </summary>
        </member>
        <member name="E:SuperSocket.SocketEngine.ISocketListener.Error">
            <summary>
            Occurs when error got.
            </summary>
        </member>
        <member name="E:SuperSocket.SocketEngine.ISocketListener.Stopped">
            <summary>
            Occurs when [stopped].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketListenerBase.Start(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Starts to listen
            </summary>
            <param name="config">The server config.</param>
            <returns></returns>
        </member>
        <member name="E:SuperSocket.SocketEngine.SocketListenerBase.Stopped">
            <summary>
            Occurs when [stopped].
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.TcpAsyncSocketListener">
            <summary>
            Tcp socket listener in async mode
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.TcpAsyncSocketListener.Start(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Starts to listen
            </summary>
            <param name="config">The server config.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketEngine.SocketServerBase.SendingQueuePool">
            <summary>
            Gets the sending queue manager.
            </summary>
            <value>
            The sending queue manager.
            </value>
        </member>
        <member name="T:SuperSocket.SocketEngine.INegotiateSocketSession">
            <summary>
            The interface for socket session which requires negotiation before communication
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.INegotiateSocketSession.Negotiate">
            <summary>
            Start negotiates
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.INegotiateSocketSession.Result">
            <summary>
            Gets a value indicating whether this <see cref="T:SuperSocket.SocketEngine.INegotiateSocketSession"/> is result.
            </summary>
            <value>
              <c>true</c> if result; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.INegotiateSocketSession.AppSession">
            <summary>
            Gets the app session.
            </summary>
            <value>
            The app session.
            </value>
        </member>
        <member name="E:SuperSocket.SocketEngine.INegotiateSocketSession.NegotiateCompleted">
            <summary>
            Occurs when [negotiate completed].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.AsyncStreamSocketSession.Start">
            <summary>
            Starts this session communication.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.CertificateConfig">
            <summary>
            Certificate configuration
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.FilePath">
            <summary>
            Gets the certificate file path.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.Password">
            <summary>
            Gets the password.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.StoreName">
            <summary>
            Gets the the store where certificate locates.
            </summary>
            <value>
            The name of the store.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.StoreLocation">
            <summary>
            Gets the store location of the certificate.
            </summary>
            <value>
            The store location.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.Thumbprint">
            <summary>
            Gets the thumbprint.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.ClientCertificateRequired">
            <summary>
            Gets a value indicating whether [client certificate required].
            </summary>
            <value>
            <c>true</c> if [client certificate required]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.CertificateConfig.KeyStorageFlags">
            <summary>
            Gets a value that will be used to instantiate the X509Certificate2 object in the CertificateManager
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.Listener">
            <summary>
            Listener configuration
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Listener.Ip">
            <summary>
            Gets the ip of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Listener.Port">
            <summary>
            Gets the port of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Listener.Backlog">
            <summary>
            Gets the backlog.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketEngine.Configuration.Listener.Security">
            <summary>
            Gets the security option, None/Default/Tls/Ssl/...
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.ListenerConfigCollection">
            <summary>
            Listener configuration collection
            </summary>
        </member>
        <member name="T:SuperSocket.SocketEngine.Configuration.ServerCollection">
            <summary>
            Server configuration collection
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.ServerCollection.AddNew(SuperSocket.SocketEngine.Configuration.Server)">
            <summary>
            Adds the new server element.
            </summary>
            <param name="newServer">The new server.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.Configuration.ServerCollection.Remove(System.String)">
            <summary>
            Removes the specified server from the configuration.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="T:SuperSocket.SocketEngine.SocketServerFactory">
            <summary>
            Default socket server factory
            </summary>
        </member>
        <member name="M:SuperSocket.SocketEngine.SocketServerFactory.CreateSocketServer``1(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.ListenerInfo[],SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Creates the socket server.
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
            <param name="appServer">The app server.</param>
            <param name="listeners">The listeners.</param>
            <param name="config">The config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.UdpSocketListener.Start(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Starts to listen
            </summary>
            <param name="config">The server config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketEngine.UdpSocketServer`1.#ctor(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.ListenerInfo[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketEngine.UdpSocketServer`1"/> class.
            </summary>
            <param name="appServer">The app server.</param>
            <param name="listeners">The listeners.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.UdpSocketServer`1.OnNewClientAccepted(SuperSocket.SocketEngine.ISocketListener,System.Net.Sockets.Socket,System.Object)">
            <summary>
            Called when [new client accepted].
            </summary>
            <param name="listener">The listener.</param>
            <param name="client">The client.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:SuperSocket.SocketEngine.UdpSocketSession.UpdateRemoteEndPoint(System.Net.IPEndPoint)">
            <summary>
            Updates the remote end point of the client.
            </summary>
            <param name="remoteEndPoint">The remote end point.</param>
        </member>
    </members>
</doc>
