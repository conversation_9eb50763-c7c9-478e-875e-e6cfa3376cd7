<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Expression.Drawing</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Expression.Controls.Callout">
            <summary>
            Renders a callout shape supporting several shapes combined with a callout arrow.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.CompositeContentShape">
             <summary>
             Provides a base class of a composite shape that derives from <see cref="T:System.Windows.Controls.ContentControl"/> and implements <see cref="T:Microsoft.Expression.Media.IShape"/>.
             </summary>
             <remarks>
             <see cref="T:Microsoft.Expression.Controls.CompositeContentShape"/> implements <see cref="T:Microsoft.Expression.Media.IShape"/> interface,
             and supports rendering a geometry similar to <see cref="T:System.Windows.Shapes.Shape"/>, but the geometry can be rendered outside the layout boundary.
             
             A typical implementation has a customized default template in generic.xaml which template-binds most shape properties to a <see cref="T:System.Windows.Shapes.Path"/>.
             It should also extend the <see cref="P:GeometrySource"/> property to customize the appearance of the <see cref="T:System.Windows.Shapes.Path"/>.
            
             This class also supports showing content together with the shape.
             </remarks>
        </member>
        <member name="T:Microsoft.Expression.Media.IGeometrySourceParameters">
            <summary>
            Provides an interface to describe the parameters of a Shape.
            </summary>
            <remarks>
            This interface is the data for communication between Shape and GeometrySource.
            Typically, a concrete implementation of IShape will implement this interface and pass it into
            GeometrySource.UpdateGeometry(), which will then consume the shape as a read-only data provider.
            </remarks>
        </member>
        <member name="T:Microsoft.Expression.Media.IShape">
            <summary>
            Provides the necessary interface to define a Shape.
            Both primitive and composite shapes need to match this interface, although they might derive from different types of FrameworkElement.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.IShape.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Invalidates the geometry for a <see cref="T:Microsoft.Expression.Media.IShape"/>. After the invalidation, the <see cref="T:Microsoft.Expression.Media.IShape"/> will recompute the geometry, which will occur asynchronously.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.IShape.Fill">
            <summary>Gets or sets the <see cref="T:System.Windows.Media.Brush" /> that specifies how to paint the interior of the shape.</summary>
            <returns>A <see cref="T:System.Windows.Media.Brush" /> that describes how the shape's interior is painted. The default is null.</returns>
        </member>
        <member name="P:Microsoft.Expression.Media.IShape.Stroke">
            <summary>Gets or sets the <see cref="T:System.Windows.Media.Brush" /> that specifies how the <see cref="T:System.Windows.Shapes.Shape" /> outline is painted.</summary>
            <returns>A <see cref="T:System.Windows.Media.Brush" /> that specifies how the <see cref="T:System.Windows.Shapes.Shape" /> outline is painted.</returns>
        </member>
        <member name="P:Microsoft.Expression.Media.IShape.StrokeThickness">
            <summary>Gets or sets the width of the <see cref="T:System.Windows.Shapes.Shape" /> stroke outline. </summary>
            <returns>The width of the <see cref="T:System.Windows.Shapes.Shape" /> outline, in pixels.</returns>
        </member>
        <member name="P:Microsoft.Expression.Media.IShape.Stretch">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.Stretch" /> enumeration value that describes how the shape fills its allocated space.</summary>
            <returns>One of the <see cref="T:System.Windows.Media.Stretch" /> enumeration values. The default value at runtime depends on the type of <see cref="T:System.Windows.Shapes.Shape" />.</returns>
        </member>
        <member name="P:Microsoft.Expression.Media.IShape.RenderedGeometry">
            <summary>
            Gets the rendered geometry presented by the rendering engine.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.IShape.GeometryMargin">
            <summary>
            Gets the margin between logical bounds and actual geometry bounds.
            This can be either positive (as in <see cref="T:Microsoft.Expression.Shapes.Arc"/>) or negative (as in <see cref="T:Microsoft.Expression.Controls.Callout"/>).
            </summary>
        </member>
        <member name="E:Microsoft.Expression.Media.IShape.RenderedGeometryChanged">
            <summary>
            Occurs when RenderedGeometry is changed.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeContentShape.CreateGeometrySource">
            <summary>
            Extends how the shape is drawn with creating geometry source.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeContentShape.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Invalidates the geometry for a <see cref="T:Microsoft.Expression.Media.IShape"/>. After the invalidation, the <see cref="T:Microsoft.Expression.Media.IShape"/> will recompute the geometry, which will occur asynchronously.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeContentShape.ArrangeOverride(System.Windows.Size)">
            <summary>Provides the behavior for the Arrange portion of a Silverlight layout pass. Classes can override this method to define their own Arrange pass behavior.</summary>
            <returns>The actual size used once the element is arranged in layout.</returns>
            <param name="finalSize">The final area within the parent that this object should use to arrange itself and its children.</param>
            <remarks> <see cref="T:Microsoft.Expression.Controls.CompositeContentShape"/>  will recompute the Geometry when it's invalidated and update the RenderedGeometry and GeometryMargin.</remarks>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeContentShape.OnContentChanged(System.Object,System.Object)">
            <summary>
            Transforms a string content into <see cref="T:TextBlock"/> with center alignment and multiple line support.
            </summary>
            <remarks>
            Use template-binding to <see cref="P:Microsoft.Expression.Controls.CompositeContentShape.InternalContent"/> instead of <see cref="P:Content"/> to enable this method.
            </remarks>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.Fill">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.Brush"/> that specifies how to paint the interior of the shape.
            </summary>
            <returns>A <see cref="T:System.Windows.Media.Brush"/> that describes how the shape's interior is painted.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.Stroke">
            <summary>Gets or sets the <see cref="T:System.Windows.Media.Brush" /> that specifies how the <see cref="T:System.Windows.Shapes.Shape" /> outline is painted.</summary>
            <returns>A <see cref="T:System.Windows.Media.Brush" /> that specifies how the <see cref="T:System.Windows.Shapes.Shape" /> outline is painted.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeThickness">
            <summary>Gets or sets the width of the <see cref="T:System.Windows.Shapes.Shape" /> stroke outline. </summary>
            <returns>The width of the <see cref="T:System.Windows.Shapes.Shape" /> outline, in pixels.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.Stretch">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.Stretch" /> enumeration value that describes how the shape fills its allocated space.</summary>
            <returns>One of the <see cref="T:System.Windows.Media.Stretch" /> enumeration values.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeStartLineCap">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineCap" /> enumeration value that describes the <see cref="T:System.Windows.Shapes.Shape" /> at the start of a <see cref="P:System.Windows.Shapes.Shape.Stroke" />. </summary>
            <returns>A value of the <see cref="T:System.Windows.Media.PenLineCap" /> enumeration that specifies the shape at the start of a <see cref="P:System.Windows.Shapes.Shape.Stroke" />.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeEndLineCap">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineCap" /> enumeration value that describes the <see cref="T:System.Windows.Shapes.Shape" /> at the end of a line. </summary>
            <returns>One of the enumeration values for <see cref="T:System.Windows.Media.PenLineCap" />. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeLineJoin">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineJoin" /> enumeration value that specifies the type of join that is used at the vertices of a <see cref="T:System.Windows.Shapes.Shape" />.</summary>
            <returns>A value of the <see cref="T:System.Windows.Media.PenLineJoin" /> enumeration that specifies the join appearance. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeMiterLimit">
            <summary>Gets or sets a limit on the ratio of the miter length to half the <see cref="P:System.Windows.Shapes.Shape.StrokeThickness" /> of a <see cref="T:System.Windows.Shapes.Shape" /> element. </summary>
            <returns>The limit on the ratio of the miter length to the <see cref="P:System.Windows.Shapes.Shape.StrokeThickness" /> of a <see cref="T:System.Windows.Shapes.Shape" /> element. This value is always a positive number that is greater than or equal to 1.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeDashArray">
            <summary>Gets or sets a collection of <see cref="T:System.Double" /> values that indicate the pattern of dashes and gaps that is used to outline shapes. </summary>
            <returns>A collection of <see cref="T:System.Double" /> values that specify the pattern of dashes and gaps. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeDashCap">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineCap" /> enumeration value that specifies how the ends of a dash are drawn. </summary>
            <returns>One of the enumeration values for <see cref="T:System.Windows.Media.PenLineCap" />. The default is <see cref="F:System.Windows.Media.PenLineCap.Flat" />. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.StrokeDashOffset">
            <summary>Gets or sets a <see cref="T:System.Double" /> that specifies the distance within the dash pattern where a dash begins.</summary>
            <returns>A <see cref="T:System.Double" /> that represents the distance within the dash pattern where a dash begins. The default value is 0.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.RenderedGeometry">
            <summary>
            Gets the rendered geometry presented by the rendering engine.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.GeometryMargin">
            <summary>
            Gets the margin between the logical bounds and the actual geometry bounds.
            This can be either positive (as in <see cref="T:Microsoft.Expression.Shapes.Arc"/>) or negative (as in <see cref="T:Microsoft.Expression.Controls.Callout"/>).
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeContentShape.InternalContent">
            <summary>
            Gets or sets the internal content that converts a string into a center-aligned, multiple-line TextBlock.
            </summary>
        </member>
        <member name="E:Microsoft.Expression.Controls.CompositeContentShape.RenderedGeometryChanged">
            <summary>
            Occurs when RenderedGeometry is changed.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.Callout.AnchorPoint">
            <summary>
            Gets or sets the position of the callout relative to the top and left corner.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.Callout.CalloutStyle">
            <summary>
            Gets or sets the callout style.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.CompositeShape">
            <summary>
            Provides a base class of a composite shape that derives from <see cref="T:System.Windows.Controls.Control"/> and implements <see cref="T:Microsoft.Expression.Media.IShape"/>.
            </summary>
            <remarks>
            <see cref="T:Microsoft.Expression.Controls.CompositeShape"/> implements <see cref="T:Microsoft.Expression.Media.IShape"/> interface,
            and supports rendering a geometry similar to <see cref="T:System.Windows.Shapes.Shape"/>, but the geometry can be rendered outside the layout boundary.
            
            A typical implementation has a customized default template in generic.xaml which template-binds most shape properties to a <see cref="T:System.Windows.Shapes.Path"/>.
            It should also extend the <see cref="P:GeometrySource"/> property to customize the appearance of the <see cref="T:System.Windows.Shapes.Path"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeShape.CreateGeometrySource">
            <summary>
            Extends how the shape is drawn with creating geometry source.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeShape.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Invalidates the geometry for a <see cref="T:Microsoft.Expression.Media.IShape"/>. After the invalidation, the <see cref="T:Microsoft.Expression.Media.IShape"/> will recompute the geometry, which will occur asynchronously.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.CompositeShape.ArrangeOverride(System.Windows.Size)">
            <summary>Provides the behavior for the Arrange portion of a Silverlight layout pass. Classes can override this method to define their own Arrange pass behavior.</summary>
            <returns>The actual size used once the element is arranged in layout.</returns>
            <param name="finalSize">The final area within the parent that this object should use to arrange itself and its children.</param>
            <remarks> <see cref="T:Microsoft.Expression.Controls.CompositeShape"/>  will recompute the Geometry when it's invalidated and update the RenderedGeometry and GeometryMargin.</remarks>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.Fill">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Media.Brush"/> that specifies how to paint the interior of the shape.
            </summary>
            <returns>A <see cref="T:System.Windows.Media.Brush"/> that describes how the shape's interior is painted.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.Stroke">
            <summary>Gets or sets the <see cref="T:System.Windows.Media.Brush" /> that specifies how the <see cref="T:System.Windows.Shapes.Shape" /> outline is painted.</summary>
            <returns>A <see cref="T:System.Windows.Media.Brush" /> that specifies how the <see cref="T:System.Windows.Shapes.Shape" /> outline is painted.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeThickness">
            <summary>Gets or sets the width of the <see cref="T:System.Windows.Shapes.Shape" /> stroke outline. </summary>
            <returns>The width of the <see cref="T:System.Windows.Shapes.Shape" /> outline, in pixels.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.Stretch">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.Stretch" /> enumeration value that describes how the shape fills its allocated space.</summary>
            <returns>One of the <see cref="T:System.Windows.Media.Stretch" /> enumeration values.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeStartLineCap">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineCap" /> enumeration value that describes the <see cref="T:System.Windows.Shapes.Shape" /> at the start of a <see cref="P:System.Windows.Shapes.Shape.Stroke" />. </summary>
            <returns>A value of the <see cref="T:System.Windows.Media.PenLineCap" /> enumeration that specifies the shape at the start of a <see cref="P:System.Windows.Shapes.Shape.Stroke" />.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeEndLineCap">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineCap" /> enumeration value that describes the <see cref="T:System.Windows.Shapes.Shape" /> at the end of a line. </summary>
            <returns>One of the enumeration values for <see cref="T:System.Windows.Media.PenLineCap" />. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeLineJoin">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineJoin" /> enumeration value that specifies the type of join that is used at the vertices of a <see cref="T:System.Windows.Shapes.Shape" />.</summary>
            <returns>A value of the <see cref="T:System.Windows.Media.PenLineJoin" /> enumeration that specifies the join appearance. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeMiterLimit">
            <summary>Gets or sets a limit on the ratio of the miter length to half the <see cref="P:System.Windows.Shapes.Shape.StrokeThickness" /> of a <see cref="T:System.Windows.Shapes.Shape" /> element. </summary>
            <returns>The limit on the ratio of the miter length to the <see cref="P:System.Windows.Shapes.Shape.StrokeThickness" /> of a <see cref="T:System.Windows.Shapes.Shape" /> element. This value is always a positive number that is greater than or equal to 1.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeDashArray">
            <summary>Gets or sets a collection of <see cref="T:System.Double" /> values that indicate the pattern of dashes and gaps that is used to outline shapes. </summary>
            <returns>A collection of <see cref="T:System.Double" /> values that specify the pattern of dashes and gaps. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeDashCap">
            <summary>Gets or sets a <see cref="T:System.Windows.Media.PenLineCap" /> enumeration value that specifies how the ends of a dash are drawn. </summary>
            <returns>One of the enumeration values for <see cref="T:System.Windows.Media.PenLineCap" />. The default is <see cref="F:System.Windows.Media.PenLineCap.Flat" />. </returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.StrokeDashOffset">
            <summary>Gets or sets a <see cref="T:System.Double" /> that specifies the distance within the dash pattern where a dash begins.</summary>
            <returns>A <see cref="T:System.Double" /> that represents the distance within the dash pattern where a dash begins. The default value is 0.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.RenderedGeometry">
            <summary>
            Gets the rendered geometry presented by the rendering engine.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.CompositeShape.GeometryMargin">
            <summary>
            Gets the margin between the logical bounds and the actual geometry bounds.
            This can be either positive (as in <see cref="T:Microsoft.Expression.Shapes.Arc"/>) or negative (as in <see cref="T:Microsoft.Expression.Controls.Callout"/>).
            </summary>
        </member>
        <member name="E:Microsoft.Expression.Controls.CompositeShape.RenderedGeometryChanged">
            <summary>
            Occurs when RenderedGeometry is changed.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.LineArrow">
            <summary>
            Renders a bent line segment with optional arrow heads on both ends.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.LineArrow.MeasureOverride(System.Windows.Size)">
            <summary>Provides the behavior for the Measure pass of Silverlight layout. Classes can override this method to define their own Measure pass behavior.</summary>
            <returns>The size that this object determines it requires during layout, based on its calculations of child object allotted sizes, or possibly on other considerations such as fixed container size.</returns>
            <param name="availableSize">The available size that this object can give to child objects. Infinity (<see cref="F:System.Double.PositiveInfinity" />) can be specified as a value to indicate that the object will size to whatever content is available.</param>
            <remarks>
            A default <see cref="T:LineArrow"/> can render at anysize.
            The <see cref="P:RenderedGeometry"/> will stretch to the layout boundary and render to the outside if necessary.
            </remarks>
        </member>
        <member name="P:Microsoft.Expression.Controls.LineArrow.BendAmount">
            <summary>
            Gets or sets the amount of bend for the arrow.
            </summary>
            <value>The bend amount between 0 and 1.</value>
        </member>
        <member name="P:Microsoft.Expression.Controls.LineArrow.StartArrow">
            <summary>
            Gets or sets how the arrow head is rendered at the start of the line.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LineArrow.EndArrow">
            <summary>
            Gets or sets how the arrow head is rendered at the end of the line.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LineArrow.StartCorner">
            <summary>
            Gets or sets from which corner to start drawing the arrow.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LineArrow.ArrowSize">
            <summary>
            Gets or sets the length of the arrow in pixels.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PanningItems">
            <summary>
            Provides an items control that displays one selected item, and allows panning between items using touch gestures.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PanningItems.#ctor">
            <summary>
            The constructor for PanningItems.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PanningItems.OnApplyTemplate">
            <summary>
            Called when the PanningItems template is applied.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PanningItems.ScrollDirection">
            <summary>
            Gets or sets the orientation of items in the control.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PanningItems.FlickTolerance">
            <summary>
            Gets or sets the flick tolerance.  This can be a value between 0 and 1.  
            It represents the percentage of the size of the PanningItems needed to be covered by the flick gesture to trigger an items change.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PanningItems.PreviousItem">
            <summary>
            Gets or sets the item before the selected item.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PanningItems.NextItem">
            <summary>
            Gets or sets the item after the selected item.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PanningItems.LoopContents">
            <summary>
            Gets or sets whether the contents of the items control will loop, so that the first item will follow the last item.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PanningItems.SliderValue">
            <summary>
            Gets or sets the value of the slider controlling the panning motion.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PathGeometryHelper">
            <summary>
            Helper class to work with PathGeometry.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathGeometryHelper.ConvertToPathGeometry(System.String)">
            <summary>
            Converts a string in the path mini-language into a PathGeometry.
            </summary>
            <param name="abbreviatedGeometry">A string in the path mini-language.</param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathGeometryHelper.AsPathGeometry(System.Windows.Media.Geometry)">
            <summary>
            Converts the given geometry into a single PathGeometry.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathGeometryHelper.SyncPolylineGeometry(System.Windows.Media.Geometry@,System.Collections.Generic.IList{System.Windows.Point},System.Boolean)">
            <summary>
            Updates the given geometry as PathGeometry with a polyline matching a given point list.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PathGeometryHelper.AbbreviatedGeometryParser">
            <summary>
            Parses abbreviated geometry sytax.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PathSegmentHelper">
            <summary>
            Helper class to convert an ArcSegment to BezierSegment(s).
            </summary>
            <summary>
            Helper class to work with PathSegment and all variations.
            </summary>
            <summary>
            Strategy classes to handle different types of PathSegment.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.ArcToBezierSegments(System.Windows.Media.ArcSegment,System.Windows.Point)">
            <summary>
            Converts an arc segment into Bezier format.
            Returns BezierSegment, PolyBezierSegment, LineSegment, or null.
            When returning null, the arc degenerates into the start point.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.SetIsStroked(System.Windows.Media.PathSegment,System.Boolean)">
            <summary>
            Avoid calling the three-parameter constructor, since it always sets a local value for IsStroked.
            </summary>
            <param name="segment"></param>
            <param name="isStroked"></param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.SyncPolylineSegment(System.Windows.Media.PathSegmentCollection,System.Int32,System.Collections.Generic.IList{System.Windows.Point},System.Int32,System.Int32)">
            <summary>
            Updates the SegmentCollection with a given polyline matching a given point list.
            Tries to keep changes minimum and returns false if nothing has been changed.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.SyncPolyBezierSegment(System.Windows.Media.PathSegmentCollection,System.Int32,System.Collections.Generic.IList{System.Windows.Point},System.Int32,System.Int32)">
            <summary>
            Updates the collection[index] segment with a poly-Bezier segment matching a given point list.
            A given point list must contain 3*N points for N Bezier segments.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.IsEmpty(System.Windows.Media.PathSegment)">
            <summary>
            Tests if a given path segment is empty.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.GetPointCount(System.Windows.Media.PathSegment)">
            <summary>
            Gets the point count in a given path segment.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.GetLastPoint(System.Windows.Media.PathSegment)">
            <summary>
            Gets the last point of a given path segment.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.GetPoint(System.Windows.Media.PathSegment,System.Int32)">
            <summary>
            Gets the point of a given index in a given segment.
            If input is (-1), returns the last point.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.FlattenSegment(System.Windows.Media.PathSegment,System.Collections.Generic.IList{System.Windows.Point},System.Windows.Point,System.Double)">
            <summary>
            Flattens a given segment and adds resulting points into a given point list.
            </summary>
            <param name="segment">The segment to be flatten.</param>
            <param name="points">The resulting points list.</param>
            <param name="start">The start point of the segment.</param>
            <param name="tolerance">The error tolerance. Must be positive. Can be zero. Fallback to default tolerance.</param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.ArcToBezierHelper.ArcToBezier(System.Double,System.Double,System.Double,System.Double,System.Double,System.Boolean,System.Boolean,System.Double,System.Double,System.Windows.Point[]@,System.Int32@)">
            <summary>
            ArcToBezier, computes the Bezier approximation of an arc.
            </summary>
            <remarks>
            This utility computes the Bezier approximation for an elliptical arc as
            it is defined in the SVG arc spec. The ellipse from which the arc is
            carved is axis-aligned in its own coordinates, and defined there by its
            x and y radii. The rotation angle defines how the ellipse's axes are
            rotated relative to the x axis. The start and end points define one of 4
            possible arcs; the sweep and large-arc flags determine which one of
            these arcs will be chosen.
            
            Returning cPieces = 0 indicates a line instead of an arc
                      cPieces = -1 indicates that the arc degenerates to a point 
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.ArcToBezierHelper.GetArcAngle(System.Windows.Point,System.Windows.Point,System.Boolean,System.Boolean,System.Double@,System.Double@,System.Int32@)">
            <summary>
            Gets the number of Bezier arcs, and sine/cosine of each.
            </summary>
            <remarks>
            This is a private utility used by ArcToBezier. Breaks the arc into
            pieces so that no piece will span more than 90 degrees. The input
            points are on the unit circle.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.ArcToBezierHelper.GetBezierDistance(System.Double,System.Double)">
             <summary>
             GetBezierDistance returns the distance as a fraction of the radius.
             </summary>
            <remarks>
                  Get the distance from a circular arc's end points to the control points
                  of the Bezier arc that approximates it, as a fraction of the arc's
                  radius.
            
                  Since the result is relative to the arc's radius, it depends strictly on
                  the arc's angle. The arc is assumed to be of 90 degrees or less, so the
                  angle is determined by the cosine of that angle, which is derived from
                  rDot = the dot product of two radius vectors. We need the Bezier curve
                  that agrees with the arc's points and tangents at the ends and midpoint. 
                  Here we compute the distance from the curve's endpoints to its control
                  points.
            
                  Since we are looking for the relative distance, we can work on the unit
                  circle. Place the center of the circle at the origin, and put the X axis
                  as the bisector between the 2 vectors.  Let a be the angle between the
                  vectors.  Then the X coordinates of the 1st and last points are cos(a/2). 
                  Let x be the X coordinate of the 2nd and 3rd points.  At t=1/2 we have a
                  point at (1,0). But the terms of the polynomial there are all equal:
            
                            (1-t)^3 = t*(1-t)^2 = t^2*(1-t) = t^3 = 1/8,
            
                  so from the Bezier formula there we have:
            
                            1 = (1/8) * (cos(a/2) + 3x + 3x + cos(a/2)), 
            
                  hence
            
                            x = (4 - cos(a/2)) / 3
            
                  The X difference between that and the 1st point is:
                  
                            DX = x - cos(a/2) = 4(1 - cos(a/2)) / 3.
            
                  But DX = distance / sin(a/2), hence the distance is
            
                            dist = (4/3)*(1 - cos(a/2)) / sin(a/2).
            
                  Rather than the angle a, we are given rDot = R^2 * cos(a), so we
                  multiply top and bottom by R:
             
                            dist = (4/3)*(R - Rcos(a/2)) / Rsin(a/2)
             
                  and use some trig:
                                           ________________
                            cos(a/2)   = \/(1 + cos(a)) / 2
                                           ______________________
                            R*cos(a/2) = \/(R^2 + R^2 cos(a)) / 2 
                                           ________________
                                       = \/(R^2 + rDot) / 2
            
                  Let A = (R^2 + rDot)/2.
                                           ____________________
                            R*sin(a/2) = \/R^2 - R^2 cos^2(a/2)
                                           _______
                                       = \/R^2 - A
            
                  so:
                                                      _
                                         4      R - \/A
                                  dist = - * ------------
                                         3      _______
                                              \/R^2 - A
            
              History:
                  5/29/2001 MichKa
                      Created it.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathSegmentHelper.ArcToBezierHelper.AcceptRadius(System.Double,System.Double,System.Double@)">
            <summary>
            Returns false if the radius is too small compared to the chord length (returns true on NaNs)
            radius is modified to the value that is accepted.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.BezierCurveFlattener">
            <summary>
            A utility class to flatten Bezier curves.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.BezierCurveFlattener.FlattenCubic(System.Windows.Point[],System.Double,System.Collections.Generic.ICollection{System.Windows.Point},System.Boolean,System.Collections.Generic.ICollection{System.Double})">
            <summary>
            Flattens a Bezier cubic curve and adds the resulting polyline to the third parameter.
            </summary>
            <param name="controlPoints">The four Bezier cubic control points.</param>
            <param name="errorTolerance">The maximum distance between two corresponding points on the true curve 
            and on the flattened polyline. Must be strictly positive.</param>
            <param name="resultPolyline">Where to add the flattened polyline.</param>
            <param name="skipFirstPoint">True to skip the first control point when adding the flattened polyline.
            <param name="resultParameters">Where to add the value of the Bezier curve parameter associated with 
            each of the polyline vertices.</param> 
            If <paramref name="resultPolyline"/> is empty, the first control point 
            and its associated parameter are always added.</param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.BezierCurveFlattener.FlattenQuadratic(System.Windows.Point[],System.Double,System.Collections.Generic.ICollection{System.Windows.Point},System.Boolean,System.Collections.Generic.ICollection{System.Double})">
            <summary>
            Flattens a Bezier quadratic curve and adds the resulting polyline to the third parameter.
            Uses degree elevation for Bezier curves to reuse the code for the cubic case.
            </summary>
            <param name="controlPoints">The three Bezier quadratic control points.</param>
            <param name="errorTolerance">The maximum distance between two corresponding points on the true curve 
            and on the flattened polyline. Must be strictly positive.</param>
            <param name="resultPolyline">Where to add the flattened polyline.</param>
            <param name="skipFirstPoint">Whether to skip the first control point when adding the flattened polyline. 
            <param name="resultParameters">Where to add the value of the Bezier curve parameter associated with
            each of the polyline vertices.</param>
            If <paramref name="resultPolyline"/> is empty, the first control point and 
            its associated parameter are always added.</param>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.CommonExtensions">
            <summary>
            Extension methods that support non-geometry types.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.ForEach(System.Collections.IEnumerable,System.Action{System.Object})">
            <summary>
            Allows the application of an action delegate (often a very simple lambda) against an entire sequence.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            Allows the application of an action delegate (often a very simple lambda) against an entire sequence.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.ForEach``1(System.Collections.Generic.IList{``0},System.Action{``0,System.Int32})">
            <summary>
            Allows the application of an action delegate (often a very simple lambda) against an entire sequence with the index of each item.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.EnsureListCount``1(System.Collections.Generic.IList{``0},System.Int32,System.Func{``0})">
            <summary>
            Ensures the count of a list to a given count. Creates with a given factory or removes items when necessary.
            If Input IList is a List, AddRange or RemoveRange is used when there's no factory.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.EnsureListCountAtLeast``1(System.Collections.Generic.IList{``0},System.Int32,System.Func{``0})">
            <summary>
            Ensures the count of a list is at least the given count. Creates with a given factory.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.AddRange``1(System.Collections.Generic.ICollection{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Add a range of items to the end of a collection.
            If a collection is a list, List.AddRange is used.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.Last``1(System.Collections.Generic.IList{``0})">
            <summary>
            Gets the last item of a given list.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.RemoveLast``1(System.Collections.Generic.IList{``0})">
            <summary>
            Removes the last item from the given list.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.DeepCopy``1(``0)">
            <summary>
            Makes a copy of obj and all it's public properties, including all collection properties.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.SetIfDifferent(System.Windows.DependencyObject,System.Windows.DependencyProperty,System.Object)">
            <summary>
            Sets the value if different. Avoids setting a local value if possible.
            Returns true when the value has been changed.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.ClearIfSet(System.Windows.DependencyObject,System.Windows.DependencyProperty)">
            <summary>
            Clears the dependency property when it is locally set on the given dependency object.
            Returns false if the dependeny property is not locally set.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.FindVisualDesendent``1(System.Windows.DependencyObject,System.Func{``0,System.Boolean})">
            <summary>
            Finds all visual descendants of a given type and condition using breadth-first search.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.CommonExtensions.GetVisualChildren(System.Windows.DependencyObject)">
            <summary>
            Gets all visual children in IEnumerable.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.DrawingPropertyMetadata">
            <summary>
            Unifies the interface of PropertyMetadata in WPF and Silverlight.
            Provides the necessary notification about render, arrange, or measure.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.DrawingPropertyMetadata.#ctor(Microsoft.Expression.Media.DrawingPropertyMetadataOptions,System.Object)">
            <summary>
            This private Ctor should only be used by AttachCallback.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.DrawingPropertyMetadata.AttachCallback(System.Object,Microsoft.Expression.Media.DrawingPropertyMetadataOptions,System.Windows.PropertyChangedCallback)">
            <summary>
            Chain InternalCallback() to attach the instance of DrawingPropertyMetadata on property callback.
            In Silverlight, the property metadata is thrown away after setting. Use callback to remember it.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.DrawingPropertyMetadata.InternalCallback(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Before chaining the original callback, trigger DrawingPropertyChangedEvent.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.GeometryHelper">
            <summary>
            Extension methods for geometry-related data structures (Point/Vector/Size/Rect).
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Resize(System.Windows.Rect,System.Double)">
            <summary>
            Resizes the rectangle to a relative size while keeping the center invariant.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Subtract(System.Windows.Point,System.Windows.Point)">
            <summary>
            Gets the difference vector between two points.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Plus(System.Windows.Point,System.Windows.Point)">
            <summary>
            Memberwise plus for Point.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Minus(System.Windows.Point,System.Windows.Point)">
            <summary>
            Memberwise minus for Point.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.ConvertToPathGeometry(System.String)">
            <summary>
            Converts a string of mini-languages to a <see cref="T:PathGeometry"/>.
            </summary>
            <remarks>See: Path Markup Syntax(http://msdn.microsoft.com/en-us/library/cc189041(VS.95).aspx)</remarks>
            <param name="abbreviatedGeometry">The string of path mini-languages for describing geometric paths.</param>
            <returns>A <see cref="T:PathGeometry"/> converted from the the path mini-languages.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.FlattenFigure(System.Windows.Media.PathFigure,System.Collections.Generic.IList{System.Windows.Point},System.Double)">
            <summary>
            Flattens a <see cref="T:PathFigure"/> and adds result points to a given <paramref name="points"/>.
            </summary>
            <param name="figure">The input <see cref="T:PathFigure"/>.</param>
            <param name="points">The point list to which result points will append.</param>
            <param name="tolerance">A positive number specifying the maximum allowed error from the result points to the input path figure. A Value of zero allows the algorithm to pick the tolerance automatically.</param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.GetArcPoint(System.Double)">
            <summary>
            Gets the normalized arc in a (0,0)(1,1) box.
            Zero degrees is mapped to [0.5, 0] (up), and clockwise.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.GetArcPoint(System.Double,System.Windows.Rect)">
            <summary>
            Gets the absolute arc point in a given bound with a given relative radius.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.GetArcAngle(System.Windows.Point)">
            <summary>
            Gets the angle on an arc relative to a (0,0)(1,1) box.
            Zero degrees is mapped to [0.5, 0] (up), and clockwise.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.GetArcAngle(System.Windows.Point,System.Windows.Rect)">
            <summary>
            Gets the angle on an arc from a given absolute point relative to a bound.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.RelativeTransform(System.Windows.Rect,System.Windows.Rect)">
            <summary>
            Computes the transform that moves "Rect from" to "Rect to".
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.RelativeTransform(System.Windows.UIElement,System.Windows.UIElement)">
            <summary>
            Computes the transform from the coordinate space of one <c>UIElement</c> to another.
            </summary>
            <param name="from">The source element.</param>
            <param name="to">The destination element.</param>
            <returns>The transform between the <c>UIElement</c>s, or null if it cannot be computed.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.RelativeToAbsolutePoint(System.Windows.Rect,System.Windows.Point)">
            <summary>
            Maps a relative point to an absolute point using the mapping from a given bound to a (0,0)(1,1) box.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.AbsoluteToRelativePoint(System.Windows.Rect,System.Windows.Point)">
            <summary>
            Maps an absolute point to a relative point using the mapping from a (0,0)(1,1) box to a given bound.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.GetStretchBound(System.Windows.Rect,System.Windows.Media.Stretch,System.Windows.Size)">
            <summary>
            Computes the bound after stretching within a given logical bound.
            If stretch to uniform, use given aspectRatio.
            If aspectRatio is empty, it's equivalent to Fill.
            If stretch is None, it's equivalent to Fill or Uniform.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Midpoint(System.Windows.Point,System.Windows.Point)">
            <summary>
            Returns the mid point of two points.
            </summary>
            <param name="lhs">The first point.</param>
            <param name="rhs">The second point.</param>
            <returns>The mid point between <paramref name="lhs"/> and <paramref name="rhs"/>.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Dot(System.Windows.Vector,System.Windows.Vector)">
            <summary>
            Returns the dot product of two vectors.
            </summary>
            <param name="lhs">The first vector.</param>
            <param name="rhs">The second vector.</param>
            <returns>The dot product of <paramref name="lhs"/> and <paramref name="rhs"/>.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Dot(System.Windows.Point,System.Windows.Point)">
            <summary>
            Returns the dot product of two points.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Distance(System.Windows.Point,System.Windows.Point)">
            <summary>
            Returns the distance between two points.
            </summary>
            <param name="lhs">The first point.</param>
            <param name="rhs">The second point.</param>
            <returns>The distance between <paramref name="lhs"/> and <paramref name="rhs"/>.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.SquaredDistance(System.Windows.Point,System.Windows.Point)">
            <summary>
            Returns the square of the distance between two points.
            </summary>
            <param name="lhs">The first point.</param>
            <param name="rhs">The second point.</param>
            <returns>The square of the distance between <paramref name="lhs"/> and <paramref name="rhs"/>.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Determinant(System.Windows.Point,System.Windows.Point)">
            <summary>
            Determinant of the cross product. Equivalent to directional area.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Normal(System.Windows.Point,System.Windows.Point)">
            <summary>
            Computes the normal direction vector of given line segments.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.Perpendicular(System.Windows.Vector)">
            <summary>
            Computes the perpendicular vector, 90-degrees, counter-clockwise.
            Vector to the right perpendicular results in a vector to up.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.GeometryEquals(System.Windows.Media.Geometry,System.Windows.Media.Geometry)">
            <summary>
            Returns whether the two geometries are identical.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.EnsureGeometryType``1(``0@,System.Windows.Media.Geometry@,System.Func{``0})">
            <summary>
            Ensures the value is an instance of result type (T). If not, replace with a new instance of type (T).
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.GeometryHelper.EnsureSegmentType``1(``0@,System.Collections.Generic.IList{System.Windows.Media.PathSegment},System.Int32,System.Func{``0})">
            <summary>
            Ensures the list[index] is an instance of result type (T). If not, replace with a new instance of type (T).
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.MathHelper">
            <summary>
            Helper class that provides static properties and methods related to floating point arithmetic.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Drawing.Core.MathHelper.Epsilon">
            <summary>
            The minimum distance to consider that two values are same.
            Note: internal floating point in MIL/SL is float, not double.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Drawing.Core.MathHelper.TwoPI">
            <summary>
            The value of the angle of a full circle.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Drawing.Core.MathHelper.PentagramInnerRadius">
            <summary>
            The inner radius for a pentagram polygon shape, at precision of three digits in percentage.
            (1 - Sin36 * Sin72 / Sin54) / (Cos36) ^ 2, which is 0.47210998990512996761913067272407
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MathHelper.IsVerySmall(System.Double)">
            <summary>
            Determines whether a <c>System.Double</c> value is small enough to be considered
            equivalent to zero.
            </summary>
            <param name="value"></param>
            <returns><c>True</c> if value is smaller than <c>DoubleTolerance</c>;
            otherwise, <c>False</c>.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MathHelper.EnsureRange(System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Returns the value that's within the given range.
            A given min/max that is null equals no limit.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MathHelper.Hypotenuse(System.Double,System.Double)">
            <summary>
            Computes the Euclidean norm of the vector (x, y).
            </summary>
            <param name="x">The first component.</param>
            <param name="y">The second component.</param>
            <returns>The Euclidean norm of the vector (x, y).</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MathHelper.DoubleFromMantissaAndExponent(System.Double,System.Int32)">
            <summary>
            Computes a real number from the mantissa and exponent.
            </summary>
            <param name="x"></param>
            <param name="exp"></param>
            <returns>The value of x * 2^exp if successful.</returns>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MathHelper.IsFiniteDouble(System.Double)">
            <summary>
            Tests a double.
            </summary>
            <param name="x">The double to test.</param>
            <returns><c>True</c> if x is not a NaN and is not equal to plus or minus infinity;
            otherwise, <c>False</c>.</returns>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PathFigureHelper">
            <summary>
            Helper class to work with PathFigure.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathFigureHelper.FlattenFigure(System.Windows.Media.PathFigure,System.Collections.Generic.IList{System.Windows.Point},System.Double,System.Boolean)">
            <summary>
            Flattens the given figure and adds result points to the given point list.
            </summary>
            <param name="tolerance">The error tolerance. Must be positive. Can be zero. Fallback to default tolerance.</param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathFigureHelper.AllSegments(System.Windows.Media.PathFigure)">
            <summary>
            Iterates all segments inside a given figure, and returns the correct start point for each segment.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathFigureHelper.SyncPolylineFigure(System.Windows.Media.PathFigure,System.Collections.Generic.IList{System.Windows.Point},System.Boolean,System.Boolean)">
            <summary>
            Synchronizes the figure to the given list of points as a single polyline segment.
            Tries to keep the change to a minimum and returns false if nothing has been changed.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PathFigureHelper.SyncEllipseFigure(System.Windows.Media.PathFigure,System.Windows.Rect,System.Windows.Media.SweepDirection,System.Boolean)">
            <summary>
            Synchronizes the given figure to be a closed ellipse with two arc segments.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PathSegmentData">
            <summary>
            A Tuple data structure for PathSegment and the corresponding StartPoint.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PolylineData">
            <summary>
            Represents a polyline with a list of connecting points.
            A closed polygon is represented by repeating the first point at the end.
            The differences, normals, angles, and lengths are computed on demand.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PolylineData.#ctor(System.Collections.Generic.IList{System.Windows.Point})">
            <summary>
            Constructs a polyline with two or more points.
            </summary>
            <param name="points"></param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PolylineData.Difference(System.Int32)">
            The forward difference vector of polyline.
            Points[i] + Differences[i] = Points[i+1]
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PolylineData.SmoothNormal(System.Int32,System.Double,System.Double)">
            <summary>
            Compute the normal vector of given location (lerp(index, index+1, fraction).
            If the location is within range of cornerRadius, interpolate the normal direction.
            </summary>
            <param name="cornerRadius">The range of normal smoothless.  If zero, no smoothness and return the exact normal on index.</param>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.IsClosed">
            <summary>
            The polyline is closed when the first and last points are repeated.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.Count">
            <summary>
            The count of points in this polyline.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.TotalLength">
            <summary>
            The total arc length of this polyline.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.Points">
            <summary>
            The point array of this polyline.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.Lengths">
            <summary>
            The length between line segments, Points[i] to Points[i+1].
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.Normals">
            <summary>
            The list of normal vectors for each segment.
            Normals[i] is the normal of segment p[i] to p[i + 1].
            Normals[N-1] == Normals[N-2].
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.Angles">
            <summary>
            The list of Cos(angle) between two line segments on point p[i].
            Note: The value is cos(angle) = Dot(u, v). Not in degrees.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.PolylineData.AccumulatedLength">
            <summary>
            The list of accumulated length from points[i] to points[0].
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.MarchLocation">
            <summary>
            The data structure to communicate with the PathMarch algorithm.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MarchLocation.GetPoint(System.Collections.Generic.IList{System.Windows.Point})">
            <summary>
            Gets the interpolated position of this MarchLocation on a given point list.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MarchLocation.GetNormal(Microsoft.Expression.Drawing.Core.PolylineData,System.Double)">
            <summary>
            Get the interpolated normal direction of this MarchLocation on a given normal vector list.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.MarchLocation.GetArcLength(System.Collections.Generic.IList{System.Double})">
            <summary>
            Gets the arc length of this MarchLocation to the start of the entire polyline.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.MarchLocation.Reason">
            <summary>
            The reason why this location is sampled.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.MarchLocation.Index">
            <summary>
            The index of the point on a polyline point list.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.MarchLocation.Ratio">
            <summary>
            Ratio: [0, 1], which is always before / (before + after).
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.MarchLocation.Before">
            <summary>
            Arc length before a stop point. Non-negative and less than Length[index].
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.MarchLocation.After">
            <summary>
            Arc length after the stop point. Non-negative and less than Length[index].
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.MarchLocation.Remain">
            <summary>
            Remaining length within a step to hit next stop. Positive to go forward. Negative to go backward.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.PolylineHelper">
            <summary>
            Helper class to work with list of points
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PolylineHelper.PathMarch(Microsoft.Expression.Drawing.Core.PolylineData,System.Double,System.Double,System.Func{Microsoft.Expression.Drawing.Core.MarchLocation,System.Double})">
            <summary>
            March the given polyline with a given interval and output each stop through callback.
            </summary>
            <param name="polyline">The polyline points to march on.</param>
            <param name="startArcLength">The arc length to march before stopping at the first point.</param>
            <param name="cornerThreshold">The max angle between edges to be considered a corner vertex.</param>
            <param name="stopCallback">Callback when marching algorithm stop at a point. The callback returns the arc length for next stop.
            If the asked length is negative, march backwards. If callback returns NaN, finish marching.</param>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.PolylineHelper.GetWrappedPolylines(System.Collections.Generic.IList{Microsoft.Expression.Drawing.Core.PolylineData},System.Double@)">
            <summary>
            Reorders the given list of polylines so that the polyline with a given arc length in the list is the first.
            Polylines that preceded this line are concatenated to the end of the list, with the first polyline at the very end. 
            </summary>
            <param name="lines">A list of polylines.</param>
            <param name="startArcLength">The arc length in the entire list of polylines at which to find the start line.
            The arc length into that line is returned in this variable.</param>
            <returns>The reordered and wrapped list.</returns>
        </member>
        <member name="T:Microsoft.Expression.Drawing.Core.RandomEngine">
            <summary>
            A random generator that supports uniform and Gaussian distributions.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.RandomEngine.Gaussian">
            <summary>
            Generates a pair of independent, standard, normally distributed random numbers,
            zero expectation, unit variance, using polar form of the Box-Muller transformation.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.SimpleSegment.#ctor">
            <summary>
            Private constructor. Force to use factory methods.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.SimpleSegment.Create(System.Windows.Point,System.Windows.Point)">
            <summary>
             Creates a line segment
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.SimpleSegment.Create(System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
             Creates a cubic bezier segment from quatratic curve (3 control points)
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.SimpleSegment.Create(System.Windows.Point,System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
             Creates a cubic bezier segment with 4 control points.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Drawing.Core.SimpleSegment.Points">
            <summary>
            Control points of path segment.  Length is variant.
            Line segment has 2 points, Cubic bezier has 4 points.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Drawing.Core.TransformExtensions.TransformEquals(System.Windows.Media.Transform,System.Windows.Media.Transform)">
            <summary>
            Compares two transforms for an exact match. Transforms with the same value but different structure (e.g. Translate(0,0) and Rotate(0) are not considered equivalent).
            </summary>
            <param name="firstTransform">The first transform.</param>
            <param name="secondTransform">The second transform.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Expression.Media.UnitType">
            <summary>
            Specifies the unit of thickness.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.UnitType.Pixel">
            <summary>
            Unit in pixels.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.UnitType.Percent">
            <summary>
            Unit in percentage relative to the bounding box.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.GeometrySource`1">
            <summary>
            Provides the base class of a source of geometry.
            Generates and caches the geometry based on the input parameters and the layout bounds.
            </summary>
            <remarks>
            A typical implementation will extend the UpdateCachedGeometry() to update this.cachedGeometry.
            This base class will then handle the invalidation, pipeline to the geometry effects, and then cache relative to the layout bounds.
            An implementation should try to reuse the cached geometry as much as possible to avoid reconstruction in the rendering thread.
            An implementation can extend the ComputeLogicalBounds to handle Stretch differently.
            </remarks>
            <typeparam name="TParameters">The type of geometry source parameter on which the base class is working on.</typeparam>
        </member>
        <member name="T:Microsoft.Expression.Media.IGeometrySource">
            <summary>
            Provides an interface to describe the source of a geometry.
            </summary>
            <remarks>
            This interface is designed to expose the geometry source in a non-generic way.
            Typical implementation should subclass GeometrySource instead of implementing this interface directly.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Media.IGeometrySource.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Notifies that the geometry has been invalidated because of external changes.
            </summary>
            <remarks>
            Geometry is typically invalidated when parameters are changed.
            If any geometry has been invalidated externally, the geometry will be recomputed even if the layout bounds change.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Media.IGeometrySource.UpdateGeometry(Microsoft.Expression.Media.IGeometrySourceParameters,System.Windows.Rect)">
            <summary>
            Update the geometry using the given parameters and the layout bounds.
            Returns false if nothing has been updated.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.IGeometrySource.Geometry">
            <summary>
            Gets or sets the resulting geometry after the latest UpdateGeometry().
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.IGeometrySource.LogicalBounds">
            <summary>
            Gets the bounding box where the geometry should stretch to.
            The actual geometry might be smaller or larger than this.
            <see cref="P:Microsoft.Expression.Media.IGeometrySource.LogicalBounds"/> should already take stroke thickness and stretch into consideration.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.IGeometrySource.LayoutBounds">
            <summary>
            Gets the actual bounds of FrameworkElement.
            <see cref="P:Microsoft.Expression.Media.IGeometrySource.LayoutBounds"/> includes logical bounds, stretch, and stroke thickness.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.GeometrySource`1.cachedGeometry">
            <summary>
            Specifics the geometry from the previous geometry effect process.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometrySource`1.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Notifies that the geometry has been invalidated because of external changes.
            </summary>
            <remarks>
            The geometry is typically invalidated when parameters are changed.
            If any geometry has been invalidated externally, the geometry will be recomputed regardless if the layout bounds change.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometrySource`1.UpdateGeometry(Microsoft.Expression.Media.IGeometrySourceParameters,System.Windows.Rect)">
            <summary>
            Update the geometry based on the given parameters and layoutBounds.
            Returns false if the geometry hasn't been changed.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometrySource`1.UpdateCachedGeometry(`0)">
            <summary>
            Extends the way to provide geometry by implementing this function.
            Returns true when any of the geometry is changed.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometrySource`1.ComputeLogicalBounds(System.Windows.Rect,Microsoft.Expression.Media.IGeometrySourceParameters)">
            <summary>
            Extends the way to handle stretch mode.
            The default is to always use Stretch.Fill and center stroke.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometrySource`1.ApplyGeometryEffect(Microsoft.Expression.Media.IGeometrySourceParameters,System.Boolean)">
            <summary>
            Apply the geometry effect when dirty or forced and update this.Geometry.
            Otherwise, keep this.Geometry as this.cachedGeometry.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.GeometrySource`1.Geometry">
            <summary>
            Gets or sets the resulting geometry after the latest UpdateGeometry().
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.GeometrySource`1.LogicalBounds">
            <summary>
            Gets the bounding box that the geometry should stretch to.
            The actual geometry might be smaller or larger than this.
            <see cref="P:Microsoft.Expression.Media.GeometrySource`1.LogicalBounds"/> should already take stroke thickness and stretch into consideration.
            </summary>
            <value></value>
        </member>
        <member name="P:Microsoft.Expression.Media.GeometrySource`1.LayoutBounds">
            <summary>
            Gets the actual bounds of FrameworkElement.
            <see cref="P:Microsoft.Expression.Media.GeometrySource`1.LayoutBounds"/> includes logical bounds, stretch and stroke thickness.
            </summary>
            <value></value>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.ComputeLogicalBounds(System.Windows.Rect,Microsoft.Expression.Media.IGeometrySourceParameters)">
            <summary>
            Arc recognizes Stretch.None as the same as Stretch.Fill, assuming aspect ratio = 1:1.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.NormalizeThickness(Microsoft.Expression.Media.IArcGeometrySourceParameters)">
            <summary>
            Normalize thickness, both relative to the bounding box and the absolute pixel.
            Relative thickness = 0 -> full circle radius or clamped.
            Relative thickness = 1 -> shrank to a dot, or degenerated.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.UpdateZeroAngleGeometry(System.Boolean,System.Double)">
            <summary>
            The arc is degenerated to a line pointing to center / normal inward.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.ComputeAngleRanges(System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Compute a list of angle pairs, defining the ranges in which arc sample should locate.
            The return value have 2, 4, or 6 double values, each pair defines a range and they are in the order
            to span the angles from given start to end angles.  The ranges will break at the self-intersect angle.
            If input start/end are within the invalid range between self intersect angle, it will be moved to neighboring self intersect.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.EnsureFirstQuadrant(System.Double)">
            <summary>
            Move angle to 0-90 range.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.SyncPieceWiseInnerCurves(System.Windows.Media.PathFigure,System.Int32,System.Windows.Point@,System.Double[])">
            <summary>
            Compute all pieces of inner curves with each pair of input angles, and connect them with poly Bezier segments.
            The new segments are output to given figure.Segments list from the given index.  The start point is output seperately.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.ComputeOneInnerCurve(System.Double,System.Double,System.Windows.Rect,System.Double)">
            <summary>
            Compute one piece of inner curve with given angle range, and output one piece of smooth curve in format of poly Beizer semgents.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.ArcGeometrySource.InnerCurveSelfIntersect(System.Double,System.Double,System.Double)">
            <summary>
            Compute the parameter (angle) of the self-intersect point for given ellipse with given thickness.
            The result is always in first quadrant, and might be 0 or 90 indicating no self-intersect.
            Basic algorithm is to binary search for the angle that sample point is not in first quadrant.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.ArrowOrientation">
            <summary>
            Specifies the direction the arrow points.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowOrientation.Left">
            <summary>
            The arrow points to the left.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowOrientation.Right">
            <summary>
            The arrow points to the right.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowOrientation.Up">
            <summary>
            The arrow points up.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowOrientation.Down">
            <summary>
            The arrow points down.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.BlockArrowGeometrySource.ArrowBuilder">
            <summary>
                 B
                /|
               / C--D
              A     |
               \ C--D
                \|
                 B
            Algorithm only uses Width/Height assuming top-left at 0,0.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.CalloutStyle">
            <summary>
            Specifies the rendering style of a callout shape.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CalloutStyle.Rectangle">
            <summary>
            A rectangular callout.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CalloutStyle.RoundedRectangle">
            <summary>
            A rectangular callout with rounded corners.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CalloutStyle.Oval">
            <summary>
            A oval-shaped callout.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CalloutStyle.Cloud">
            <summary>
            A cloud-shaped callout.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.CalloutGeometrySource.UpdateEdge(System.Windows.Media.PathSegmentCollection,System.Int32,System.Windows.Point,System.Windows.Point,System.Windows.Point,System.Double,System.Boolean)">
            <summary>
            Updates the edge line, and then connects to the anchor point if necessary.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.CalloutGeometrySource.UpdatePolylineSegment(System.Windows.Media.PathSegmentCollection,System.Int32,System.Windows.Point,System.Windows.Point,System.Windows.Point,System.Double)">
            <summary>
            Updates the polyline segment, and then connects start, anchor, and end points with the callout style.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.CalloutGeometrySource.UpdateLineSegment(System.Windows.Media.PathSegmentCollection,System.Int32,System.Windows.Point)">
            <summary>
            Updates the line segment to a given point.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.CalloutGeometrySource.ComputeCorners(System.Double)">
            <summary>
            Computes the corner points in a clockwise direction, with eight points for the four corners.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.CalloutGeometrySource.UpdateCornerArc(System.Windows.Media.PathSegmentCollection,System.Int32,System.Windows.Point,System.Windows.Point)">
            <summary>
            The corner arc is always smaller than a 90-degree arc.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.GeometryEffect">
            <summary>
            Provides the base class for GeometryEffect that transforms a geometry into another geometry.
            </summary>
            <remarks>
            This class provides the basic implementation of processing the rendered geometry of a IShape before it's passed to rendering.
            A typical implementation will extend the virtual function <see cref="F:ProcessGeometry"/> to transform the input geometry.
            <see cref="T:GeometryEffect"/> is typically attached to <see cref="T:IShape"/> as an attached property and activated when <see cref="T:IShape"/> geometry is updated.
            The <see cref="P:OutputGeometry"/> of a <see cref="T:GeometryEffect"/> will replace the rendered geometry in <see cref="T:IShape"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.GetGeometryEffect(System.Windows.DependencyObject)">
            <summary>
            Gets the geometry effect as an attached property on a given dependency object.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.SetGeometryEffect(System.Windows.DependencyObject,Microsoft.Expression.Media.GeometryEffect)">
            <summary>
            Sets the geometry effect as an attached property on a given dependency object.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.CloneCurrentValue">
            <summary>
            Makes a deep copy of the <see cref="T:GeometryEffect"/> using its current values.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.DeepCopy">
            <summary>
            Makes a deep copy of the geometry effect. Implements CloneCurrentValue in Silverlight.
            </summary>
            <returns>A clone of the current instance of the geometry effect.</returns>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.Equals(Microsoft.Expression.Media.GeometryEffect)">
            <summary>
            Tests if the given geometry effect is equivalent to the current instance.
            </summary>
            <param name="geometryEffect">A geometry effect to compare with.</param>
            <returns>Returns true when two effects render with the same appearance.</returns>
        </member>
        <member name="F:Microsoft.Expression.Media.GeometryEffect.cachedGeometry">
            <summary>
            Specifics the geometry from the previous geometry effect process.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Invalidates the geometry effect without actually computing the geometry.
            Notifies all parent shapes or effects to invalidate accordingly.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.ProcessGeometry(System.Windows.Media.Geometry)">
            <summary>
            Processes the geometry effect on a given input geometry.
            Stores the result in GeometryEffect.OutputGeometry.
            </summary>
            <returns>Returns false if nothing has been changed.</returns>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.UpdateCachedGeometry(System.Windows.Media.Geometry)">
            <summary>
            Extends the way of updating cachedGeometry based on a given input geometry.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.Detach">
            <summary>
            Notified when detached from a parent chain.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.Attach(System.Windows.DependencyObject)">
            <summary>
            Notified when attached to a parent chain.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.InvalidateParent(System.Windows.DependencyObject)">
            <summary>
            Invalidates the geometry on a given dependency object when
            the object is a valid parent type (IShape or GeometryEffect).
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffect.CreateInstanceCore">
            <summary>
            Implement the Freezable in WPF.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.GeometryEffect.DefaultGeometryEffect">
            <summary>
            The default geometry effect that only passes through the input geometry.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.GeometryEffect.OutputGeometry">
            <summary>
            Gets the output geometry of this geometry effect.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Media.GeometryEffect.Parent">
            <summary>
            Parent can be either IShape or GeometryEffectGroup.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.GeometryEffectConverter">
            <summary>
            Provides the conversion between string and geometry effects.
            </summary>
            <remarks>
            This class enables the brief syntax in XAML like <code>GeometryEffect="Sketch"</code>.
            Creates a clone of the instance of the geometry effect so it can be used as a resource.
            </remarks>
        </member>
        <member name="F:Microsoft.Expression.Media.GeometryEffectConverter.registeredEffects">
            <summary>
            Builds a preset list of supported geometry effects.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffectConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            A GeometryEffect that can be converted from a string type.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffectConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            A GeometryEffect that can be converted to a string type.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffectConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts a string to a geometry effect. The fallback value is null.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.GeometryEffectConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts a geometry effect into a string.  The fallback value is null.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.InvalidateGeometryReasons">
            <summary>
            Specifies the reason of <see cref="M:Microsoft.Expression.Media.InvalidateGeometry"/> being called.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.InvalidateGeometryReasons.PropertyChanged">
            <summary>
            Geometry has been invalidated because a property has been changed.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.InvalidateGeometryReasons.IsAnimated">
            <summary>
            Geometry has been invalidated because a property is being animated.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.InvalidateGeometryReasons.ChildInvalidated">
            <summary>
            Geometry has been invalidated because a child has been invalidated.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.InvalidateGeometryReasons.ParentInvalidated">
            <summary>
            Geometry has been invalidated because a parent has been invalidated.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.InvalidateGeometryReasons.TemplateChanged">
            <summary>
            Geometry has been invalidated because a new template has been applied.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.IGeometrySourceExtensions">
            <summary>
            Provides helper extension methods to work with IGeometrySource and parameters.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.ArrowType">
            <summary>
            Specifies the arrow head type.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowType.NoArrow">
            <summary>
            No arrow head.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowType.Arrow">
            <summary>
            A triangle arrow head.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowType.StealthArrow">
            <summary>
            A stealth triangle arrow head.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowType.OpenArrow">
            <summary>
            An open triangle arrow head.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.ArrowType.OvalArrow">
            <summary>
            An oval arrow head.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.CornerType">
            <summary>
            Specifies the corner location.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CornerType.TopLeft">
            <summary>
            On the top left of the bounding box.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CornerType.TopRight">
            <summary>
            On the top right of the bounding box.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CornerType.BottomRight">
            <summary>
            On the bottom right of the bounding box.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Media.CornerType.BottomLeft">
            <summary>
            On the bottom left of the bounding box.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.PolygonGeometrySource.ComputeLogicalBounds(System.Windows.Rect,Microsoft.Expression.Media.IGeometrySourceParameters)">
            <summary>
            Polygon recognizes Stretch.None as the same as Stretch.Fill.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Media.SketchGeometryEffect">
            <summary>
            A geometry effect that transforms any geometry into a Sketch style as in SketchFlow.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.SketchGeometryEffect.DeepCopy">
            <summary>
            Makes a deep copy of the geometry effect.
            </summary>
            <returns>A clone of the current instance of the geometry effect.</returns>
        </member>
        <member name="M:Microsoft.Expression.Media.SketchGeometryEffect.Equals(Microsoft.Expression.Media.GeometryEffect)">
            <summary>
            Tests if the given geometry effect is equivalent to the current instance.
            </summary>
            <param name="geometryEffect">A geometry effect to compare with.</param>
            <returns>Returns true when two effects render with the same appearance.</returns>
        </member>
        <member name="M:Microsoft.Expression.Media.SketchGeometryEffect.UpdateCachedGeometry(System.Windows.Media.Geometry)">
            <summary>
            Updating cachedGeometry based on the given input geometry.
            </summary>
            <param name="input">An input geometry.</param>
            <returns>Returns true when anything on cachedGeometry has been updated.</returns>
        </member>
        <member name="F:Microsoft.Expression.Media.SketchGeometryEffect.randomSeed">
            <summary>
            Use the same random seed on creation to keep visual flickering to a minimum.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Media.SketchGeometryEffect.GetEffectiveSegments(System.Windows.Media.PathFigure)">
            <summary>
            Iterates all simple segments in given path figure including the closing chord.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Shapes.Arc">
            <summary>
            Renders an arc shape supporting Arc, Ring, and Pie mode controlled by ArcThickness.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Shapes.PrimitiveShape">
            <summary>
            Platform-neutral implementation of Shape deriving from WPF:Shape or SL:Path.
            </summary>
            <summary>
            Provides the WPF implementation of Shape that derives from the platform Shape.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Shapes.PrimitiveShape.CreateGeometrySource">
            <summary>
            Extends how the shape is drawn with creating geometry source.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Shapes.PrimitiveShape.InvalidateGeometry(Microsoft.Expression.Media.InvalidateGeometryReasons)">
            <summary>
            Invalidates the geometry for a <see cref="T:Microsoft.Expression.Media.IShape"/>. After the invalidation, the <see cref="T:Microsoft.Expression.Media.IShape"/> will recompute the geometry, which will occur asynchronously.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Shapes.PrimitiveShape.MeasureOverride(System.Windows.Size)">
            <summary>Provides the behavior for the Measure portion of Silverlight layout pass. Classes can override this method to define their own Measure pass behavior.</summary>
            <returns>The size that this object determines it requires during layout, based on its calculations of child object allotted sizes, or possibly on other considerations such as fixed container size.</returns>
            <param name="availableSize">The available size that this object can provide to child objects. Infinity (<see cref="F:System.Double.PositiveInfinity" />) can be specified as a value to indicate that the object will size to whatever content is available.</param>
            <remarks>
            In WPF, measure override works from Shape.DefiningGeometry which is not always as expected
            see bug 99497 for details where WPF is not having correct measure by default.
            
            In Silverlight, measure override on Path does not work the same as primitive shape works.
            
            We should return the smallest size this shape can correctly render without clipping.
            By default a shape can render as small as a dot, therefore returning the strokethickness.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Shapes.PrimitiveShape.ArrangeOverride(System.Windows.Size)">
            <summary>Provides the behavior for the Arrange portion of Silverlight layout pass. Classes can override this method to define their own Arrange pass behavior.</summary>
            <returns>The actual size used once the element is arranged in layout.</returns>
            <param name="finalSize">The final area within the parent that this object should use to arrange itself and its children.</param>
            <remarks> <see cref="T:Microsoft.Expression.Shapes.PrimitiveShape"/>  will recompute the Geometry when it's invalidated and update the RenderedGeometry and GeometryMargin.</remarks>
        </member>
        <member name="E:Microsoft.Expression.Shapes.PrimitiveShape.RenderedGeometryChanged">
            <summary>
            Occurs when RenderedGeometry is changed.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Shapes.PrimitiveShape.GeometryMargin">
            <summary>
            Gets the margin between logical bounds and actual geometry bounds.
            This can be either positive (as in <see cref="T:Microsoft.Expression.Shapes.Arc"/>) or negative (as in <see cref="T:Microsoft.Expression.Controls.Callout"/>).
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Shapes.Arc.StartAngle">
            <summary>
            Gets or sets the start angle.
            </summary>
            <value>The start angle in degrees. Zero degrees is pointing up.</value>
        </member>
        <member name="P:Microsoft.Expression.Shapes.Arc.EndAngle">
            <summary>
            Gets or sets the end angle.
            </summary>
            <value>The end angle in degrees. Zero degrees is pointing up.</value>
        </member>
        <member name="P:Microsoft.Expression.Shapes.Arc.ArcThickness">
            <summary>
            Gets or sets the arc thickness.
            </summary>
            <value>The arc thickness in pixels or percentage depending on "ArcThicknessUnit".</value>
        </member>
        <member name="P:Microsoft.Expression.Shapes.Arc.ArcThicknessUnit">
            <summary>
            Gets or sets the arc thickness unit.
            </summary>
            <value>The arc thickness unit in pixels or percentage.</value>
        </member>
        <member name="T:Microsoft.Expression.Shapes.BlockArrow">
            <summary>
            Renders a block arrow shape that supports resizable arrow head and body.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Shapes.BlockArrow.Orientation">
            <summary>
            Gets or sets the orientation.
            </summary>
            <value>The orientation where the arrow is pointing to.</value>
        </member>
        <member name="P:Microsoft.Expression.Shapes.BlockArrow.ArrowheadAngle">
            <summary>
            Gets or sets the arrow head angle.
            </summary>
            <value>The arrow head angle in degrees.</value>
        </member>
        <member name="P:Microsoft.Expression.Shapes.BlockArrow.ArrowBodySize">
            <summary>
            Gets or sets the size of the arrow body.
            </summary>
            <value>The size of the arrow body in pixels.</value>
        </member>
        <member name="T:Microsoft.Expression.Shapes.RegularPolygon">
            <summary>
            Renders a regular polygon shape or corresponding star shape with variable number of points.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Shapes.RegularPolygon.PointCount">
            <summary>
            Gets or sets the number of points of the <see cref="T:Microsoft.Expression.Shapes.RegularPolygon"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Shapes.RegularPolygon.InnerRadius">
            <summary>
            Gets or sets the the distance between the center and the innermost point.
            </summary>
            <value>The distance between the center and the innermost point.</value>
        </member>
    </members>
</doc>
