# 云深处科技 Lite3小狗 MES生产管理系统

## 系统概述

云深处科技 Lite3小狗 MES生产管理系统是一个专门为 Lite3 小狗产品设计的制造执行系统（MES），集成了二维码追溯、工序管理、质量控制、生产监控等功能，实现了从生产装配到发货的全流程数字化管理。

## 核心功能

### 1. 工序流程管理
- **装配工序**: 产品装配完成后分配序号，生成二维码
- **标零操作**: 一测区域标零操作
- **一测流程**: 第一次测试，质量检测和结果记录
- **维护工序**: 对一测产品进行维护和调整
- **二测流程**: 第二次测试，最终质量确认
- **打包发货**: 产品打包和发货管理

### 2. 二维码管理
- **二维码生成**: 自动生成包含产品信息的二维码
- **二维码扫描**: 支持摄像头实时扫描和文件扫描
- **追溯管理**: 基于二维码的完整产品追溯链

### 3. 质量管理
- **质量检测**: 多参数质量测试和结果记录
- **质量分析**: 质量趋势分析和缺陷统计
- **质量报告**: 自动生成质量分析报告

### 4. 生产监控
- **实时状态**: 实时监控所有产品的生产状态
- **进度跟踪**: 工序进度和完成率统计
- **异常告警**: 超时、质量异常等告警机制

### 5. 报表统计
- **日报表**: 日生产统计和质量分析
- **周报表**: 周生产趋势和效率对比
- **月报表**: 月度生产分析和设备利用率
- **追溯报告**: 单个产品的完整追溯报告

## 技术架构

### 开发环境
- **开发语言**: C# (.NET Framework 4.8)
- **UI框架**: WPF (Windows Presentation Foundation)
- **数据库**: SQL Server
- **二维码库**: ZXing.NET
- **摄像头**: AForge.NET
- **报表**: 自定义报表引擎

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层    │    │   业务逻辑层    │    │   数据访问层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ MainWindow      │    │ ProcessFlow     │    │ SQL Server      │
│ QRCodeScan      │    │ QRCodeManager   │    │ Database        │
│ TestWindow      │    │ Traceability    │    │ Tables          │
│ ReportWindow    │    │ ReportManager   │    │ Views           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据库设计
主要数据表：
- **Products**: 产品主表
- **ProcessDefinitions**: 工序定义表
- **ProcessRecords**: 工序记录表
- **QualityTests**: 质量检测表
- **ProductTraceability**: 产品追溯表
- **ScanRecords**: 扫描记录表
- **ShippingInfo**: 发货信息表

## 安装部署

### 系统要求
- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **数据库**: SQL Server 2016+
- **.NET Framework**: 4.8
- **内存**: 最低 4GB，推荐 8GB+
- **硬盘**: 最低 10GB 可用空间
- **摄像头**: USB 摄像头（用于二维码扫描）

### 安装步骤

1. **数据库初始化**
   ```sql
   -- 运行数据库脚本
   sqlcmd -S server_name -d master -i Database/Lite3DogMES_Database.sql
   ```

2. **配置连接字符串**
   ```xml
   <!-- 修改 App.config 中的连接字符串 -->
   <connectionStrings>
     <add name="Lite3DogMES" 
          connectionString="Server=your_server;Database=Lite3DogMES;Integrated Security=true;" />
   </connectionStrings>
   ```

3. **运行应用程序**
   ```bash
   # 正常启动
   Lite3DogMES.exe
   
   # 测试模式
   Lite3DogMES.exe /test
   ```

## 使用指南

### 基本操作流程

1. **启动系统**
   - 运行 Lite3DogMES.exe
   - 系统自动检查数据库连接和初始化

2. **产品生产流程**
   ```
   装配 → 扫码确认 → 标零 → 一测 → 维护 → 二测 → 打包 → 发货
   ```

3. **工序操作**
   - 在工序管理界面输入产品序号
   - 扫描二维码确认产品
   - 执行相应工序操作
   - 记录质量检测结果

4. **追溯查询**
   - 在产品追溯界面输入产品序号
   - 查看完整的生产历史和质量记录
   - 生成追溯报告

### 二维码扫描
- 点击"扫描二维码"按钮
- 选择摄像头设备
- 将二维码对准摄像头
- 系统自动识别并提取产品信息

### 报表查看
- 选择报表类型（日报、周报、月报）
- 设置查询时间范围
- 生成并查看报表
- 支持导出为 Excel 或 PDF

## 系统配置

### 主要配置项
```xml
<!-- 二维码设置 -->
<add key="QRCodeWidth" value="300" />
<add key="QRCodeHeight" value="300" />

<!-- 工序超时设置 -->
<add key="ProcessTimeoutWarningMinutes" value="30" />
<add key="ProcessTimeoutErrorMinutes" value="60" />

<!-- 质量控制设置 -->
<add key="QualityPassThreshold" value="80" />

<!-- 备份设置 -->
<add key="AutoBackupEnabled" value="true" />
<add key="BackupIntervalHours" value="24" />
```

## 系统测试

### 运行测试
```bash
# 运行完整系统测试
Lite3DogMES.exe /test
```

### 测试内容
- 数据库连接测试
- 二维码生成和扫描测试
- 产品创建测试
- 工序流程测试
- 追溯功能测试
- 报表生成测试
- 性能测试

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 SQL Server 服务是否启动
   - 验证连接字符串配置
   - 确认数据库权限设置

2. **二维码扫描失败**
   - 检查摄像头设备连接
   - 确认摄像头驱动正常
   - 调整二维码扫描距离和角度

3. **工序操作异常**
   - 检查产品序号是否正确
   - 确认工序顺序是否正确
   - 查看系统日志获取详细错误信息

### 日志查看
- 日志文件位置: `Logs/MES_yyyyMMdd.log`
- 系统追踪日志: `Logs/trace.log`

## 维护指南

### 定期维护
- **数据库备份**: 系统自动每24小时备份一次
- **日志清理**: 自动清理30天前的日志文件
- **性能优化**: 系统自动在凌晨2点执行数据库优化

### 手动维护
```sql
-- 手动备份数据库
BACKUP DATABASE Lite3DogMES TO DISK = 'C:\Backup\Lite3DogMES.bak'

-- 清理测试数据
DELETE FROM Products WHERE ProductSN LIKE '%TEST_%'

-- 更新统计信息
UPDATE STATISTICS Products
UPDATE STATISTICS ProcessRecords
```

## 版本信息

- **当前版本**: v1.0.0
- **发布日期**: 2024年1月
- **开发公司**: 云深处科技
- **技术支持**: <EMAIL>

## 许可证

本软件为云深处科技专有软件，受版权法保护。未经授权不得复制、分发或修改。

---

© 2024 云深处科技. 保留所有权利。
