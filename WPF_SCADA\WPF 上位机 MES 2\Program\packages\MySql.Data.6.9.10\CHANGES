6.9.10
- Added user contribution in bug MySql.Web.Security.CreateUserInUserTable does not work (MySQL Bug #83656, Oracle Bug # 25046364).
- Fixed incorrect encoding in connection passwords (MySQL Bug #80611, Oracle Bug #23257011).


6.9.9
- Fix for Nested transactions are not supported in Entity Framework (MySQL Bug #71502, Oracle Bug #22366266).
- Fix for EF5 and EF6 wrong SQL statement to set primary key (MySQL Bug 76292, Oracle Bug #20711384) 
- Fixed problem where mysql.proc tables would not be used for metadata even though access was available (MySQL Bug #74116, Oracle Bug #20960373)
- Added support for TLSv1.1 and TLSv1.2
- Fixed Fix for "Aborted connection" (MySQL Bug #80997, Oracle Bug # 23346197).


6.9.8
- Added support for Chinese character set gb18030. (Oracle bug # 21098546).
- Added support for Json type. (WL # 8132).
- Added changes for metadata information in generated columns with 5.7 (WL #411)

6.9.7
- Changed default SSL mode to Preferred in connection string. Now the server connections will be using SSL if server allows it by default but it's possible to override this configuration.
- Changed handshake process to use bytes instead of encoded strings.
- Fix for Fabric connections (Oracle Bug #20983968).
- Fix for Fabric plugin: fabric server selection is broken when master is faulty (Oracle Bug #21203824).


6.9.6
- Fix for Incorrect query result with Entity Framework 6 (MySql bug #74918, Oracle bug #20129927).
- Fix for GetTimeZoneOffset to use date and time to calculate timediff (MySQL Bug #74905, Oracle Bug #20065691).
- Fix for MySqlSimpleMembershipProvider keeps database connections open on some operations (MySQL Bug #74662, Oracle Bug #20109419)
- Fix for Any Call to RoleExists() returns true whether or not the role exists (MySql bug #75397, Oracle bug #20325391).
- Fix for all dateTimes set as UTC Kind (MySQL Bug #74134, Oracle Bug #20200662).
- Fix for Invalid SQL query when eager loading two nested collections (MySQL Bug #70941, Oracle bug #18049862).
- Fix for chinese characters used in the connection string when connecting to MySql Server (MySQL Bug #70041, Oracle Bug #18141356).



6.9.5
- Disabled installation on-demand in Installer (Oracle Bug #19670596).
- Fix for Generated SQL requests column that doesn't exist in LINQ to Entities (MySql bug #72004, Oracle bug #19681348).
- Fix for MySQL Connector/NET generates incorrect SQL for LINQ 'StartsWith' queries (MySql bug #72058, Oracle bug #19680236).
- Fix for Exception when using IEnumerable.Contains(model.property) in Where predicate (MySql bug #73643, Oracle bug #19690370).
- Fix for Generated Sql does not contain ORDER BY statement whose is requested by LINQ (MySql bug #73549, Oracle bug #19698010).
- Fix for Web providers registration in machine.config (removed v20 suffix) (MySQL Bug #74080, Oracle Bug #19715398)
- Fix for Error of "Every derived table must have an alias" in LINQ to Entities when using EF6 + DbFirst + View + Take
  (MySql Bug #72148, Oracle bug #19356006).
- Fix for 'the method or operation is not implemented' when using linq with orderby (MySQL Bug #70722, Oracle Bug #19681723).
- Fix for Exception "The given key was not present in the dictionary" when using utf16le charset in a query. (MySql #72737, Oracle Bug #19355906)
- Fix for Memory leak in a loop opening a connection to the database and executing a command (MySql Bug #73122, Oracle Bug #19467233).
- Fix for Multiple issues caused by trailing and leading white space character in params using MySql Membership Provider (MySql Bug #73411, Oracle Bug #19453313)
- Fix for bad assumption leads to modify query adding CALL statement to the beginning of the sql query even when CommandType.Text is specified (MySql Bug #72736, Oracle Bug #19325120).


6.9.4
- Added a new plugin for MySql Fabric 1.5 support


6.9.3
- Fix for Web Parts Personalization provider
- Fix for changing the PK between two int columns (MySql Bug #71418, Oracle bug #18923294).
- Fix for Error when Calling MySqlConnection.GetSchema("PROCEDURES WITH PARAMETERS", ...) (Oracle bug #19285959).
- Fix for EF provider reports ManifestProviderToken = 5.6 for server 5.7 (Oracle bug #19453814).
- Fix for Fluent API DbModelBuilder.HasColumnType is ignored in EF6 (Oracle bug #19456229).
- Fix for Setting a PK GUID identity in Code First in EF6 no longer works in Server 5.7 (Oracle bug #19456452).
- Non PKs declared as Identity GUID have no GUID autogenerated (Oracle bug #19456415).


6.9.2
- Add async/await compatible methods
- Fix for Unable to read geometry column when it has been set with a SRID value. (MySql Bug #71869, Oracle Bug #19137999)
- Fix for Exception adding a new column to an existing model as identity and PK fails when applying the migration (MySql Bug #71418, Oracle bug #18923294).
- Added SiteMap and Personalization configuration web providers to MySql.Web Nuget Package.


6.9.1
- Fix for Exception of "duplicate entry" in MySqlSessionProvider (MySql Bug #70409, Oracle bug #18657550).


6.9.0
- Added implementation of MySQLPersonalizationProvider.
- Added SiteMap Web provider.
- Added Simple Membership Web Provider.
- Fix for open sockets connections left when connection open fails, the error happens when the client try to get a connection when the max number of connections is reached in the server. (MySql #72025, Oracle Bug #18665388).
