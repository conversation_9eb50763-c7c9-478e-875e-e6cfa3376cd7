using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using Lite3DogMES.QRCodeManager;

namespace Lite3DogMES.ProcessManager
{
    /// <summary>
    /// 工序流程控制器
    /// </summary>
    public class ProcessFlowController
    {
        private readonly string _connectionString;
        private readonly QRCodeTraceability _traceability;

        public ProcessFlowController(string connectionString)
        {
            _connectionString = connectionString;
            _traceability = new QRCodeTraceability(connectionString);
        }

        /// <summary>
        /// 开始装配工序
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="operatorID">操作员ID</param>
        /// <param name="location">位置</param>
        /// <returns>操作结果</returns>
        public ProcessResult StartAssembly(string productSN, string operatorID, string location = "装配区")
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 创建新产品
                            var productID = CreateNewProduct(connection, transaction, productSN, operatorID);
                            
                            // 开始装配工序
                            StartProcess(connection, transaction, productID, "ASSEMBLY", operatorID, location);
                            
                            transaction.Commit();
                            
                            return new ProcessResult 
                            { 
                                Success = true, 
                                Message = "装配工序开始成功",
                                ProductID = productID
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult 
                { 
                    Success = false, 
                    Message = $"开始装配失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 完成装配工序
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="qrCodeScanned">是否已扫描二维码</param>
        /// <param name="operatorID">操作员ID</param>
        /// <param name="qualityResult">质量结果</param>
        /// <returns>操作结果</returns>
        public ProcessResult CompleteAssembly(string productSN, bool qrCodeScanned, string operatorID, 
            string qualityResult = "合格", string remarks = null)
        {
            try
            {
                if (!qrCodeScanned)
                {
                    return new ProcessResult 
                    { 
                        Success = false, 
                        Message = "请先扫描二维码确认产品位置" 
                    };
                }

                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult 
                    { 
                        Success = false, 
                        Message = "产品不存在" 
                    };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 完成装配工序
                    CompleteProcess(connection, productInfo.ProductID, "ASSEMBLY", qualityResult, operatorID, remarks);
                    
                    // 如果质量合格，自动进入下一工序
                    if (qualityResult == "合格")
                    {
                        UpdateProductStatus(connection, productInfo.ProductID, "标零", "一测区");
                    }
                    
                    return new ProcessResult 
                    { 
                        Success = true, 
                        Message = "装配工序完成",
                        ProductID = productInfo.ProductID
                    };
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult 
                { 
                    Success = false, 
                    Message = $"完成装配失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 执行标零操作
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult ExecuteCalibration(string productSN, string operatorID)
        {
            return ExecuteSimpleProcess(productSN, "CALIBRATION", "标零", operatorID, "一测区");
        }

        /// <summary>
        /// 开始一测
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult StartFirstTest(string productSN, string operatorID)
        {
            return StartProcessByCode(productSN, "FIRST_TEST", operatorID, "一测区");
        }

        /// <summary>
        /// 完成一测
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="testResults">测试结果</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult CompleteFirstTest(string productSN, List<TestResult> testResults, string operatorID)
        {
            try
            {
                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult { Success = false, Message = "产品不存在" };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 保存测试结果
                            var overallResult = SaveTestResults(connection, transaction, productInfo.ProductID, 
                                "FIRST_TEST", testResults, operatorID);
                            
                            // 完成一测工序
                            CompleteProcess(connection, productInfo.ProductID, "FIRST_TEST", overallResult, operatorID);
                            
                            // 更新产品状态到维护
                            UpdateProductStatus(connection, productInfo.ProductID, "维护", "维护区");
                            
                            transaction.Commit();
                            
                            return new ProcessResult 
                            { 
                                Success = true, 
                                Message = $"一测完成，结果: {overallResult}",
                                ProductID = productInfo.ProductID
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult 
                { 
                    Success = false, 
                    Message = $"完成一测失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 执行维护操作
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="maintenanceActions">维护动作</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult ExecuteMaintenance(string productSN, List<string> maintenanceActions, string operatorID)
        {
            try
            {
                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult { Success = false, Message = "产品不存在" };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 开始维护工序
                    StartProcess(connection, null, productInfo.ProductID, "MAINTENANCE", operatorID, "维护区");
                    
                    // 记录维护动作
                    var remarks = string.Join("; ", maintenanceActions);
                    
                    // 完成维护工序
                    CompleteProcess(connection, productInfo.ProductID, "MAINTENANCE", "完成", operatorID, remarks);
                    
                    // 更新产品状态到二测
                    UpdateProductStatus(connection, productInfo.ProductID, "二测", "二测区");
                    
                    return new ProcessResult 
                    { 
                        Success = true, 
                        Message = "维护完成",
                        ProductID = productInfo.ProductID
                    };
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult 
                { 
                    Success = false, 
                    Message = $"维护失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 开始二测
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult StartSecondTest(string productSN, string operatorID)
        {
            return StartProcessByCode(productSN, "SECOND_TEST", operatorID, "二测区");
        }

        /// <summary>
        /// 完成二测
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="testResults">测试结果</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult CompleteSecondTest(string productSN, List<TestResult> testResults, string operatorID)
        {
            try
            {
                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult { Success = false, Message = "产品不存在" };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 保存测试结果
                            var overallResult = SaveTestResults(connection, transaction, productInfo.ProductID, 
                                "SECOND_TEST", testResults, operatorID);
                            
                            // 完成二测工序
                            CompleteProcess(connection, productInfo.ProductID, "SECOND_TEST", overallResult, operatorID);
                            
                            if (overallResult == "合格")
                            {
                                // 更新产品状态到打包
                                UpdateProductStatus(connection, productInfo.ProductID, "打包", "打包区");
                            }
                            else
                            {
                                // 不合格返回维护
                                UpdateProductStatus(connection, productInfo.ProductID, "维护", "维护区");
                            }
                            
                            transaction.Commit();
                            
                            return new ProcessResult 
                            { 
                                Success = true, 
                                Message = $"二测完成，结果: {overallResult}",
                                ProductID = productInfo.ProductID
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult 
                { 
                    Success = false, 
                    Message = $"完成二测失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 执行打包操作
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult ExecutePackaging(string productSN, string operatorID)
        {
            return ExecuteSimpleProcess(productSN, "PACKAGING", "打包", operatorID, "打包区");
        }

        /// <summary>
        /// 执行发货操作
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <param name="shippingInfo">发货信息</param>
        /// <param name="operatorID">操作员ID</param>
        /// <returns>操作结果</returns>
        public ProcessResult ExecuteShipping(string productSN, ShippingInfo shippingInfo, string operatorID)
        {
            try
            {
                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult { Success = false, Message = "产品不存在" };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 开始发货工序
                            StartProcess(connection, transaction, productInfo.ProductID, "SHIPPING", operatorID, "发货区");
                            
                            // 保存发货信息
                            SaveShippingInfo(connection, transaction, productInfo.ProductID, shippingInfo, operatorID);
                            
                            // 完成发货工序
                            CompleteProcess(connection, productInfo.ProductID, "SHIPPING", "已发货", operatorID);
                            
                            // 标记产品完成
                            MarkProductCompleted(connection, productInfo.ProductID);
                            
                            transaction.Commit();
                            
                            return new ProcessResult 
                            { 
                                Success = true, 
                                Message = "发货完成",
                                ProductID = productInfo.ProductID
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult
                {
                    Success = false,
                    Message = $"发货失败: {ex.Message}"
                };
            }
        }

        // 私有辅助方法
        private string CreateNewProduct(SqlConnection connection, SqlTransaction transaction, string productSN, string operatorID)
        {
            var productID = Guid.NewGuid().ToString();
            var qrCode = $"LITE3_{productSN}";

            var sql = @"
                INSERT INTO Products (ProductID, ProductSN, QRCode, ProductModel, CurrentStatus, CurrentLocation)
                VALUES (@ProductID, @ProductSN, @QRCode, 'Lite3', '装配', '装配区')";

            using (var command = new SqlCommand(sql, connection, transaction))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@ProductSN", productSN);
                command.Parameters.AddWithValue("@QRCode", qrCode);
                command.ExecuteNonQuery();
            }

            // 创建二维码信息
            var qrSql = @"
                INSERT INTO QRCodeInfo (ProductID, QRCodeContent)
                VALUES (@ProductID, @QRCodeContent)";

            using (var command = new SqlCommand(qrSql, connection, transaction))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@QRCodeContent", qrCode);
                command.ExecuteNonQuery();
            }

            return productID;
        }

        private void StartProcess(SqlConnection connection, SqlTransaction transaction, string productID,
            string processCode, string operatorID, string location)
        {
            var sql = @"
                INSERT INTO ProcessRecords (ProductID, ProcessID, StartTime, Status, OperatorID, Location)
                SELECT @ProductID, ProcessID, GETDATE(), '进行中', @OperatorID, @Location
                FROM ProcessDefinitions WHERE ProcessCode = @ProcessCode";

            using (var command = new SqlCommand(sql, connection, transaction))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@ProcessCode", processCode);
                command.Parameters.AddWithValue("@OperatorID", operatorID);
                command.Parameters.AddWithValue("@Location", location);
                command.ExecuteNonQuery();
            }
        }

        private void CompleteProcess(SqlConnection connection, string productID, string processCode,
            string qualityResult, string operatorID, string remarks = null)
        {
            var sql = @"
                UPDATE ProcessRecords
                SET EndTime = GETDATE(), Status = '完成', QualityResult = @QualityResult, Remarks = @Remarks
                WHERE ProductID = @ProductID
                AND ProcessID = (SELECT ProcessID FROM ProcessDefinitions WHERE ProcessCode = @ProcessCode)
                AND Status = '进行中'";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@ProcessCode", processCode);
                command.Parameters.AddWithValue("@QualityResult", qualityResult);
                command.Parameters.AddWithValue("@Remarks", remarks ?? "");
                command.ExecuteNonQuery();
            }
        }

        private void UpdateProductStatus(SqlConnection connection, string productID, string status, string location)
        {
            var sql = @"
                UPDATE Products
                SET CurrentStatus = @Status, CurrentLocation = @Location, UpdatedTime = GETDATE()
                WHERE ProductID = @ProductID";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@Status", status);
                command.Parameters.AddWithValue("@Location", location);
                command.ExecuteNonQuery();
            }
        }

        private ProcessResult ExecuteSimpleProcess(string productSN, string processCode, string processName,
            string operatorID, string location)
        {
            try
            {
                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult { Success = false, Message = "产品不存在" };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    StartProcess(connection, null, productInfo.ProductID, processCode, operatorID, location);
                    CompleteProcess(connection, productInfo.ProductID, processCode, "完成", operatorID);

                    return new ProcessResult
                    {
                        Success = true,
                        Message = $"{processName}完成",
                        ProductID = productInfo.ProductID
                    };
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult
                {
                    Success = false,
                    Message = $"{processName}失败: {ex.Message}"
                };
            }
        }

        private ProcessResult StartProcessByCode(string productSN, string processCode, string operatorID, string location)
        {
            try
            {
                var productInfo = _traceability.GetProductBySerialNumber(productSN);
                if (productInfo == null)
                {
                    return new ProcessResult { Success = false, Message = "产品不存在" };
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    StartProcess(connection, null, productInfo.ProductID, processCode, operatorID, location);

                    return new ProcessResult
                    {
                        Success = true,
                        Message = "工序开始成功",
                        ProductID = productInfo.ProductID
                    };
                }
            }
            catch (Exception ex)
            {
                return new ProcessResult
                {
                    Success = false,
                    Message = $"开始工序失败: {ex.Message}"
                };
            }
        }

        private string SaveTestResults(SqlConnection connection, SqlTransaction transaction, string productID,
            string processCode, List<TestResult> testResults, string operatorID)
        {
            int passCount = 0;
            int totalCount = testResults.Count;

            foreach (var result in testResults)
            {
                var sql = @"
                    INSERT INTO QualityTests
                    (ProductID, ProcessID, TestType, TestParameter, StandardValue, ActualValue,
                     TestResult, TesterID, TestEquipment, Remarks)
                    SELECT @ProductID, ProcessID, @TestType, @TestParameter, @StandardValue, @ActualValue,
                           @TestResult, @TesterID, @TestEquipment, @Remarks
                    FROM ProcessDefinitions WHERE ProcessCode = @ProcessCode";

                using (var command = new SqlCommand(sql, connection, transaction))
                {
                    command.Parameters.AddWithValue("@ProductID", productID);
                    command.Parameters.AddWithValue("@ProcessCode", processCode);
                    command.Parameters.AddWithValue("@TestType", result.TestType);
                    command.Parameters.AddWithValue("@TestParameter", result.TestParameter);
                    command.Parameters.AddWithValue("@StandardValue", result.StandardValue);
                    command.Parameters.AddWithValue("@ActualValue", result.ActualValue);
                    command.Parameters.AddWithValue("@TestResult", result.Result);
                    command.Parameters.AddWithValue("@TesterID", operatorID);
                    command.Parameters.AddWithValue("@TestEquipment", result.TestEquipment ?? "");
                    command.Parameters.AddWithValue("@Remarks", result.Remarks ?? "");
                    command.ExecuteNonQuery();
                }

                if (result.Result == "合格")
                    passCount++;
            }

            return passCount == totalCount ? "合格" : "不合格";
        }

        private void SaveShippingInfo(SqlConnection connection, SqlTransaction transaction, string productID,
            ShippingInfo shippingInfo, string operatorID)
        {
            var sql = @"
                INSERT INTO ShippingInfo
                (ProductID, CustomerName, CustomerAddress, TrackingNumber, ShippingCompany, OperatorID, Remarks)
                VALUES
                (@ProductID, @CustomerName, @CustomerAddress, @TrackingNumber, @ShippingCompany, @OperatorID, @Remarks)";

            using (var command = new SqlCommand(sql, connection, transaction))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@CustomerName", shippingInfo.CustomerName ?? "");
                command.Parameters.AddWithValue("@CustomerAddress", shippingInfo.CustomerAddress ?? "");
                command.Parameters.AddWithValue("@TrackingNumber", shippingInfo.TrackingNumber ?? "");
                command.Parameters.AddWithValue("@ShippingCompany", shippingInfo.ShippingCompany ?? "");
                command.Parameters.AddWithValue("@OperatorID", operatorID);
                command.Parameters.AddWithValue("@Remarks", shippingInfo.Remarks ?? "");
                command.ExecuteNonQuery();
            }
        }

        private void MarkProductCompleted(SqlConnection connection, string productID)
        {
            var sql = @"
                UPDATE Products
                SET IsCompleted = 1, CurrentStatus = '已完成', UpdatedTime = GETDATE()
                WHERE ProductID = @ProductID";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.ExecuteNonQuery();
            }
        }
    }

    // 数据模型类
    public class ProcessResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string ProductID { get; set; }
    }

    public class TestResult
    {
        public string TestType { get; set; }
        public string TestParameter { get; set; }
        public decimal StandardValue { get; set; }
        public decimal ActualValue { get; set; }
        public string Result { get; set; }
        public string TestEquipment { get; set; }
        public string Remarks { get; set; }
    }

    public class ShippingInfo
    {
        public string CustomerName { get; set; }
        public string CustomerAddress { get; set; }
        public string TrackingNumber { get; set; }
        public string ShippingCompany { get; set; }
        public string Remarks { get; set; }
    }
}
