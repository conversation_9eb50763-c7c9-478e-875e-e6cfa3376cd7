using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Threading.Tasks;
using Lite3DogMES.ProcessManager;
using Lite3DogMES.QRCodeManager;
using Lite3DogMES.TraceabilitySystem;
using Lite3DogMES.ReportSystem;

namespace Lite3DogMES.Tests
{
    /// <summary>
    /// 系统测试套件
    /// </summary>
    public class SystemTestSuite
    {
        private readonly string _connectionString;
        private readonly ProcessFlowController _processController;
        private readonly QRCodeGenerator _qrGenerator;
        private readonly QRCodeScanner _qrScanner;
        private readonly ProductTraceabilityManager _traceabilityManager;
        private readonly ProductionReportManager _reportManager;
        private readonly ProcessStatusMonitor _statusMonitor;

        public SystemTestSuite()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["Lite3DogMES"]?.ConnectionString 
                ?? "Server=.;Database=Lite3DogMES;Integrated Security=true;";
            
            _processController = new ProcessFlowController(_connectionString);
            _qrGenerator = new QRCodeGenerator();
            _qrScanner = new QRCodeScanner();
            _traceabilityManager = new ProductTraceabilityManager(_connectionString);
            _reportManager = new ProductionReportManager(_connectionString);
            _statusMonitor = new ProcessStatusMonitor(_connectionString);
        }

        /// <summary>
        /// 运行完整的系统测试
        /// </summary>
        /// <returns>测试结果</returns>
        public async Task<SystemTestResult> RunFullSystemTest()
        {
            var testResult = new SystemTestResult
            {
                StartTime = DateTime.Now,
                TestName = "完整系统测试"
            };

            var testCases = new List<Func<Task<TestCaseResult>>>
            {
                TestDatabaseConnection,
                TestQRCodeGeneration,
                TestQRCodeScanning,
                TestProductCreation,
                TestProcessFlow,
                TestQualityTesting,
                TestTraceability,
                TestReportGeneration,
                TestStatusMonitoring,
                TestPerformance,
                TestConcurrency,
                TestErrorHandling
            };

            foreach (var testCase in testCases)
            {
                try
                {
                    var result = await testCase();
                    testResult.TestCases.Add(result);
                    
                    if (!result.Passed)
                    {
                        testResult.FailedTests++;
                    }
                    else
                    {
                        testResult.PassedTests++;
                    }
                }
                catch (Exception ex)
                {
                    testResult.TestCases.Add(new TestCaseResult
                    {
                        TestName = testCase.Method.Name,
                        Passed = false,
                        ErrorMessage = ex.Message,
                        ExecutionTime = TimeSpan.Zero
                    });
                    testResult.FailedTests++;
                }
            }

            testResult.EndTime = DateTime.Now;
            testResult.TotalExecutionTime = testResult.EndTime - testResult.StartTime;
            testResult.OverallSuccess = testResult.FailedTests == 0;

            return testResult;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        private async Task<TestCaseResult> TestDatabaseConnection()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "数据库连接测试" };

            try
            {
                using (var connection = new System.Data.SqlClient.SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // 测试基本查询
                    var command = new System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM Products", connection);
                    var count = await command.ExecuteScalarAsync();
                    
                    result.Passed = true;
                    result.Message = $"数据库连接成功，产品表记录数: {count}";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试二维码生成
        /// </summary>
        private async Task<TestCaseResult> TestQRCodeGeneration()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "二维码生成测试" };

            try
            {
                var productSN = $"TEST_{DateTime.Now:yyyyMMddHHmmss}";
                var qrContent = QRCodeGenerator.GenerateProductQRContent(productSN, "Lite3", DateTime.Now);
                var qrBytes = _qrGenerator.GenerateQRCode(qrContent);

                if (qrBytes != null && qrBytes.Length > 0)
                {
                    result.Passed = true;
                    result.Message = $"二维码生成成功，大小: {qrBytes.Length} 字节";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = "二维码生成失败，返回空数据";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试二维码扫描
        /// </summary>
        private async Task<TestCaseResult> TestQRCodeScanning()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "二维码扫描测试" };

            try
            {
                // 生成测试二维码
                var productSN = $"SCAN_TEST_{DateTime.Now:yyyyMMddHHmmss}";
                var qrContent = QRCodeGenerator.GenerateProductQRContent(productSN, "Lite3", DateTime.Now);
                var qrBytes = _qrGenerator.GenerateQRCode(qrContent);

                // 保存为临时文件
                var tempFile = System.IO.Path.GetTempFileName() + ".png";
                await System.IO.File.WriteAllBytesAsync(tempFile, qrBytes);

                // 扫描测试
                var scanResult = _qrScanner.ScanFromFile(tempFile);

                if (scanResult.Success && !string.IsNullOrEmpty(scanResult.Content))
                {
                    result.Passed = true;
                    result.Message = $"二维码扫描成功，内容: {scanResult.Content}";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = scanResult.ErrorMessage ?? "扫描失败";
                }

                // 清理临时文件
                if (System.IO.File.Exists(tempFile))
                {
                    System.IO.File.Delete(tempFile);
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试产品创建
        /// </summary>
        private async Task<TestCaseResult> TestProductCreation()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "产品创建测试" };

            try
            {
                var productSN = $"CREATE_TEST_{DateTime.Now:yyyyMMddHHmmss}";
                var createResult = _processController.StartAssembly(productSN, "test_operator", "测试装配区");

                if (createResult.Success)
                {
                    result.Passed = true;
                    result.Message = $"产品创建成功: {createResult.Message}";
                    result.Data = createResult.ProductID;
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = createResult.Message;
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试工序流程
        /// </summary>
        private async Task<TestCaseResult> TestProcessFlow()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "工序流程测试" };

            try
            {
                var productSN = $"FLOW_TEST_{DateTime.Now:yyyyMMddHHmmss}";
                var operatorID = "test_operator";

                // 1. 开始装配
                var assemblyResult = _processController.StartAssembly(productSN, operatorID, "测试装配区");
                if (!assemblyResult.Success)
                {
                    throw new Exception($"装配开始失败: {assemblyResult.Message}");
                }

                // 2. 完成装配
                var completeAssemblyResult = _processController.CompleteAssembly(productSN, true, operatorID);
                if (!completeAssemblyResult.Success)
                {
                    throw new Exception($"装配完成失败: {completeAssemblyResult.Message}");
                }

                // 3. 标零
                var calibrationResult = _processController.ExecuteCalibration(productSN, operatorID);
                if (!calibrationResult.Success)
                {
                    throw new Exception($"标零失败: {calibrationResult.Message}");
                }

                // 4. 一测
                var firstTestResult = _processController.StartFirstTest(productSN, operatorID);
                if (!firstTestResult.Success)
                {
                    throw new Exception($"一测开始失败: {firstTestResult.Message}");
                }

                result.Passed = true;
                result.Message = "工序流程测试成功完成";
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试质量检测
        /// </summary>
        private async Task<TestCaseResult> TestQualityTesting()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "质量检测测试" };

            try
            {
                var productSN = $"QUALITY_TEST_{DateTime.Now:yyyyMMddHHmmss}";
                var operatorID = "test_operator";

                // 创建测试产品
                var createResult = _processController.StartAssembly(productSN, operatorID, "测试区");
                if (!createResult.Success)
                {
                    throw new Exception($"产品创建失败: {createResult.Message}");
                }

                // 模拟测试结果
                var testResults = new List<TestResult>
                {
                    new TestResult
                    {
                        TestType = "功能测试",
                        TestParameter = "响应时间",
                        StandardValue = 100,
                        ActualValue = 95,
                        Result = "合格",
                        TestEquipment = "测试设备1"
                    },
                    new TestResult
                    {
                        TestType = "性能测试",
                        TestParameter = "处理速度",
                        StandardValue = 50,
                        ActualValue = 52,
                        Result = "合格",
                        TestEquipment = "测试设备2"
                    }
                };

                var testResult = _processController.CompleteFirstTest(productSN, testResults, operatorID);
                if (testResult.Success)
                {
                    result.Passed = true;
                    result.Message = "质量检测测试成功";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = testResult.Message;
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试追溯功能
        /// </summary>
        private async Task<TestCaseResult> TestTraceability()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "追溯功能测试" };

            try
            {
                var productSN = $"TRACE_TEST_{DateTime.Now:yyyyMMddHHmmss}";
                
                // 创建测试产品
                var createResult = _processController.StartAssembly(productSN, "test_operator", "测试区");
                if (!createResult.Success)
                {
                    throw new Exception($"产品创建失败: {createResult.Message}");
                }

                // 等待数据写入
                await Task.Delay(1000);

                // 查询追溯信息
                var traceChain = _traceabilityManager.GetProductTraceChain(productSN);
                if (traceChain != null && traceChain.ProductInfo != null)
                {
                    result.Passed = true;
                    result.Message = $"追溯查询成功，找到 {traceChain.TraceEvents.Count} 个追溯事件";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = "追溯信息查询失败";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试报表生成
        /// </summary>
        private async Task<TestCaseResult> TestReportGeneration()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "报表生成测试" };

            try
            {
                var today = DateTime.Today;
                var dailyReport = _reportManager.GenerateDailyReport(today);

                if (dailyReport != null && string.IsNullOrEmpty(dailyReport.ErrorMessage))
                {
                    result.Passed = true;
                    result.Message = $"日报表生成成功，生产产品数: {dailyReport.ProductionSummary?.TotalProducts ?? 0}";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = dailyReport?.ErrorMessage ?? "报表生成失败";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试状态监控
        /// </summary>
        private async Task<TestCaseResult> TestStatusMonitoring()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "状态监控测试" };

            try
            {
                var productStatuses = _statusMonitor.GetAllProductStatus();
                var processStatistics = _statusMonitor.GetProcessStatistics();

                if (productStatuses != null && processStatistics != null)
                {
                    result.Passed = true;
                    result.Message = $"状态监控正常，监控到 {productStatuses.Count} 个产品状态";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = "状态监控数据获取失败";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试系统性能
        /// </summary>
        private async Task<TestCaseResult> TestPerformance()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "性能测试" };

            try
            {
                var tasks = new List<Task>();
                var successCount = 0;
                var totalTasks = 10;

                // 并发创建多个产品测试性能
                for (int i = 0; i < totalTasks; i++)
                {
                    var taskIndex = i;
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var productSN = $"PERF_TEST_{DateTime.Now:yyyyMMddHHmmss}_{taskIndex}";
                            var createResult = _processController.StartAssembly(productSN, "test_operator", "性能测试区");
                            if (createResult.Success)
                            {
                                System.Threading.Interlocked.Increment(ref successCount);
                            }
                        }
                        catch
                        {
                            // 忽略单个任务的错误
                        }
                    }));
                }

                await Task.WhenAll(tasks);

                if (successCount >= totalTasks * 0.8) // 80%成功率认为性能测试通过
                {
                    result.Passed = true;
                    result.Message = $"性能测试通过，{totalTasks}个并发任务中{successCount}个成功";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = $"性能测试失败，成功率过低: {successCount}/{totalTasks}";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试并发处理
        /// </summary>
        private async Task<TestCaseResult> TestConcurrency()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "并发处理测试" };

            try
            {
                var concurrentTasks = 5;
                var tasks = new List<Task<bool>>();

                for (int i = 0; i < concurrentTasks; i++)
                {
                    var taskIndex = i;
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var productSN = $"CONCURRENT_TEST_{DateTime.Now:yyyyMMddHHmmss}_{taskIndex}";
                            var createResult = _processController.StartAssembly(productSN, $"operator_{taskIndex}", "并发测试区");
                            
                            if (createResult.Success)
                            {
                                var completeResult = _processController.CompleteAssembly(productSN, true, $"operator_{taskIndex}");
                                return completeResult.Success;
                            }
                            return false;
                        }
                        catch
                        {
                            return false;
                        }
                    }));
                }

                var results = await Task.WhenAll(tasks);
                var successCount = results.Count(r => r);

                if (successCount == concurrentTasks)
                {
                    result.Passed = true;
                    result.Message = $"并发处理测试成功，{concurrentTasks}个并发任务全部完成";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = $"并发处理测试失败，{concurrentTasks}个任务中只有{successCount}个成功";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 测试错误处理
        /// </summary>
        private async Task<TestCaseResult> TestErrorHandling()
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new TestCaseResult { TestName = "错误处理测试" };

            try
            {
                // 测试无效产品序列号
                var invalidResult = _processController.CompleteAssembly("INVALID_PRODUCT_SN", true, "test_operator");
                
                // 测试重复产品创建
                var productSN = $"ERROR_TEST_{DateTime.Now:yyyyMMddHHmmss}";
                var firstCreate = _processController.StartAssembly(productSN, "test_operator", "测试区");
                var secondCreate = _processController.StartAssembly(productSN, "test_operator", "测试区");

                // 错误处理测试应该优雅地处理这些情况
                if (!invalidResult.Success && firstCreate.Success && !secondCreate.Success)
                {
                    result.Passed = true;
                    result.Message = "错误处理测试通过，系统正确处理了无效输入和重复操作";
                }
                else
                {
                    result.Passed = false;
                    result.ErrorMessage = "错误处理测试失败，系统未正确处理错误情况";
                }
            }
            catch (Exception ex)
            {
                result.Passed = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        public async Task CleanupTestData()
        {
            try
            {
                using (var connection = new System.Data.SqlClient.SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // 删除测试产品数据
                    var deleteCommands = new[]
                    {
                        "DELETE FROM ProcessRecords WHERE ProductID IN (SELECT ProductID FROM Products WHERE ProductSN LIKE '%TEST_%')",
                        "DELETE FROM QualityTests WHERE ProductID IN (SELECT ProductID FROM Products WHERE ProductSN LIKE '%TEST_%')",
                        "DELETE FROM ProductTraceability WHERE ProductID IN (SELECT ProductID FROM Products WHERE ProductSN LIKE '%TEST_%')",
                        "DELETE FROM ScanRecords WHERE ProductID IN (SELECT ProductID FROM Products WHERE ProductSN LIKE '%TEST_%')",
                        "DELETE FROM QRCodeInfo WHERE ProductID IN (SELECT ProductID FROM Products WHERE ProductSN LIKE '%TEST_%')",
                        "DELETE FROM Products WHERE ProductSN LIKE '%TEST_%'"
                    };

                    foreach (var sql in deleteCommands)
                    {
                        var command = new System.Data.SqlClient.SqlCommand(sql, connection);
                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理测试数据失败: {ex.Message}");
            }
        }
    }

    // 测试结果数据模型
    public class SystemTestResult
    {
        public string TestName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan TotalExecutionTime { get; set; }
        public bool OverallSuccess { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        public List<TestCaseResult> TestCases { get; set; } = new List<TestCaseResult>();
        
        public int TotalTests => PassedTests + FailedTests;
        public decimal SuccessRate => TotalTests > 0 ? (decimal)PassedTests / TotalTests * 100 : 0;
    }

    public class TestCaseResult
    {
        public string TestName { get; set; }
        public bool Passed { get; set; }
        public string Message { get; set; }
        public string ErrorMessage { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public object Data { get; set; }
    }
}
