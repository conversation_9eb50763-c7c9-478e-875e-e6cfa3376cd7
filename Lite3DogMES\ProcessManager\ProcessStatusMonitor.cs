using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Timers;

namespace Lite3DogMES.ProcessManager
{
    /// <summary>
    /// 工序状态监控器
    /// </summary>
    public class ProcessStatusMonitor : IDisposable
    {
        private readonly string _connectionString;
        private readonly Timer _monitorTimer;
        private bool _isMonitoring;

        public event EventHandler<ProcessStatusEventArgs> ProcessStatusChanged;
        public event EventHandler<ProcessAlertEventArgs> ProcessAlert;

        public ProcessStatusMonitor(string connectionString, int monitorIntervalSeconds = 30)
        {
            _connectionString = connectionString;
            _monitorTimer = new Timer(monitorIntervalSeconds * 1000);
            _monitorTimer.Elapsed += OnMonitorTimerElapsed;
        }

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            _isMonitoring = true;
            _monitorTimer.Start();
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            _isMonitoring = false;
            _monitorTimer.Stop();
        }

        /// <summary>
        /// 获取当前所有产品状态
        /// </summary>
        /// <returns>产品状态列表</returns>
        public List<ProductStatus> GetAllProductStatus()
        {
            var statusList = new List<ProductStatus>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT 
                            p.ProductID,
                            p.ProductSN,
                            p.ProductModel,
                            p.CurrentStatus,
                            p.CurrentLocation,
                            p.ProductionDate,
                            p.IsCompleted,
                            ISNULL(progress.CompletedProcesses, 0) as CompletedProcesses,
                            ISNULL(progress.TotalProcesses, 0) as TotalProcesses,
                            ISNULL(progress.CompletionRate, 0) as CompletionRate,
                            current_process.StartTime as CurrentProcessStartTime,
                            current_process.OperatorID as CurrentOperatorID
                        FROM Products p
                        LEFT JOIN (
                            SELECT 
                                ProductID,
                                COUNT(DISTINCT pr.ProcessID) as CompletedProcesses,
                                (SELECT COUNT(*) FROM ProcessDefinitions WHERE IsActive = 1) as TotalProcesses,
                                CAST(COUNT(DISTINCT pr.ProcessID) * 100.0 / 
                                    (SELECT COUNT(*) FROM ProcessDefinitions WHERE IsActive = 1) AS DECIMAL(5,2)) as CompletionRate
                            FROM ProcessRecords pr
                            WHERE pr.Status = '完成'
                            GROUP BY ProductID
                        ) progress ON p.ProductID = progress.ProductID
                        LEFT JOIN (
                            SELECT 
                                pr.ProductID,
                                pr.StartTime,
                                pr.OperatorID,
                                ROW_NUMBER() OVER (PARTITION BY pr.ProductID ORDER BY pr.StartTime DESC) as rn
                            FROM ProcessRecords pr
                            WHERE pr.Status = '进行中'
                        ) current_process ON p.ProductID = current_process.ProductID AND current_process.rn = 1
                        WHERE p.IsCompleted = 0
                        ORDER BY p.ProductionDate DESC";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            statusList.Add(new ProductStatus
                            {
                                ProductID = reader["ProductID"].ToString(),
                                ProductSN = reader["ProductSN"].ToString(),
                                ProductModel = reader["ProductModel"].ToString(),
                                CurrentStatus = reader["CurrentStatus"].ToString(),
                                CurrentLocation = reader["CurrentLocation"].ToString(),
                                ProductionDate = Convert.ToDateTime(reader["ProductionDate"]),
                                IsCompleted = Convert.ToBoolean(reader["IsCompleted"]),
                                CompletedProcesses = Convert.ToInt32(reader["CompletedProcesses"]),
                                TotalProcesses = Convert.ToInt32(reader["TotalProcesses"]),
                                CompletionRate = Convert.ToDecimal(reader["CompletionRate"]),
                                CurrentProcessStartTime = reader["CurrentProcessStartTime"] == DBNull.Value ? 
                                    null : (DateTime?)reader["CurrentProcessStartTime"],
                                CurrentOperatorID = reader["CurrentOperatorID"].ToString()
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品状态失败: {ex.Message}");
            }

            return statusList;
        }

        /// <summary>
        /// 获取工序统计信息
        /// </summary>
        /// <returns>工序统计</returns>
        public ProcessStatistics GetProcessStatistics()
        {
            var statistics = new ProcessStatistics();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 获取各工序的产品数量
                    var sql = @"
                        SELECT 
                            pd.ProcessName,
                            pd.ProcessCode,
                            COUNT(DISTINCT p.ProductID) as ProductCount,
                            AVG(CASE 
                                WHEN pr.EndTime IS NOT NULL AND pr.StartTime IS NOT NULL 
                                THEN DATEDIFF(MINUTE, pr.StartTime, pr.EndTime)
                                ELSE NULL 
                            END) as AvgProcessTime,
                            SUM(CASE WHEN pr.QualityResult = '合格' THEN 1 ELSE 0 END) as PassCount,
                            COUNT(pr.RecordID) as TotalCount
                        FROM ProcessDefinitions pd
                        LEFT JOIN ProcessRecords pr ON pd.ProcessID = pr.ProcessID
                        LEFT JOIN Products p ON pr.ProductID = p.ProductID AND p.IsCompleted = 0
                        WHERE pd.IsActive = 1
                        GROUP BY pd.ProcessName, pd.ProcessCode, pd.ProcessOrder
                        ORDER BY pd.ProcessOrder";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            statistics.ProcessCounts.Add(new ProcessCount
                            {
                                ProcessName = reader["ProcessName"].ToString(),
                                ProcessCode = reader["ProcessCode"].ToString(),
                                ProductCount = Convert.ToInt32(reader["ProductCount"]),
                                AvgProcessTime = reader["AvgProcessTime"] == DBNull.Value ? 
                                    0 : Convert.ToDouble(reader["AvgProcessTime"]),
                                PassCount = Convert.ToInt32(reader["PassCount"]),
                                TotalCount = Convert.ToInt32(reader["TotalCount"]),
                                PassRate = Convert.ToInt32(reader["TotalCount"]) > 0 ? 
                                    Convert.ToDecimal(reader["PassCount"]) / Convert.ToDecimal(reader["TotalCount"]) * 100 : 0
                            });
                        }
                    }
                }

                // 计算总体统计
                statistics.TotalProducts = statistics.ProcessCounts.FirstOrDefault()?.ProductCount ?? 0;
                statistics.OverallPassRate = statistics.ProcessCounts.Count > 0 ? 
                    statistics.ProcessCounts.Average(p => p.PassRate) : 0;
                statistics.AvgCompletionTime = statistics.ProcessCounts.Sum(p => p.AvgProcessTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工序统计失败: {ex.Message}");
            }

            return statistics;
        }

        /// <summary>
        /// 获取异常产品列表
        /// </summary>
        /// <returns>异常产品列表</returns>
        public List<ProductAlert> GetProductAlerts()
        {
            var alerts = new List<ProductAlert>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 查找超时的工序
                    var timeoutSql = @"
                        SELECT 
                            p.ProductID,
                            p.ProductSN,
                            pd.ProcessName,
                            pr.StartTime,
                            pd.RequiredTime,
                            DATEDIFF(MINUTE, pr.StartTime, GETDATE()) as ActualTime
                        FROM ProcessRecords pr
                        INNER JOIN Products p ON pr.ProductID = p.ProductID
                        INNER JOIN ProcessDefinitions pd ON pr.ProcessID = pd.ProcessID
                        WHERE pr.Status = '进行中'
                        AND pd.RequiredTime > 0
                        AND DATEDIFF(MINUTE, pr.StartTime, GETDATE()) > pd.RequiredTime * 1.5";

                    using (var command = new SqlCommand(timeoutSql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            alerts.Add(new ProductAlert
                            {
                                ProductID = reader["ProductID"].ToString(),
                                ProductSN = reader["ProductSN"].ToString(),
                                AlertType = "工序超时",
                                AlertMessage = $"{reader["ProcessName"]}工序超时，已用时{reader["ActualTime"]}分钟，标准时间{reader["RequiredTime"]}分钟",
                                AlertTime = DateTime.Now,
                                Severity = AlertSeverity.Warning
                            });
                        }
                    }
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // 查找质量不合格的产品
                    var qualitySql = @"
                        SELECT 
                            p.ProductID,
                            p.ProductSN,
                            pd.ProcessName,
                            pr.QualityResult,
                            pr.EndTime
                        FROM ProcessRecords pr
                        INNER JOIN Products p ON pr.ProductID = p.ProductID
                        INNER JOIN ProcessDefinitions pd ON pr.ProcessID = pd.ProcessID
                        WHERE pr.QualityResult = '不合格'
                        AND pr.EndTime >= DATEADD(HOUR, -24, GETDATE())";

                    using (var command = new SqlCommand(qualitySql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            alerts.Add(new ProductAlert
                            {
                                ProductID = reader["ProductID"].ToString(),
                                ProductSN = reader["ProductSN"].ToString(),
                                AlertType = "质量异常",
                                AlertMessage = $"{reader["ProcessName"]}工序质量检测不合格",
                                AlertTime = Convert.ToDateTime(reader["EndTime"]),
                                Severity = AlertSeverity.High
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品告警失败: {ex.Message}");
            }

            return alerts;
        }

        /// <summary>
        /// 监控定时器事件
        /// </summary>
        private void OnMonitorTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (!_isMonitoring) return;

            try
            {
                // 检查产品状态变化
                var currentStatus = GetAllProductStatus();
                ProcessStatusChanged?.Invoke(this, new ProcessStatusEventArgs { ProductStatuses = currentStatus });

                // 检查异常告警
                var alerts = GetProductAlerts();
                if (alerts.Any())
                {
                    ProcessAlert?.Invoke(this, new ProcessAlertEventArgs { Alerts = alerts });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"监控过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopMonitoring();
            _monitorTimer?.Dispose();
        }
    }

    // 数据模型类
    public class ProductStatus
    {
        public string ProductID { get; set; }
        public string ProductSN { get; set; }
        public string ProductModel { get; set; }
        public string CurrentStatus { get; set; }
        public string CurrentLocation { get; set; }
        public DateTime ProductionDate { get; set; }
        public bool IsCompleted { get; set; }
        public int CompletedProcesses { get; set; }
        public int TotalProcesses { get; set; }
        public decimal CompletionRate { get; set; }
        public DateTime? CurrentProcessStartTime { get; set; }
        public string CurrentOperatorID { get; set; }
    }

    public class ProcessStatistics
    {
        public List<ProcessCount> ProcessCounts { get; set; } = new List<ProcessCount>();
        public int TotalProducts { get; set; }
        public decimal OverallPassRate { get; set; }
        public double AvgCompletionTime { get; set; }
    }

    public class ProcessCount
    {
        public string ProcessName { get; set; }
        public string ProcessCode { get; set; }
        public int ProductCount { get; set; }
        public double AvgProcessTime { get; set; }
        public int PassCount { get; set; }
        public int TotalCount { get; set; }
        public decimal PassRate { get; set; }
    }

    public class ProductAlert
    {
        public string ProductID { get; set; }
        public string ProductSN { get; set; }
        public string AlertType { get; set; }
        public string AlertMessage { get; set; }
        public DateTime AlertTime { get; set; }
        public AlertSeverity Severity { get; set; }
    }

    public enum AlertSeverity
    {
        Low,
        Warning,
        High,
        Critical
    }

    public class ProcessStatusEventArgs : EventArgs
    {
        public List<ProductStatus> ProductStatuses { get; set; }
    }

    public class ProcessAlertEventArgs : EventArgs
    {
        public List<ProductAlert> Alerts { get; set; }
    }
}
