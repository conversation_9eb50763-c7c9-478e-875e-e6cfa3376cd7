﻿#pragma checksum "..\..\..\MaterialRecivingLine.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "9832770CDF38BE8F6EB42E8C54EB28DFAFBA11FF90761579D05507AC19FC006A"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using HMIControl;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CoreTest {
    
    
    /// <summary>
    /// MaterialRecivingLine
    /// </summary>
    public partial class MaterialRecivingLine : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 7 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CoreTest.MaterialRecivingLine MaterialRecivingLine1;
        
        #line default
        #line hidden
        
        
        #line 9 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas cvs1;
        
        #line default
        #line hidden
        
        
        #line 10 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock productline;
        
        #line default
        #line hidden
        
        
        #line 11 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ManualAddControl manualAddControl1;
        
        #line default
        #line hidden
        
        
        #line 12 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor1;
        
        #line default
        #line hidden
        
        
        #line 14 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ManualAddControl manualAddControl2;
        
        #line default
        #line hidden
        
        
        #line 15 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor2;
        
        #line default
        #line hidden
        
        
        #line 17 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Elevator elevator1;
        
        #line default
        #line hidden
        
        
        #line 18 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Elevator elevator2;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Elevator elevator3;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin1;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin2;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin3;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin4;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin5;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin6;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.MagnetCleaner magnetCleaner1;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.MagnetCleaner magnetCleaner2;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.MagnetCleaner magnetCleaner3;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.PreCleaner preCleaner1;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.PreCleaner preCleaner2;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor3;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Divert divert1;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FourWays fourWays1;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor4;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo1;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo2;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo3;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo4;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo5;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo6;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo7;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SelectSwitch selectSwitch1;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SelectSwitch selectSwitch3;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo8;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo9;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo10;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo11;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo13;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate6;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate1;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate2;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate3;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate4;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate5;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate7;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo12;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin7;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin8;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin9;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin10;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin11;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.BufferBin bufferBin12;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Elevator elevator4;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ManualAddControl manualAddControl3;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor5;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo14;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Divert divert2;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.PreCleaner preCleaner3;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.MagnetCleaner magnetCleaner4;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor6;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo15;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate9;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate10;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate11;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate12;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate13;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate14;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor7;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock2;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock1;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock3;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock4;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock5;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock6;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock7;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock8;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock9;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock10;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock11;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock12;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock13;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock14;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock15;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock16;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock17;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock18;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock20;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock21;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock22;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock23;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock24;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock25;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock26;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock27;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock28;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock29;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock textBlock30;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\MaterialRecivingLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Fan Receiving2_Fan1;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CoreTest;component/materialrecivingline.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MaterialRecivingLine.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MaterialRecivingLine1 = ((CoreTest.MaterialRecivingLine)(target));
            
            #line 8 "..\..\..\MaterialRecivingLine.xaml"
            this.MaterialRecivingLine1.Loaded += new System.Windows.RoutedEventHandler(this.HMI_Loaded);
            
            #line default
            #line hidden
            
            #line 8 "..\..\..\MaterialRecivingLine.xaml"
            this.MaterialRecivingLine1.Unloaded += new System.Windows.RoutedEventHandler(this.HMI_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cvs1 = ((System.Windows.Controls.Canvas)(target));
            return;
            case 3:
            this.productline = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.manualAddControl1 = ((HMIControl.ManualAddControl)(target));
            return;
            case 5:
            this.chainConveyor1 = ((HMIControl.ChainConveyor)(target));
            return;
            case 6:
            this.manualAddControl2 = ((HMIControl.ManualAddControl)(target));
            return;
            case 7:
            this.chainConveyor2 = ((HMIControl.ChainConveyor)(target));
            return;
            case 8:
            this.elevator1 = ((HMIControl.Elevator)(target));
            return;
            case 9:
            this.elevator2 = ((HMIControl.Elevator)(target));
            return;
            case 10:
            this.elevator3 = ((HMIControl.Elevator)(target));
            return;
            case 11:
            this.bufferBin1 = ((HMIControl.BufferBin)(target));
            return;
            case 12:
            this.bufferBin2 = ((HMIControl.BufferBin)(target));
            return;
            case 13:
            this.bufferBin3 = ((HMIControl.BufferBin)(target));
            return;
            case 14:
            this.bufferBin4 = ((HMIControl.BufferBin)(target));
            return;
            case 15:
            this.bufferBin5 = ((HMIControl.BufferBin)(target));
            return;
            case 16:
            this.bufferBin6 = ((HMIControl.BufferBin)(target));
            return;
            case 17:
            this.magnetCleaner1 = ((HMIControl.MagnetCleaner)(target));
            return;
            case 18:
            this.magnetCleaner2 = ((HMIControl.MagnetCleaner)(target));
            return;
            case 19:
            this.magnetCleaner3 = ((HMIControl.MagnetCleaner)(target));
            return;
            case 20:
            this.preCleaner1 = ((HMIControl.PreCleaner)(target));
            return;
            case 21:
            this.preCleaner2 = ((HMIControl.PreCleaner)(target));
            return;
            case 22:
            this.chainConveyor3 = ((HMIControl.ChainConveyor)(target));
            return;
            case 23:
            this.divert1 = ((HMIControl.Divert)(target));
            return;
            case 24:
            this.fourWays1 = ((HMIControl.FourWays)(target));
            return;
            case 25:
            this.chainConveyor4 = ((HMIControl.ChainConveyor)(target));
            return;
            case 26:
            this.fromTo1 = ((HMIControl.FromTo)(target));
            return;
            case 27:
            this.fromTo2 = ((HMIControl.FromTo)(target));
            return;
            case 28:
            this.fromTo3 = ((HMIControl.FromTo)(target));
            return;
            case 29:
            this.fromTo4 = ((HMIControl.FromTo)(target));
            return;
            case 30:
            this.fromTo5 = ((HMIControl.FromTo)(target));
            return;
            case 31:
            this.fromTo6 = ((HMIControl.FromTo)(target));
            return;
            case 32:
            this.fromTo7 = ((HMIControl.FromTo)(target));
            return;
            case 33:
            this.selectSwitch1 = ((HMIControl.SelectSwitch)(target));
            return;
            case 34:
            this.selectSwitch3 = ((HMIControl.SelectSwitch)(target));
            return;
            case 35:
            this.fromTo8 = ((HMIControl.FromTo)(target));
            return;
            case 36:
            this.fromTo9 = ((HMIControl.FromTo)(target));
            return;
            case 37:
            this.fromTo10 = ((HMIControl.FromTo)(target));
            return;
            case 38:
            this.fromTo11 = ((HMIControl.FromTo)(target));
            return;
            case 39:
            this.fromTo13 = ((HMIControl.FromTo)(target));
            return;
            case 40:
            this.slideGate6 = ((HMIControl.SlideGate)(target));
            return;
            case 41:
            this.slideGate1 = ((HMIControl.SlideGate)(target));
            return;
            case 42:
            this.slideGate2 = ((HMIControl.SlideGate)(target));
            return;
            case 43:
            this.slideGate3 = ((HMIControl.SlideGate)(target));
            return;
            case 44:
            this.slideGate4 = ((HMIControl.SlideGate)(target));
            return;
            case 45:
            this.slideGate5 = ((HMIControl.SlideGate)(target));
            return;
            case 46:
            this.slideGate7 = ((HMIControl.SlideGate)(target));
            return;
            case 47:
            this.fromTo12 = ((HMIControl.FromTo)(target));
            return;
            case 48:
            this.bufferBin7 = ((HMIControl.BufferBin)(target));
            return;
            case 49:
            this.bufferBin8 = ((HMIControl.BufferBin)(target));
            return;
            case 50:
            this.bufferBin9 = ((HMIControl.BufferBin)(target));
            return;
            case 51:
            this.bufferBin10 = ((HMIControl.BufferBin)(target));
            return;
            case 52:
            this.bufferBin11 = ((HMIControl.BufferBin)(target));
            return;
            case 53:
            this.bufferBin12 = ((HMIControl.BufferBin)(target));
            return;
            case 54:
            this.elevator4 = ((HMIControl.Elevator)(target));
            return;
            case 55:
            this.manualAddControl3 = ((HMIControl.ManualAddControl)(target));
            return;
            case 56:
            this.chainConveyor5 = ((HMIControl.ChainConveyor)(target));
            return;
            case 57:
            this.fromTo14 = ((HMIControl.FromTo)(target));
            return;
            case 58:
            this.divert2 = ((HMIControl.Divert)(target));
            return;
            case 59:
            this.preCleaner3 = ((HMIControl.PreCleaner)(target));
            return;
            case 60:
            this.magnetCleaner4 = ((HMIControl.MagnetCleaner)(target));
            return;
            case 61:
            this.chainConveyor6 = ((HMIControl.ChainConveyor)(target));
            return;
            case 62:
            this.fromTo15 = ((HMIControl.FromTo)(target));
            return;
            case 63:
            this.slideGate9 = ((HMIControl.SlideGate)(target));
            return;
            case 64:
            this.slideGate10 = ((HMIControl.SlideGate)(target));
            return;
            case 65:
            this.slideGate11 = ((HMIControl.SlideGate)(target));
            return;
            case 66:
            this.slideGate12 = ((HMIControl.SlideGate)(target));
            return;
            case 67:
            this.slideGate13 = ((HMIControl.SlideGate)(target));
            return;
            case 68:
            this.slideGate14 = ((HMIControl.SlideGate)(target));
            return;
            case 69:
            this.chainConveyor7 = ((HMIControl.ChainConveyor)(target));
            return;
            case 70:
            this.textBlock2 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 71:
            this.textBlock1 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 72:
            this.textBlock3 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 73:
            this.textBlock4 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 74:
            this.textBlock5 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 75:
            this.textBlock6 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 76:
            this.textBlock7 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 77:
            this.textBlock8 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 78:
            this.textBlock9 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 79:
            this.textBlock10 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 80:
            this.textBlock11 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 81:
            this.textBlock12 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 82:
            this.textBlock13 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 83:
            this.textBlock14 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 84:
            this.textBlock15 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 85:
            this.textBlock16 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 86:
            this.textBlock17 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 87:
            this.textBlock18 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 88:
            this.textBlock20 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 89:
            this.textBlock21 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 90:
            this.textBlock22 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 91:
            this.textBlock23 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 92:
            this.textBlock24 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 93:
            this.textBlock25 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 94:
            this.textBlock26 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 95:
            this.textBlock27 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 96:
            this.textBlock28 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 97:
            this.textBlock29 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 98:
            this.textBlock30 = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 99:
            this.Receiving2_Fan1 = ((HMIControl.Fan)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

