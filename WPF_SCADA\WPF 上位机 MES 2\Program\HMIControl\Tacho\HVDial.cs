﻿using System.Windows;
using System.Windows.Controls;

namespace HMIControl
{
    public class HVDial : Dial 
    {
        public static readonly DependencyProperty OrientationProperty = DependencyProperty.Register("Orientation", typeof(Orientation), typeof(Dial));
       
        public Orientation Orientation
        {
            get
            {
                return (Orientation)base.GetValue(OrientationProperty);
            }
            set
            {
                base.SetValue(OrientationProperty, value);
            }
        }
    }
}
