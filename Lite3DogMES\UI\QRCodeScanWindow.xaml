&lt;Window x:Class="Lite3DogMES.UI.QRCodeScanWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="二维码扫描" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#FF2D2D30">
    
    &lt;Window.Resources>
        &lt;Style x:Key="ModernButtonStyle" TargetType="Button">
            &lt;Setter Property="Background" Value="#FF007ACC"/>
            &lt;Setter Property="Foreground" Value="White"/>
            &lt;Setter Property="BorderThickness" Value="0"/>
            &lt;Setter Property="Padding" Value="15,8"/>
            &lt;Setter Property="Margin" Value="5"/>
            &lt;Setter Property="FontSize" Value="14"/>
            &lt;Setter Property="FontWeight" Value="SemiBold"/>
            &lt;Setter Property="Cursor" Value="Hand"/>
            &lt;Setter Property="Template">
                &lt;Setter.Value>
                    &lt;ControlTemplate TargetType="Button">
                        &lt;Border Background="{TemplateBinding Background}" 
                                CornerRadius="3"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            &lt;ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        &lt;/Border>
                        &lt;ControlTemplate.Triggers>
                            &lt;Trigger Property="IsMouseOver" Value="True">
                                &lt;Setter Property="Background" Value="#FF1E90FF"/>
                            &lt;/Trigger>
                            &lt;Trigger Property="IsPressed" Value="True">
                                &lt;Setter Property="Background" Value="#FF0066CC"/>
                            &lt;/Trigger>
                        &lt;/ControlTemplate.Triggers>
                    &lt;/ControlTemplate>
                &lt;/Setter.Value>
            &lt;/Setter>
        &lt;/Style>
    &lt;/Window.Resources>

    &lt;Grid Margin="20">
        &lt;Grid.RowDefinitions>
            &lt;RowDefinition Height="Auto"/>
            &lt;RowDefinition Height="*"/>
            &lt;RowDefinition Height="Auto"/>
            &lt;RowDefinition Height="Auto"/>
        &lt;/Grid.RowDefinitions>

        &lt;!-- 标题 -->
        &lt;TextBlock Grid.Row="0" Text="二维码扫描" FontSize="24" FontWeight="Bold" 
                 Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,20"/>

        &lt;!-- 摄像头预览区域 -->
        &lt;Border Grid.Row="1" Background="#FF3F3F46" CornerRadius="5" Padding="10">
            &lt;Grid>
                &lt;Grid.ColumnDefinitions>
                    &lt;ColumnDefinition Width="*"/>
                    &lt;ColumnDefinition Width="300"/>
                &lt;/Grid.ColumnDefinitions>

                &lt;!-- 摄像头预览 -->
                &lt;Border Grid.Column="0" Background="Black" CornerRadius="3" Margin="0,0,10,0">
                    &lt;Grid>
                        &lt;PictureBox x:Name="pictureBoxCamera" 
                                  Background="Black"
                                  HorizontalAlignment="Stretch"
                                  VerticalAlignment="Stretch"/>
                        &lt;TextBlock Text="摄像头预览区域" FontSize="18" Foreground="Gray"
                                 HorizontalAlignment="Center" VerticalAlignment="Center"
                                 x:Name="txtCameraPlaceholder"/>
                    &lt;/Grid>
                &lt;/Border>

                &lt;!-- 控制面板 -->
                &lt;StackPanel Grid.Column="1" Margin="10,0,0,0">
                    &lt;TextBlock Text="扫描控制" FontSize="16" FontWeight="Bold" 
                             Foreground="White" Margin="0,0,0,15"/>

                    &lt;GroupBox Header="摄像头设置" Foreground="White" Margin="0,0,0,15">
                        &lt;StackPanel>
                            &lt;TextBlock Text="选择摄像头:" Foreground="White" Margin="0,5"/>
                            &lt;ComboBox x:Name="cmbCameras" Height="30" Margin="0,5"
                                    Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"/>
                            
                            &lt;Button x:Name="btnStartCamera" Content="启动摄像头" 
                                  Style="{StaticResource ModernButtonStyle}" 
                                  Click="BtnStartCamera_Click" Margin="0,10,0,0"/>
                            
                            &lt;Button x:Name="btnStopCamera" Content="停止摄像头" 
                                  Style="{StaticResource ModernButtonStyle}" 
                                  Click="BtnStopCamera_Click" IsEnabled="False"/>
                        &lt;/StackPanel>
                    &lt;/GroupBox>

                    &lt;GroupBox Header="扫描操作" Foreground="White" Margin="0,0,0,15">
                        &lt;StackPanel>
                            &lt;Button x:Name="btnTriggerScan" Content="手动扫描" 
                                  Style="{StaticResource ModernButtonStyle}" 
                                  Click="BtnTriggerScan_Click" IsEnabled="False"/>
                            
                            &lt;CheckBox x:Name="chkAutoScan" Content="自动扫描" 
                                    Foreground="White" Margin="0,10" IsChecked="True"/>
                        &lt;/StackPanel>
                    &lt;/GroupBox>

                    &lt;GroupBox Header="扫描结果" Foreground="White">
                        &lt;StackPanel>
                            &lt;TextBlock Text="产品序号:" Foreground="White" Margin="0,5"/>
                            &lt;TextBox x:Name="txtScannedSN" Height="30" Margin="0,5"
                                   Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"
                                   IsReadOnly="True"/>
                            
                            &lt;TextBlock Text="扫描时间:" Foreground="White" Margin="0,5"/>
                            &lt;TextBox x:Name="txtScanTime" Height="30" Margin="0,5"
                                   Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"
                                   IsReadOnly="True"/>
                            
                            &lt;TextBlock Text="扫描状态:" Foreground="White" Margin="0,5"/>
                            &lt;TextBox x:Name="txtScanStatus" Height="30" Margin="0,5"
                                   Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"
                                   IsReadOnly="True" Text="等待扫描..."/>
                        &lt;/StackPanel>
                    &lt;/GroupBox>
                &lt;/StackPanel>
            &lt;/Grid>
        &lt;/Border>

        &lt;!-- 手动输入区域 -->
        &lt;Border Grid.Row="2" Background="#FF3F3F46" CornerRadius="5" Padding="15" Margin="0,10,0,0">
            &lt;StackPanel Orientation="Horizontal">
                &lt;TextBlock Text="手动输入产品序号:" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                &lt;TextBox x:Name="txtManualInput" Width="200" Height="30" Margin="0,0,10,0"
                       Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"/>
                &lt;Button x:Name="btnManualConfirm" Content="确认" Style="{StaticResource ModernButtonStyle}" 
                      Click="BtnManualConfirm_Click"/>
            &lt;/StackPanel>
        &lt;/Border>

        &lt;!-- 按钮区域 -->
        &lt;StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            &lt;Button x:Name="btnOK" Content="确定" Style="{StaticResource ModernButtonStyle}" 
                  Click="BtnOK_Click" IsEnabled="False" Width="100"/>
            &lt;Button x:Name="btnCancel" Content="取消" Style="{StaticResource ModernButtonStyle}" 
                  Click="BtnCancel_Click" Width="100"/>
        &lt;/StackPanel>
    &lt;/Grid>
&lt;/Window>
