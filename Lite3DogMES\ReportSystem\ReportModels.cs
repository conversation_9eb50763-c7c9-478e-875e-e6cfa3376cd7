using System;
using System.Collections.Generic;

namespace Lite3DogMES.ReportSystem
{
    /// <summary>
    /// 日生产报表
    /// </summary>
    public class DailyProductionReport
    {
        public DateTime ReportDate { get; set; }
        public DateTime GeneratedTime { get; set; }
        public string ErrorMessage { get; set; }
        
        public DailyProductionSummary ProductionSummary { get; set; }
        public List<ProcessStatistic> ProcessStatistics { get; set; } = new List<ProcessStatistic>();
        public QualityStatistic QualityStatistics { get; set; }
        public EfficiencyStatistic EfficiencyStatistics { get; set; }
        public ExceptionStatistic ExceptionStatistics { get; set; }
    }

    /// <summary>
    /// 周生产报表
    /// </summary>
    public class WeeklyProductionReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedTime { get; set; }
        public string ErrorMessage { get; set; }
        
        public List<DailyTrend> DailyTrends { get; set; } = new List<DailyTrend>();
        public WeeklySummary WeeklySummary { get; set; }
        public List<ProcessEfficiencyComparison> ProcessEfficiencyComparison { get; set; } = new List<ProcessEfficiencyComparison>();
        public List<QualityTrend> QualityTrends { get; set; } = new List<QualityTrend>();
    }

    /// <summary>
    /// 月生产报表
    /// </summary>
    public class MonthlyProductionReport
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedTime { get; set; }
        public string ErrorMessage { get; set; }
        
        public MonthlySummary MonthlySummary { get; set; }
        public List<DailyProductionTrend> DailyProductionTrends { get; set; } = new List<DailyProductionTrend>();
        public List<ProcessPerformanceAnalysis> ProcessPerformanceAnalysis { get; set; } = new List<ProcessPerformanceAnalysis>();
        public QualityAnalysis QualityAnalysis { get; set; }
        public List<EquipmentUtilization> EquipmentUtilization { get; set; } = new List<EquipmentUtilization>();
    }

    /// <summary>
    /// 产品追溯报表
    /// </summary>
    public class ProductTraceabilityReport
    {
        public string ProductSN { get; set; }
        public DateTime GeneratedTime { get; set; }
        public string ErrorMessage { get; set; }
        
        public ProductBasicInfo ProductInfo { get; set; }
        public List<ProcessExecutionDetail> ProcessExecutionDetails { get; set; } = new List<ProcessExecutionDetail>();
        public List<QualityTestDetail> QualityTestDetails { get; set; } = new List<QualityTestDetail>();
        public List<ExceptionRecord> ExceptionRecords { get; set; } = new List<ExceptionRecord>();
        public List<OperatorRecord> OperatorRecords { get; set; } = new List<OperatorRecord>();
        public ProductStatistics Statistics { get; set; }
    }

    /// <summary>
    /// 质量分析报表
    /// </summary>
    public class QualityAnalysisReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedTime { get; set; }
        public string ErrorMessage { get; set; }
        
        public QualityOverview QualityOverview { get; set; }
        public List<ProcessQualityAnalysis> ProcessQualityAnalysis { get; set; } = new List<ProcessQualityAnalysis>();
        public DefectAnalysis DefectAnalysis { get; set; }
        public List<QualityTrendAnalysis> QualityTrends { get; set; } = new List<QualityTrendAnalysis>();
        public List<string> ImprovementSuggestions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 效率分析报表
    /// </summary>
    public class EfficiencyAnalysisReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedTime { get; set; }
        public string ErrorMessage { get; set; }
        
        public EfficiencyOverview EfficiencyOverview { get; set; }
        public List<ProcessEfficiencyAnalysis> ProcessEfficiencyAnalysis { get; set; } = new List<ProcessEfficiencyAnalysis>();
        public BottleneckAnalysis BottleneckAnalysis { get; set; }
        public List<OperatorEfficiencyAnalysis> OperatorEfficiencyAnalysis { get; set; } = new List<OperatorEfficiencyAnalysis>();
        public List<EquipmentEfficiencyAnalysis> EquipmentEfficiencyAnalysis { get; set; } = new List<EquipmentEfficiencyAnalysis>();
        public List<string> ImprovementRecommendations { get; set; } = new List<string>();
    }

    // 基础数据模型
    /// <summary>
    /// 日生产汇总
    /// </summary>
    public class DailyProductionSummary
    {
        public int TotalProducts { get; set; }
        public int CompletedProducts { get; set; }
        public int QualifiedProducts { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal QualificationRate { get; set; }
    }

    /// <summary>
    /// 工序统计
    /// </summary>
    public class ProcessStatistic
    {
        public string ProcessName { get; set; }
        public string ProcessCode { get; set; }
        public int TotalCount { get; set; }
        public int CompletedCount { get; set; }
        public int QualifiedCount { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal QualificationRate { get; set; }
        public double AvgProcessTime { get; set; }
    }

    /// <summary>
    /// 质量统计
    /// </summary>
    public class QualityStatistic
    {
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public decimal PassRate { get; set; }
        public double AvgDeviation { get; set; }
    }

    /// <summary>
    /// 效率统计
    /// </summary>
    public class EfficiencyStatistic
    {
        public double AvgCycleTime { get; set; }
        public int ProductCount { get; set; }
        public double ProductionEfficiency { get; set; }
    }

    /// <summary>
    /// 异常统计
    /// </summary>
    public class ExceptionStatistic
    {
        public int QualityExceptions { get; set; }
        public int ProcessExceptions { get; set; }
        public int TimeoutExceptions { get; set; }
        public int TotalExceptions { get; set; }
    }

    /// <summary>
    /// 日趋势
    /// </summary>
    public class DailyTrend
    {
        public DateTime Date { get; set; }
        public int ProductCount { get; set; }
        public decimal QualityRate { get; set; }
        public double AvgCycleTime { get; set; }
    }

    /// <summary>
    /// 周汇总
    /// </summary>
    public class WeeklySummary
    {
        public int TotalProducts { get; set; }
        public int CompletedProducts { get; set; }
        public decimal AvgDailyProduction { get; set; }
        public decimal OverallQualityRate { get; set; }
        public double AvgCycleTime { get; set; }
    }

    /// <summary>
    /// 工序效率对比
    /// </summary>
    public class ProcessEfficiencyComparison
    {
        public string ProcessName { get; set; }
        public double CurrentWeekAvgTime { get; set; }
        public double PreviousWeekAvgTime { get; set; }
        public double ImprovementPercent { get; set; }
    }

    /// <summary>
    /// 质量趋势
    /// </summary>
    public class QualityTrend
    {
        public DateTime Date { get; set; }
        public decimal PassRate { get; set; }
        public int DefectCount { get; set; }
        public string MainDefectType { get; set; }
    }

    /// <summary>
    /// 月汇总
    /// </summary>
    public class MonthlySummary
    {
        public int TotalProducts { get; set; }
        public int CompletedProducts { get; set; }
        public decimal AvgDailyProduction { get; set; }
        public decimal MonthlyGrowthRate { get; set; }
        public decimal QualityRate { get; set; }
        public double AvgCycleTime { get; set; }
    }

    /// <summary>
    /// 日产量趋势
    /// </summary>
    public class DailyProductionTrend
    {
        public DateTime Date { get; set; }
        public int ProductionCount { get; set; }
        public int CompletedCount { get; set; }
        public decimal CompletionRate { get; set; }
    }

    /// <summary>
    /// 工序性能分析
    /// </summary>
    public class ProcessPerformanceAnalysis
    {
        public string ProcessName { get; set; }
        public double AvgProcessTime { get; set; }
        public double StandardTime { get; set; }
        public double EfficiencyRate { get; set; }
        public int BottleneckRank { get; set; }
    }

    /// <summary>
    /// 质量分析
    /// </summary>
    public class QualityAnalysis
    {
        public decimal OverallPassRate { get; set; }
        public int TotalDefects { get; set; }
        public string TopDefectType { get; set; }
        public decimal DefectRate { get; set; }
        public decimal ImprovementFromLastMonth { get; set; }
    }

    /// <summary>
    /// 设备利用率
    /// </summary>
    public class EquipmentUtilization
    {
        public string EquipmentName { get; set; }
        public double UtilizationRate { get; set; }
        public double UpTime { get; set; }
        public double DownTime { get; set; }
        public int MaintenanceCount { get; set; }
    }

    /// <summary>
    /// 产品基本信息
    /// </summary>
    public class ProductBasicInfo
    {
        public string ProductID { get; set; }
        public string ProductSN { get; set; }
        public string ProductModel { get; set; }
        public DateTime ProductionDate { get; set; }
        public string CurrentStatus { get; set; }
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// 工序执行详情
    /// </summary>
    public class ProcessExecutionDetail
    {
        public string ProcessName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public string Status { get; set; }
        public string QualityResult { get; set; }
        public string OperatorID { get; set; }
        public string Location { get; set; }
    }

    /// <summary>
    /// 质量测试详情
    /// </summary>
    public class QualityTestDetail
    {
        public string TestType { get; set; }
        public string TestParameter { get; set; }
        public decimal StandardValue { get; set; }
        public decimal ActualValue { get; set; }
        public decimal Deviation { get; set; }
        public string TestResult { get; set; }
        public DateTime TestTime { get; set; }
        public string TesterID { get; set; }
    }

    /// <summary>
    /// 异常记录
    /// </summary>
    public class ExceptionRecord
    {
        public DateTime OccurTime { get; set; }
        public string ExceptionType { get; set; }
        public string Description { get; set; }
        public string ProcessName { get; set; }
        public string OperatorID { get; set; }
        public string Resolution { get; set; }
    }

    /// <summary>
    /// 操作员记录
    /// </summary>
    public class OperatorRecord
    {
        public string OperatorID { get; set; }
        public string OperatorName { get; set; }
        public string ProcessName { get; set; }
        public DateTime OperationTime { get; set; }
        public string OperationType { get; set; }
        public string Result { get; set; }
    }

    /// <summary>
    /// 产品统计
    /// </summary>
    public class ProductStatistics
    {
        public TimeSpan TotalProcessTime { get; set; }
        public int TotalProcesses { get; set; }
        public int CompletedProcesses { get; set; }
        public int QualityTests { get; set; }
        public int PassedTests { get; set; }
        public decimal QualityPassRate { get; set; }
        public int ExceptionCount { get; set; }
        public int OperatorCount { get; set; }
    }

    // 质量分析相关模型
    public class QualityOverview
    {
        public decimal OverallPassRate { get; set; }
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        public decimal ImprovementFromLastPeriod { get; set; }
    }

    public class ProcessQualityAnalysis
    {
        public string ProcessName { get; set; }
        public decimal PassRate { get; set; }
        public int TotalTests { get; set; }
        public int DefectCount { get; set; }
        public string MainDefectType { get; set; }
    }

    public class DefectAnalysis
    {
        public List<DefectType> TopDefectTypes { get; set; } = new List<DefectType>();
        public int TotalDefects { get; set; }
        public decimal DefectRate { get; set; }
        public string TrendDirection { get; set; }
    }

    public class DefectType
    {
        public string TypeName { get; set; }
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    public class QualityTrendAnalysis
    {
        public DateTime Date { get; set; }
        public decimal PassRate { get; set; }
        public int DefectCount { get; set; }
        public decimal Trend { get; set; }
    }

    // 效率分析相关模型
    public class EfficiencyOverview
    {
        public double OverallEfficiency { get; set; }
        public double AvgCycleTime { get; set; }
        public double Throughput { get; set; }
        public double CapacityUtilization { get; set; }
    }

    public class ProcessEfficiencyAnalysis
    {
        public string ProcessName { get; set; }
        public double AvgProcessTime { get; set; }
        public double StandardTime { get; set; }
        public double EfficiencyRate { get; set; }
        public double UtilizationRate { get; set; }
    }

    public class BottleneckAnalysis
    {
        public string BottleneckProcess { get; set; }
        public double BottleneckTime { get; set; }
        public double ImpactOnOverallEfficiency { get; set; }
        public List<string> ImprovementSuggestions { get; set; } = new List<string>();
    }

    public class OperatorEfficiencyAnalysis
    {
        public string OperatorID { get; set; }
        public string OperatorName { get; set; }
        public double AvgProcessTime { get; set; }
        public double EfficiencyRate { get; set; }
        public int ProcessedCount { get; set; }
        public decimal QualityRate { get; set; }
    }

    public class EquipmentEfficiencyAnalysis
    {
        public string EquipmentName { get; set; }
        public double UtilizationRate { get; set; }
        public double AvgProcessTime { get; set; }
        public double DowntimeRate { get; set; }
        public int MaintenanceFrequency { get; set; }
    }
}
