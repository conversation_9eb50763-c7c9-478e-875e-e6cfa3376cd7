<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SuperSocket.SocketBase</name>
    </assembly>
    <members>
        <member name="T:SuperSocket.SocketBase.AppServerBase`2">
            <summary>
            AppServer base class
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.IAppServer`2">
            <summary>
            The interface for AppServer
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.IAppServer`1">
            <summary>
            The interface for AppServer
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.IAppServer">
            <summary>
            The interface for AppServer
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IWorkItem">
            <summary>
            An item can be started and stopped
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IWorkItemBase">
            <summary>
            An item can be started and stopped
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IStatusInfoSource">
            <summary>
            StatusInfo source interface
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IStatusInfoSource.GetServerStatusMetadata">
            <summary>
            Gets the server status metadata.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IStatusInfoSource.CollectServerStatus(SuperSocket.SocketBase.StatusInfoCollection)">
            <summary>
            Collects the bootstrap status.
            </summary>
            <param name="bootstrapStatus">The bootstrap status.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.ISystemEndPoint">
            <summary>
            The interface for endpoint who can send/receive system message with each other
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.ISystemEndPoint.TransferSystemMessage(System.String,System.Object)">
            <summary>
            Transfers the system message.
            </summary>
            <param name="messageType">Type of the message.</param>
            <param name="messageData">The message data.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.IWorkItemBase.Start">
            <summary>
            Starts this server instance.
            </summary>
            <returns>return true if start successfull, else false</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IWorkItemBase.ReportPotentialConfigChange(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Reports the potential configuration change.
            </summary>
            <param name="config">The server config which may be changed.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.IWorkItemBase.Stop">
            <summary>
            Stops this server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IWorkItemBase.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IWorkItemBase.Config">
            <summary>
            Gets the server's config.
            </summary>
            <value>
            The server's config.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IWorkItemBase.SessionCount">
            <summary>
            Gets the total session count.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IWorkItem.Setup(SuperSocket.SocketBase.IBootstrap,SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Provider.ProviderFactoryInfo[])">
            <summary>
            Setups with the specified root config.
            </summary>
            <param name="bootstrap">The bootstrap.</param>
            <param name="config">The socket server instance config.</param>
            <param name="factories">The factories.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.IWorkItem.State">
            <summary>
            Gets the current state of the work item.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.ILoggerProvider">
            <summary>
            The interface for who provides logger
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ILoggerProvider.Logger">
            <summary>
            Gets the logger assosiated with this object.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppServer.CreateAppSession(SuperSocket.SocketBase.ISocketSession)">
            <summary>
            Creates the app session.
            </summary>
            <param name="socketSession">The socket session.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppServer.RegisterSession(SuperSocket.SocketBase.IAppSession)">
            <summary>
            Registers the new created app session into the appserver's session container.
            </summary>
            <param name="session">The session.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppServer.GetSessionByID(System.String)">
            <summary>
            Gets the app session by ID.
            </summary>
            <param name="sessionID">The session ID.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppServer.ResetSessionSecurity(SuperSocket.SocketBase.IAppSession,System.Security.Authentication.SslProtocols)">
            <summary>
            Resets the session's security protocol.
            </summary>
            <param name="session">The session.</param>
            <param name="security">The security protocol.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppServer.StartedTime">
            <summary>
            Gets the started time.
            </summary>
            <value>
            The started time.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppServer.Listeners">
            <summary>
            Gets or sets the listeners.
            </summary>
            <value>
            The listeners.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppServer.ReceiveFilterFactory">
            <summary>
            Gets the Receive filter factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppServer.Certificate">
            <summary>
            Gets the certificate of current server.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppServer.BasicSecurity">
            <summary>
            Gets the transfer layer security protocol.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppServer.LogFactory">
            <summary>
            Gets the log factory.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppServer`1.GetSessions(System.Func{`0,System.Boolean})">
            <summary>
            Gets the matched sessions from sessions snapshot.
            </summary>
            <param name="critera">The prediction critera.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppServer`1.GetAllSessions">
            <summary>
            Gets all sessions in sessions snapshot.
            </summary>
            <returns></returns>
        </member>
        <member name="E:SuperSocket.SocketBase.IAppServer`1.NewSessionConnected">
            <summary>
            Gets/sets the new session connected event handler.
            </summary>
        </member>
        <member name="E:SuperSocket.SocketBase.IAppServer`1.SessionClosed">
            <summary>
            Gets/sets the session closed event handler.
            </summary>
        </member>
        <member name="E:SuperSocket.SocketBase.IAppServer`2.NewRequestReceived">
            <summary>
            Occurs when [request comming].
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IRawDataProcessor`1">
            <summary>
            The raw data processor
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
        </member>
        <member name="E:SuperSocket.SocketBase.IRawDataProcessor`1.RawDataReceived">
            <summary>
            Gets or sets the raw binary data received event handler.
            TAppSession: session
            byte[]: receive buffer
            int: receive buffer offset
            int: receive lenght
            bool: whether process the received data further
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IRequestHandler`1">
            <summary>
            The interface for handler of session request
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.IRequestHandler`1.ExecuteCommand(SuperSocket.SocketBase.IAppSession,`0)">
            <summary>
            Executes the command.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.ISocketServerAccessor">
            <summary>
            SocketServer Accessor interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketServerAccessor.SocketServer">
            <summary>
            Gets the socket server.
            </summary>
            <value>
            The socket server.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.IRemoteCertificateValidator">
            <summary>
            The basic interface for RemoteCertificateValidator
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IRemoteCertificateValidator.Validate(SuperSocket.SocketBase.IAppSession,System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validates the remote certificate
            </summary>
            <param name="session">The session.</param>
            <param name="sender">The sender.</param>
            <param name="certificate">The certificate.</param>
            <param name="chain">The chain.</param>
            <param name="sslPolicyErrors">The SSL policy errors.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.IActiveConnector">
            <summary>
            The inerface to connect the remote endpoint actively
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IActiveConnector.ActiveConnect(System.Net.EndPoint)">
            <summary>
            Connect the target endpoint actively.
            </summary>
            <param name="targetEndPoint">The target end point.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IActiveConnector.ActiveConnect(System.Net.EndPoint,System.Net.EndPoint)">
            <summary>
            Connect the target endpoint actively.
            </summary>
            <param name="targetEndPoint">The target end point.</param>
            <param name="localEndPoint">The local end point.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.RegisterConfigHandler``1(SuperSocket.SocketBase.Config.IServerConfig,System.String,System.Func{``0,System.Boolean})">
            <summary>
            Registers the configuration option value handler, it is used for reading configuration value and reload it after the configuration is changed;
            </summary>
            <typeparam name="TConfigOption">The type of the configuration option.</typeparam>
            <param name="config">The server configuration.</param>
            <param name="name">The changed config option's name.</param>
            <param name="handler">The handler.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.RegisterConfigHandler(SuperSocket.SocketBase.Config.IServerConfig,System.String,System.Func{System.String,System.Boolean})">
            <summary>
            Registers the configuration option value handler, it is used for reading configuration value and reload it after the configuration is changed;
            </summary>
            <param name="config">The server configuration.</param>
            <param name="name">The changed config option name.</param>
            <param name="handler">The handler.</param>
        </member>
        <member name="F:SuperSocket.SocketBase.AppServerBase`2.NullAppSession">
            <summary>
            Null appSession instance
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.AppServerBase`2.m_StateCode">
            <summary>
            the current state's code
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServerBase`2"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.#ctor(SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{`1})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServerBase`2"/> class.
            </summary>
            <param name="receiveFilterFactory">The Receive filter factory.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetCommandFilterAttributes(System.Type)">
            <summary>
            Gets the filter attributes.
            </summary>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SetupCommands(System.Collections.Generic.Dictionary{System.String,SuperSocket.SocketBase.Command.ICommand{`0,`1}})">
            <summary>
            Setups the command into command dictionary
            </summary>
            <param name="discoveredCommands">The discovered commands.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Setup(SuperSocket.SocketBase.Config.IRootConfig,SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Setups the specified root config.
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="config">The config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Setup(System.Int32)">
            <summary>
            Setups with the specified port.
            </summary>
            <param name="port">The port.</param>
            <returns>return setup result</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Setup(SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.ISocketServerFactory,SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{`1},SuperSocket.SocketBase.Logging.ILogFactory,System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.IConnectionFilter},System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.Command.ICommandLoader{SuperSocket.SocketBase.Command.ICommand{`0,`1}}})">
            <summary>
            Setups with the specified config.
            </summary>
            <param name="config">The server config.</param>
            <param name="socketServerFactory">The socket server factory.</param>
            <param name="receiveFilterFactory">The receive filter factory.</param>
            <param name="logFactory">The log factory.</param>
            <param name="connectionFilters">The connection filters.</param>
            <param name="commandLoaders">The command loaders.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Setup(SuperSocket.SocketBase.Config.IRootConfig,SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.ISocketServerFactory,SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{`1},SuperSocket.SocketBase.Logging.ILogFactory,System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.IConnectionFilter},System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.Command.ICommandLoader{SuperSocket.SocketBase.Command.ICommand{`0,`1}}})">
            <summary>
            Setups the specified root config, this method used for programming setup
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="config">The server config.</param>
            <param name="socketServerFactory">The socket server factory.</param>
            <param name="receiveFilterFactory">The Receive filter factory.</param>
            <param name="logFactory">The log factory.</param>
            <param name="connectionFilters">The connection filters.</param>
            <param name="commandLoaders">The command loaders.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Setup(System.String,System.Int32,SuperSocket.SocketBase.ISocketServerFactory,SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{`1},SuperSocket.SocketBase.Logging.ILogFactory,System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.IConnectionFilter},System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.Command.ICommandLoader{SuperSocket.SocketBase.Command.ICommand{`0,`1}}})">
            <summary>
            Setups with the specified ip and port.
            </summary>
            <param name="ip">The ip.</param>
            <param name="port">The port.</param>
            <param name="socketServerFactory">The socket server factory.</param>
            <param name="receiveFilterFactory">The Receive filter factory.</param>
            <param name="logFactory">The log factory.</param>
            <param name="connectionFilters">The connection filters.</param>
            <param name="commandLoaders">The command loaders.</param>
            <returns>return setup result</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IWorkItem#Setup(SuperSocket.SocketBase.IBootstrap,SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Provider.ProviderFactoryInfo[])">
            <summary>
            Setups the specified root config.
            </summary>
            <param name="bootstrap">The bootstrap.</param>
            <param name="config">The socket server instance config.</param>
            <param name="factories">The factories.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SetupCommandLoaders(System.Collections.Generic.List{SuperSocket.SocketBase.Command.ICommandLoader{SuperSocket.SocketBase.Command.ICommand{`0,`1}}})">
            <summary>
            Setups the command loaders.
            </summary>
            <param name="commandLoaders">The command loaders.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.CreateLogger(System.String)">
            <summary>
            Creates the logger for the AppServer.
            </summary>
            <param name="loggerName">Name of the logger.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SetupSecurity(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Setups the security option of socket communications.
            </summary>
            <param name="config">The config of the server instance.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetCertificate(SuperSocket.SocketBase.Config.ICertificateConfig)">
            <summary>
            Gets the certificate from server configuguration.
            </summary>
            <param name="certificate">The certificate config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.ValidateClientCertificate(`0,System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validates the client certificate. This method is only used if the certificate configuration attribute "clientCertificateRequired" is true.
            </summary>
            <param name="session">The session.</param>
            <param name="sender">The sender.</param>
            <param name="certificate">The certificate.</param>
            <param name="chain">The chain.</param>
            <param name="sslPolicyErrors">The SSL policy errors.</param>
            <returns>return the validation result</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SetupSocketServer">
            <summary>
            Setups the socket server.instance
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SetupListeners(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Setups the listeners base on server configuration
            </summary>
            <param name="config">The config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Start">
            <summary>
            Starts this server instance.
            </summary>
            <returns>
            return true if start successfull, else false
            </returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnStartup">
            <summary>
            Called when [startup].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnStarted">
            <summary>
            Called when [started].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnStopped">
            <summary>
            Called when [stopped].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Stop">
            <summary>
            Stops this server instance.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetCommandByName(System.String)">
            <summary>
            Gets command by command name.
            </summary>
            <param name="commandName">Name of the command.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnRawDataReceived(SuperSocket.SocketBase.IAppSession,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Called when [raw data received].
            </summary>
            <param name="session">The session.</param>
            <param name="buffer">The buffer.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.ExecuteCommand(`0,`1)">
            <summary>
            Executes the command.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.ExecuteCommand(SuperSocket.SocketBase.IAppSession,`1)">
            <summary>
            Executes the command for the session.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IRequestHandler{TRequestInfo}#ExecuteCommand(SuperSocket.SocketBase.IAppSession,`1)">
            <summary>
            Executes the command.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.ExecuteConnectionFilters(System.Net.IPEndPoint)">
            <summary>
            Executes the connection filters.
            </summary>
            <param name="remoteAddress">The remote address.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IAppServer#CreateAppSession(SuperSocket.SocketBase.ISocketSession)">
            <summary>
            Creates the app session.
            </summary>
            <param name="socketSession">The socket session.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.CreateAppSession(SuperSocket.SocketBase.ISocketSession)">
            <summary>
            create a new TAppSession instance, you can override it to create the session instance in your own way
            </summary>
            <param name="socketSession">the socket session.</param>
            <returns>the new created session instance</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IAppServer#RegisterSession(SuperSocket.SocketBase.IAppSession)">
            <summary>
            Registers the new created app session into the appserver's session container.
            </summary>
            <param name="session">The session.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.RegisterSession(System.String,`0)">
            <summary>
            Registers the session into session container.
            </summary>
            <param name="sessionID">The session ID.</param>
            <param name="appSession">The app session.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnNewSessionConnected(`0)">
            <summary>
            Called when [new session connected].
            </summary>
            <param name="session">The session.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.ResetSessionSecurity(SuperSocket.SocketBase.IAppSession,System.Security.Authentication.SslProtocols)">
            <summary>
            Resets the session's security protocol.
            </summary>
            <param name="session">The session.</param>
            <param name="security">The security protocol.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnSocketSessionClosed(SuperSocket.SocketBase.ISocketSession,SuperSocket.SocketBase.CloseReason)">
            <summary>
            Called when [socket session closed].
            </summary>
            <param name="session">The socket session.</param>
            <param name="reason">The reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnSessionClosed(`0,SuperSocket.SocketBase.CloseReason)">
            <summary>
            Called when [session closed].
            </summary>
            <param name="session">The appSession.</param>
            <param name="reason">The reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetSessionByID(System.String)">
            <summary>
            Gets the app session by ID.
            </summary>
            <param name="sessionID">The session ID.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IAppServer#GetSessionByID(System.String)">
            <summary>
            Gets the app session by ID.
            </summary>
            <param name="sessionID"></param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetSessions(System.Func{`0,System.Boolean})">
            <summary>
            Gets the matched sessions from sessions snapshot.
            </summary>
            <param name="critera">The prediction critera.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetAllSessions">
            <summary>
            Gets all sessions in sessions snapshot.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.GetFilePath(System.String)">
            <summary>
            Gets the physical file path by the relative file path,
            search both in the appserver's root and in the supersocket root dir if the isolation level has been set other than 'None'.
            </summary>
            <param name="relativeFilePath">The relative file path.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IActiveConnector#ActiveConnect(System.Net.EndPoint,System.Net.EndPoint)">
            <summary>
            Connect the remote endpoint actively.
            </summary>
            <param name="targetEndPoint">The target end point.</param>
            <param name="localEndPoint">The local end point.</param>
            <returns></returns>
            <exception cref="T:System.Exception">This server cannot support active connect.</exception>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IActiveConnector#ActiveConnect(System.Net.EndPoint)">
            <summary>
            Connect the remote endpoint actively.
            </summary>
            <param name="targetEndPoint">The target end point.</param>
            <returns></returns>
            <exception cref="T:System.Exception">This server cannot support active connect.</exception>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#ISystemEndPoint#TransferSystemMessage(System.String,System.Object)">
            <summary>
            Transfers the system message
            </summary>
            <param name="messageType">Type of the message.</param>
            <param name="messageData">The message data.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnSystemMessageReceived(System.String,System.Object)">
            <summary>
            Called when [system message received].
            </summary>
            <param name="messageType">Type of the message.</param>
            <param name="messageData">The message data.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.UpdateServerStatus(SuperSocket.SocketBase.StatusInfoCollection)">
            <summary>
            Updates the summary of the server.
            </summary>
            <param name="serverStatus">The server status.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.OnServerStatusCollected(SuperSocket.SocketBase.StatusInfoCollection,SuperSocket.SocketBase.StatusInfoCollection)">
            <summary>
            Called when [server status collected].
            </summary>
            <param name="bootstrapStatus">The bootstrapStatus status.</param>
            <param name="serverStatus">The server status.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServerBase`2.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.Config">
            <summary>
            Gets the server's config.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.State">
            <summary>
            Gets the current state of the work item.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.Certificate">
            <summary>
            Gets the certificate of current server.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.ReceiveFilterFactory">
            <summary>
            Gets or sets the receive filter factory.
            </summary>
            <value>
            The receive filter factory.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IAppServer#ReceiveFilterFactory">
            <summary>
            Gets the Receive filter factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.BasicSecurity">
            <summary>
            Gets the basic transfer layer security protocol.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.RootConfig">
            <summary>
            Gets the root config.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.Logger">
            <summary>
            Gets the logger assosiated with this object.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.Bootstrap">
            <summary>
            Gets the bootstrap of this appServer instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.TotalHandledRequests">
            <summary>
            Gets the total handled requests number.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.Listeners">
            <summary>
            Gets or sets the listeners inforamtion.
            </summary>
            <value>
            The listeners.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.StartedTime">
            <summary>
            Gets the started time of this server instance.
            </summary>
            <value>
            The started time.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.LogFactory">
            <summary>
            Gets or sets the log factory.
            </summary>
            <value>
            The log factory.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.TextEncoding">
            <summary>
            Gets the default text encoding.
            </summary>
            <value>
            The text encoding.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.Name">
            <summary>
            Gets the name of the server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#ISocketServerAccessor#SocketServer">
            <summary>
            Gets the socket server.
            </summary>
            <value>
            The socket server.
            </value>
        </member>
        <member name="E:SuperSocket.SocketBase.AppServerBase`2.SuperSocket#SocketBase#IRawDataProcessor{TAppSession}#RawDataReceived">
            <summary>
            Gets or sets the raw binary data received event handler.
            TAppSession: session
            byte[]: receive buffer
            int: receive buffer offset
            int: receive lenght
            bool: whether process the received data further
            </summary>
        </member>
        <member name="E:SuperSocket.SocketBase.AppServerBase`2.NewRequestReceived">
            <summary>
            Occurs when a full request item received.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.ConnectionFilters">
            <summary>
            Gets or sets the server's connection filter
            </summary>
            <value>
            The server's connection filters
            </value>
        </member>
        <member name="E:SuperSocket.SocketBase.AppServerBase`2.NewSessionConnected">
            <summary>
            The action which will be executed after a new session connect
            </summary>
        </member>
        <member name="E:SuperSocket.SocketBase.AppServerBase`2.SessionClosed">
            <summary>
            Gets/sets the session closed event handler.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServerBase`2.SessionCount">
            <summary>
            Gets the total session count.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Async">
            <summary>
            Async extension class
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action)">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action,System.Threading.Tasks.TaskCreationOptions)">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="taskOption">The task option.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action,System.Action{System.Exception})">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="exceptionHandler">The exception handler.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action,System.Threading.Tasks.TaskCreationOptions,System.Action{System.Exception})">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="taskOption">The task option.</param>
            <param name="exceptionHandler">The exception handler.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action{System.Object},System.Object)">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="state">The state.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="state">The state.</param>
            <param name="taskOption">The task option.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action{System.Object},System.Object,System.Action{System.Exception})">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="state">The state.</param>
            <param name="exceptionHandler">The exception handler.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Async.AsyncRun(SuperSocket.SocketBase.ILoggerProvider,System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions,System.Action{System.Exception})">
            <summary>
            Runs the specified task.
            </summary>
            <param name="logProvider">The log provider.</param>
            <param name="task">The task.</param>
            <param name="state">The state.</param>
            <param name="taskOption">The task option.</param>
            <param name="exceptionHandler">The exception handler.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.CommandExecutingContext">
            <summary>
            Command Executing Context
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.CommandExecutingContext.Initialize(SuperSocket.SocketBase.IAppSession,SuperSocket.SocketBase.Protocol.IRequestInfo,SuperSocket.SocketBase.Command.ICommand)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.CommandExecutingContext"/> class.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
            <param name="command">The command.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.CommandExecutingContext.Session">
            <summary>
            Gets the session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.CommandExecutingContext.RequestInfo">
            <summary>
            Gets the request info.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.CommandExecutingContext.CurrentCommand">
            <summary>
            Gets the current command.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.CommandExecutingContext.Exception">
            <summary>
            Gets the exception.
            </summary>
            <value>
            The exception.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.CommandExecutingContext.ExceptionHandled">
            <summary>
            Gets a value indicating whether [exception handled].
            </summary>
            <value>
              <c>true</c> if [exception handled]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.CommandExecutingContext.Cancel">
            <summary>
            Gets or sets a value indicating whether this command executing is cancelled.
            </summary>
            <value>
              <c>true</c> if cancel; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.CommandLoaderBase`1">
            <summary>
            CommandLoader base class
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.ICommandLoader`1">
            <summary>
            Command loader's interface
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.ICommandLoader">
            <summary>
            the empty basic interface for command loader
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ICommandLoader`1.Initialize(SuperSocket.SocketBase.Config.IRootConfig,SuperSocket.SocketBase.IAppServer)">
            <summary>
            Initializes the command loader by the root config and the server instance.
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="appServer">The app server.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ICommandLoader`1.TryLoadCommands(System.Collections.Generic.IEnumerable{`0}@)">
            <summary>
            Tries to load commands.
            </summary>
            <param name="commands">The commands.</param>
            <returns></returns>
        </member>
        <member name="E:SuperSocket.SocketBase.Command.ICommandLoader`1.Updated">
            <summary>
            Occurs when [updated].
            </summary>
        </member>
        <member name="E:SuperSocket.SocketBase.Command.ICommandLoader`1.Error">
            <summary>
            Occurs when [error].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandLoaderBase`1.Initialize(SuperSocket.SocketBase.Config.IRootConfig,SuperSocket.SocketBase.IAppServer)">
            <summary>
            Initializes the command loader by the root config and appserver instance.
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="appServer">The app server.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandLoaderBase`1.TryLoadCommands(System.Collections.Generic.IEnumerable{`0}@)">
            <summary>
            Tries to load commands.
            </summary>
            <param name="commands">The commands.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandLoaderBase`1.OnUpdated(System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.Command.CommandUpdateInfo{`0}})">
            <summary>
            Called when [updated].
            </summary>
            <param name="commands">The commands.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandLoaderBase`1.OnError(System.String)">
            <summary>
            Called when [error].
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandLoaderBase`1.OnError(System.Exception)">
            <summary>
            Called when [error].
            </summary>
            <param name="e">The e.</param>
        </member>
        <member name="E:SuperSocket.SocketBase.Command.CommandLoaderBase`1.Updated">
            <summary>
            Occurs when [updated].
            </summary>
        </member>
        <member name="E:SuperSocket.SocketBase.Command.CommandLoaderBase`1.Error">
            <summary>
            Occurs when [error].
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.CommandUpdateEventArgs`1">
            <summary>
            CommandUpdateEventArgs
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandUpdateEventArgs`1.#ctor(System.Collections.Generic.IEnumerable{SuperSocket.SocketBase.Command.CommandUpdateInfo{`0}})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Command.CommandUpdateEventArgs`1"/> class.
            </summary>
            <param name="commands">The commands.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Command.CommandUpdateEventArgs`1.Commands">
            <summary>
            Gets the commands updated.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.ICommandFilterProvider">
            <summary>
            The basic interface for CommandFilter
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ICommandFilterProvider.GetFilters">
            <summary>
            Gets the filters which assosiated with this command object.
            </summary>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.CommandAssemblyConfig">
            <summary>
            Command assembly config
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.ICommandAssemblyConfig">
            <summary>
            The basic interface for command assembly config
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICommandAssemblyConfig.Assembly">
            <summary>
            Gets the assembly name.
            </summary>
            <value>
            The assembly.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CommandAssemblyConfig.Assembly">
            <summary>
            Gets or sets the assembly name.
            </summary>
            <value>
            The assembly.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.IConfigurationSource">
            <summary>
            Configuration source interface
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.IRootConfig">
            <summary>
            IRootConfig, the part compatible with .Net 4.5 or higher
            </summary>
            <summary>
            The root configuration interface
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.IRootConfig.GetChildConfig``1(System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.DefaultCulture">
            <summary>
            Gets the default culture for all server instances.
            </summary>
            <value>
            The default culture.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.MaxWorkingThreads">
            <summary>
            Gets the max working threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.MinWorkingThreads">
            <summary>
            Gets the min working threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.MaxCompletionPortThreads">
            <summary>
            Gets the max completion port threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.MinCompletionPortThreads">
            <summary>
            Gets the min completion port threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.DisablePerformanceDataCollector">
            <summary>
            Gets a value indicating whether [disable performance data collector].
            </summary>
            <value>
            	<c>true</c> if [disable performance data collector]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.PerformanceDataCollectInterval">
            <summary>
            Gets the performance data collect interval, in seconds.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.LogFactory">
            <summary>
            Gets the log factory name.
            </summary>
            <value>
            The log factory.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.Isolation">
            <summary>
            Gets the isolation mode.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IRootConfig.OptionElements">
            <summary>
            Gets the option elements.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IConfigurationSource.Servers">
            <summary>
            Gets the servers definitions.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IConfigurationSource.ServerTypes">
            <summary>
            Gets the appServer types definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IConfigurationSource.ConnectionFilters">
            <summary>
            Gets the connection filters definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IConfigurationSource.LogFactories">
            <summary>
            Gets the log factories definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IConfigurationSource.ReceiveFilterFactories">
            <summary>
            Gets the Receive filter factories definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IConfigurationSource.CommandLoaders">
            <summary>
            Gets the command loaders definition.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.IServerConfig">
            <summary>
            IServerConfig, the part compatible with .Net 4.5 or higher
            </summary>
            <summary>
            Server instance configuation interface
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.IServerConfig.GetChildConfig``1(System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.DefaultCulture">
            <summary>
            Gets the default culture for this server.
            </summary>
            <value>
            The default culture.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ServerTypeName">
            <summary>
            Gets the name of the server type this appServer want to use.
            </summary>
            <value>
            The name of the server type.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ServerType">
            <summary>
            Gets the type definition of the appserver.
            </summary>
            <value>
            The type of the server.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ReceiveFilterFactory">
            <summary>
            Gets the Receive filter factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Ip">
            <summary>
            Gets the ip.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Port">
            <summary>
            Gets the port.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Options">
            <summary>
            Gets the options.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.OptionElements">
            <summary>
            Gets the option elements.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Disabled">
            <summary>
            Gets a value indicating whether this <see cref="T:SuperSocket.SocketBase.Config.IServerConfig"/> is disabled.
            </summary>
            <value>
              <c>true</c> if disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Mode">
            <summary>
            Gets the mode.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.SendTimeOut">
            <summary>
            Gets the send time out.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.MaxConnectionNumber">
            <summary>
            Gets the max connection number.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ReceiveBufferSize">
            <summary>
            Gets the size of the receive buffer.
            </summary>
            <value>
            The size of the receive buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.SendBufferSize">
            <summary>
            Gets the size of the send buffer.
            </summary>
            <value>
            The size of the send buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.SyncSend">
            <summary>
            Gets a value indicating whether sending is in synchronous mode.
            </summary>
            <value>
              <c>true</c> if [sync send]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.LogCommand">
            <summary>
            Gets a value indicating whether log command in log file.
            </summary>
            <value><c>true</c> if log command; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ClearIdleSession">
            <summary>
            Gets a value indicating whether clear idle session.
            </summary>
            <value><c>true</c> if clear idle session; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ClearIdleSessionInterval">
            <summary>
            Gets the clear idle session interval, in seconds.
            </summary>
            <value>The clear idle session interval.</value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.IdleSessionTimeOut">
            <summary>
            Gets the idle session timeout time length, in seconds.
            </summary>
            <value>The idle session time out.</value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Certificate">
            <summary>
            Gets X509Certificate configuration.
            </summary>
            <value>X509Certificate configuration.</value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Security">
            <summary>
            Gets the security protocol, X509 certificate.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.MaxRequestLength">
            <summary>
            Gets the length of the max request.
            </summary>
            <value>
            The length of the max request.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.DisableSessionSnapshot">
            <summary>
            Gets a value indicating whether [disable session snapshot].
            </summary>
            <value>
            	<c>true</c> if [disable session snapshot]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.SessionSnapshotInterval">
            <summary>
            Gets the interval to taking snapshot for all live sessions.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ConnectionFilter">
            <summary>
            Gets the connection filters used by this server instance.
            </summary>
            <value>
            The connection filter's name list, seperated by comma
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.CommandLoader">
            <summary>
            Gets the command loader, multiple values should be separated by comma.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.KeepAliveTime">
            <summary>
            Gets the start keep alive time, in seconds
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.KeepAliveInterval">
            <summary>
            Gets the keep alive interval, in seconds.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.ListenBacklog">
            <summary>
            Gets the backlog size of socket listening.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.StartupOrder">
            <summary>
            Gets the startup order of the server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.Listeners">
            <summary>
            Gets the listeners' configuration.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.LogFactory">
            <summary>
            Gets the log factory name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.SendingQueueSize">
            <summary>
            Gets the size of the sending queue.
            </summary>
            <value>
            The size of the sending queue.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.LogBasicSessionActivity">
            <summary>
            Gets a value indicating whether [log basic session activity like connected and disconnected].
            </summary>
            <value>
            	<c>true</c> if [log basic session activity]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.LogAllSocketException">
            <summary>
            Gets a value indicating whether [log all socket exception].
            </summary>
            <value>
            <c>true</c> if [log all socket exception]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.TextEncoding">
            <summary>
            Gets the default text encoding.
            </summary>
            <value>
            The text encoding.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IServerConfig.CommandAssemblies">
            <summary>
            Gets the command assemblies configuration.
            </summary>
            <value>
            The command assemblies.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.ITypeProvider">
            <summary>
            TypeProvider's interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ITypeProvider.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ITypeProvider.Type">
            <summary>
            Gets the type.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.ConfigurationSource">
            <summary>
            Poco configuration source
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.RootConfig">
            <summary>
            Root configuration model
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.RootConfig.#ctor(SuperSocket.SocketBase.Config.IRootConfig)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.RootConfig"/> class.
            </summary>
            <param name="rootConfig">The root config.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.RootConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.RootConfig"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.RootConfig.GetChildConfig``1(System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.DefaultCulture">
            <summary>
            Gets or sets the default culture.
            </summary>
            <value>
            The default culture.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.MaxWorkingThreads">
            <summary>
            Gets/Sets the max working threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.MinWorkingThreads">
            <summary>
            Gets/sets the min working threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.MaxCompletionPortThreads">
            <summary>
            Gets/sets the max completion port threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.MinCompletionPortThreads">
            <summary>
            Gets/sets the min completion port threads.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.PerformanceDataCollectInterval">
            <summary>
            Gets/sets the performance data collect interval, in seconds.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.DisablePerformanceDataCollector">
            <summary>
            Gets/sets a value indicating whether [disable performance data collector].
            </summary>
            <value>
            	<c>true</c> if [disable performance data collector]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.Isolation">
            <summary>
            Gets/sets the isolation mode.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.LogFactory">
            <summary>
            Gets/sets the log factory name.
            </summary>
            <value>
            The log factory.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.RootConfig.OptionElements">
            <summary>
            Gets/sets the option elements.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.ConfigurationSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.ConfigurationSource"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.ConfigurationSource.#ctor(SuperSocket.SocketBase.Config.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.ConfigurationSource"/> class.
            </summary>
            <param name="source">The source.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ConfigurationSource.Servers">
            <summary>
            Gets the servers definitions.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ConfigurationSource.ServerTypes">
            <summary>
            Gets/sets the server types definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ConfigurationSource.ConnectionFilters">
            <summary>
            Gets/sets the connection filters definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ConfigurationSource.LogFactories">
            <summary>
            Gets/sets the log factories definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ConfigurationSource.ReceiveFilterFactories">
            <summary>
            Gets/sets the Receive filter factories definition.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ConfigurationSource.CommandLoaders">
            <summary>
            Gets/sets the command loaders definition.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.ServerConfig">
            <summary>
            Server configruation model
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultReceiveBufferSize">
            <summary>
            Default ReceiveBufferSize
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultMaxConnectionNumber">
            <summary>
            Default MaxConnectionNumber
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultSendingQueueSize">
            <summary>
            Default sending queue size
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultMaxRequestLength">
            <summary>
            Default MaxRequestLength
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultSendTimeout">
            <summary>
            Default send timeout value, in milliseconds
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultClearIdleSessionInterval">
            <summary>
            Default clear idle session interval
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultIdleSessionTimeOut">
            <summary>
            Default idle session timeout
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultSendBufferSize">
            <summary>
            The default send buffer size
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultSessionSnapshotInterval">
            <summary>
            The default session snapshot interval
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultKeepAliveTime">
            <summary>
            The default keep alive time
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultKeepAliveInterval">
            <summary>
            The default keep alive interval
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Config.ServerConfig.DefaultListenBacklog">
            <summary>
            The default listen backlog
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.ServerConfig.#ctor(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.ServerConfig"/> class.
            </summary>
            <param name="serverConfig">The server config.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.ServerConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.ServerConfig"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.ServerConfig.GetChildConfig``1(System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.DefaultCulture">
            <summary>
            Gets or sets the default culture.
            </summary>
            <value>
            The default culture.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ServerTypeName">
            <summary>
            Gets/sets the name of the server type of this appServer want to use.
            </summary>
            <value>
            The name of the server type.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ServerType">
            <summary>
            Gets/sets the type definition of the appserver.
            </summary>
            <value>
            The type of the server.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ReceiveFilterFactory">
            <summary>
            Gets/sets the Receive filter factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Ip">
            <summary>
            Gets/sets the ip.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Port">
            <summary>
            Gets/sets the port.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Options">
            <summary>
            Gets/sets the options.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.OptionElements">
            <summary>
            Gets the option elements.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Disabled">
            <summary>
            Gets/sets a value indicating whether this <see cref="T:SuperSocket.SocketBase.Config.IServerConfig"/> is disabled.
            </summary>
            <value>
              <c>true</c> if disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Mode">
            <summary>
            Gets/sets the mode.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.SendTimeOut">
            <summary>
            Gets/sets the send time out.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.MaxConnectionNumber">
            <summary>
            Gets the max connection number.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ReceiveBufferSize">
            <summary>
            Gets the size of the receive buffer.
            </summary>
            <value>
            The size of the receive buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.SendBufferSize">
            <summary>
            Gets the size of the send buffer.
            </summary>
            <value>
            The size of the send buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.SyncSend">
            <summary>
            Gets a value indicating whether sending is in synchronous mode.
            </summary>
            <value>
              <c>true</c> if [sync send]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.LogCommand">
            <summary>
            Gets/sets a value indicating whether log command in log file.
            </summary>
            <value>
              <c>true</c> if log command; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ClearIdleSession">
            <summary>
            Gets/sets a value indicating whether clear idle session.
            </summary>
            <value>
              <c>true</c> if clear idle session; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ClearIdleSessionInterval">
            <summary>
            Gets/sets the clear idle session interval, in seconds.
            </summary>
            <value>
            The clear idle session interval.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.IdleSessionTimeOut">
            <summary>
            Gets/sets the idle session timeout time length, in seconds.
            </summary>
            <value>
            The idle session time out.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Certificate">
            <summary>
            Gets/sets X509Certificate configuration.
            </summary>
            <value>
            X509Certificate configuration.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Security">
            <summary>
            Gets/sets the security protocol, X509 certificate.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.MaxRequestLength">
            <summary>
            Gets/sets the length of the max request.
            </summary>
            <value>
            The length of the max request.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.DisableSessionSnapshot">
            <summary>
            Gets/sets a value indicating whether [disable session snapshot].
            </summary>
            <value>
            	<c>true</c> if [disable session snapshot]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.SessionSnapshotInterval">
            <summary>
            Gets/sets the interval to taking snapshot for all live sessions.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ConnectionFilter">
            <summary>
            Gets/sets the connection filters used by this server instance.
            </summary>
            <value>
            The connection filter's name list, seperated by comma
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.CommandLoader">
            <summary>
            Gets the command loader, multiple values should be separated by comma.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.KeepAliveTime">
            <summary>
            Gets/sets the start keep alive time, in seconds
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.KeepAliveInterval">
            <summary>
            Gets/sets the keep alive interval, in seconds.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.ListenBacklog">
            <summary>
            Gets the backlog size of socket listening.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.StartupOrder">
            <summary>
            Gets/sets the startup order of the server instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.Listeners">
            <summary>
            Gets and sets the listeners' configuration.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.LogFactory">
            <summary>
            Gets/sets the log factory name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.SendingQueueSize">
            <summary>
            Gets/sets the size of the sending queue.
            </summary>
            <value>
            The size of the sending queue.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.LogBasicSessionActivity">
            <summary>
            Gets a value indicating whether [log basic session activity like connected and disconnected].
            </summary>
            <value>
            	<c>true</c> if [log basic session activity]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.LogAllSocketException">
            <summary>
            Gets/sets a value indicating whether [log all socket exception].
            </summary>
            <value>
            <c>true</c> if [log all socket exception]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.TextEncoding">
            <summary>
            Gets/sets the default text encoding.
            </summary>
            <value>
            The text encoding.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ServerConfig.CommandAssemblies">
            <summary>
            Gets the command assemblies configuration.
            </summary>
            <value>
            The command assemblies.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.HotUpdateAttribute">
            <summary>
            the attribute to mark which property of ServerConfig support hot update
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.TypeProvider">
            <summary>
            Type provider configuration
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.TypeProvider.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.TypeProvider.Type">
            <summary>
            Gets the type.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.TypeProviderCollection">
            <summary>
            Type provider colletion configuration
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.TypeProviderCollection.CreateNewElement">
            <summary>
            When overridden in a derived class, creates a new <see cref="T:System.Configuration.ConfigurationElement"/>.
            </summary>
            <returns>
            A new <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.TypeProviderCollection.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for.</param>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.TypeProviderCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.TypeProviderConfig">
            <summary>
            TypeProviderConfig
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.TypeProviderConfig.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.TypeProviderConfig.Type">
            <summary>
            Gets the type.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Extensions">
            <summary>
            Extensions class for SocketBase project
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Extensions.GetServerByName(SuperSocket.SocketBase.IBootstrap,System.String)">
            <summary>
            Gets the app server instance in the bootstrap by name, ignore case
            </summary>
            <param name="bootstrap">The bootstrap.</param>
            <param name="name">The name of the appserver instance.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:SuperSocket.SocketBase.Extensions.GetStatusInfoMetadata(System.Type)">
            <summary>
            Gets the status info metadata from the server type.
            </summary>
            <param name="serverType">Type of the server.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:SuperSocket.SocketBase.ActiveConnectResult">
            <summary>
            Active connect result model
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ActiveConnectResult.Result">
            <summary>
            Gets or sets a value indicating whether the conecting is sucessfull
            </summary>
            <value>
              <c>true</c> if result; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.ActiveConnectResult.Session">
            <summary>
            Gets or sets the connected session.
            </summary>
            <value>
            The connected session.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.StartResult">
            <summary>
            The bootstrap start result
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.StartResult.None">
            <summary>
            No appserver has been set in the bootstrap, so nothing was started
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.StartResult.Success">
            <summary>
            All appserver instances were started successfully
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.StartResult.PartialSuccess">
            <summary>
            Some appserver instances were started successfully, but some of them failed
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.StartResult.Failed">
            <summary>
            All appserver instances failed to start
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IBootstrap">
            <summary>
            SuperSocket bootstrap
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Initialize">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Initialize(System.Collections.Generic.IDictionary{System.String,System.Net.IPEndPoint})">
            <summary>
            Initializes the bootstrap with a listen endpoint replacement dictionary
            </summary>
            <param name="listenEndPointReplacement">The listen end point replacement.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Initialize(System.Func{SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Config.IServerConfig})">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <param name="serverConfigResolver">The server config resolver.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Initialize(SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Initialize(System.Func{SuperSocket.SocketBase.Config.IServerConfig,SuperSocket.SocketBase.Config.IServerConfig},SuperSocket.SocketBase.Logging.ILogFactory)">
            <summary>
            Initializes the bootstrap with the configuration
            </summary>
            <param name="serverConfigResolver">The server config resolver.</param>
            <param name="logFactory">The log factory.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Start">
            <summary>
            Starts this bootstrap.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IBootstrap.Stop">
            <summary>
            Stops this bootstrap.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IBootstrap.AppServers">
            <summary>
            Gets all the app servers running in this bootstrap
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IBootstrap.Config">
            <summary>
            Gets the config.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IBootstrap.StartupConfigFile">
            <summary>
            Gets the startup config file.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IBootstrap.BaseDirectory">
            <summary>
            Gets the base directory.
            </summary>
            <value>
            The base directory.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.IDynamicBootstrap">
            <summary>
            The bootstrap interface to support add new server instance in runtime
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IDynamicBootstrap.Add(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Adds a new server into the bootstrap.
            </summary>
            <param name="config">The new server's config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IDynamicBootstrap.AddAndStart(SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Adds a new server into the bootstrap and then start it.
            </summary>
            <param name="config">The new server's config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IDynamicBootstrap.Remove(System.String)">
            <summary>
            Removes the server instance which is specified by name.
            </summary>
            <param name="name">The name of the server instance to be removed.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.IsolationMode">
            <summary>
            AppServer instance running isolation mode
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.IsolationMode.None">
            <summary>
            No isolation
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.IsolationMode.AppDomain">
            <summary>
            Isolation by AppDomain
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.IsolationMode.Process">
            <summary>
            Isolation by process
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.ConsoleLog">
            <summary>
            Console Log
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.ILog">
            <summary>
            Log interface
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Debug(System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Debug(System.Object,System.Exception)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.DebugFormat(System.String,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the debug message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.DebugFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.DebugFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Error(System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Error(System.Object,System.Exception)">
            <summary>
            Logs the error message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.ErrorFormat(System.String,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the error message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.ErrorFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.ErrorFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Fatal(System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Fatal(System.Object,System.Exception)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.FatalFormat(System.String,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.FatalFormat(System.String,System.Object[])">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.FatalFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.FatalFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Info(System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Info(System.Object,System.Exception)">
            <summary>
            Logs the info message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.InfoFormat(System.String,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the info message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.InfoFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.InfoFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Warn(System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.Warn(System.Object,System.Exception)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.WarnFormat(System.String,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.WarnFormat(System.String,System.Object[])">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the warning message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.WarnFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILog.WarnFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ILog.IsDebugEnabled">
            <summary>
            Gets a value indicating whether this instance is debug enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is debug enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ILog.IsErrorEnabled">
            <summary>
            Gets a value indicating whether this instance is error enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is error enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ILog.IsFatalEnabled">
            <summary>
            Gets a value indicating whether this instance is fatal enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is fatal enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ILog.IsInfoEnabled">
            <summary>
            Gets a value indicating whether this instance is info enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is info enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ILog.IsWarnEnabled">
            <summary>
            Gets a value indicating whether this instance is warn enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is warn enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Logging.ConsoleLog"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Debug(System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Debug(System.Object,System.Exception)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.DebugFormat(System.String,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the debug message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.DebugFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.DebugFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Error(System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Error(System.Object,System.Exception)">
            <summary>
            Logs the error message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.ErrorFormat(System.String,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the error message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.ErrorFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.ErrorFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Fatal(System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Fatal(System.Object,System.Exception)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.FatalFormat(System.String,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.FatalFormat(System.String,System.Object[])">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.FatalFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.FatalFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Info(System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Info(System.Object,System.Exception)">
            <summary>
            Logs the info message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.InfoFormat(System.String,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the info message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.InfoFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.InfoFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Warn(System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.Warn(System.Object,System.Exception)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.WarnFormat(System.String,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.WarnFormat(System.String,System.Object[])">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the warning message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.WarnFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLog.WarnFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ConsoleLog.IsDebugEnabled">
            <summary>
            Gets a value indicating whether this instance is debug enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is debug enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ConsoleLog.IsErrorEnabled">
            <summary>
            Gets a value indicating whether this instance is error enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is error enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ConsoleLog.IsFatalEnabled">
            <summary>
            Gets a value indicating whether this instance is fatal enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is fatal enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ConsoleLog.IsInfoEnabled">
            <summary>
            Gets a value indicating whether this instance is info enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is info enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.ConsoleLog.IsWarnEnabled">
            <summary>
            Gets a value indicating whether this instance is warn enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is warn enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.ConsoleLogFactory">
            <summary>
            Console log factory
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.ILogFactory">
            <summary>
            LogFactory Interface
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ILogFactory.GetLog(System.String)">
            <summary>
            Gets the log by name.
            </summary>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.ConsoleLogFactory.GetLog(System.String)">
            <summary>
            Gets the log by name.
            </summary>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.Log4NetLog">
            <summary>
            Log4NetLog
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.#ctor(log4net.ILog)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Logging.Log4NetLog"/> class.
            </summary>
            <param name="log">The log.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Debug(System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Debug(System.Object,System.Exception)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.DebugFormat(System.String,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the debug message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.DebugFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.DebugFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the debug message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Error(System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Error(System.Object,System.Exception)">
            <summary>
            Logs the error message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.ErrorFormat(System.String,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the error message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.ErrorFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.ErrorFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Fatal(System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Fatal(System.Object,System.Exception)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.FatalFormat(System.String,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.FatalFormat(System.String,System.Object[])">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.FatalFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.FatalFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the fatal error message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Info(System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Info(System.Object,System.Exception)">
            <summary>
            Logs the info message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.InfoFormat(System.String,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the info message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.InfoFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.InfoFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the info message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Warn(System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.Warn(System.Object,System.Exception)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.WarnFormat(System.String,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.WarnFormat(System.String,System.Object[])">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs the warning message.
            </summary>
            <param name="provider">The provider.</param>
            <param name="format">The format.</param>
            <param name="args">The args.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.WarnFormat(System.String,System.Object,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLog.WarnFormat(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Logs the warning message.
            </summary>
            <param name="format">The format.</param>
            <param name="arg0">The arg0.</param>
            <param name="arg1">The arg1.</param>
            <param name="arg2">The arg2.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.Log4NetLog.IsDebugEnabled">
            <summary>
            Gets a value indicating whether this instance is debug enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is debug enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.Log4NetLog.IsErrorEnabled">
            <summary>
            Gets a value indicating whether this instance is error enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is error enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.Log4NetLog.IsFatalEnabled">
            <summary>
            Gets a value indicating whether this instance is fatal enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is fatal enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.Log4NetLog.IsInfoEnabled">
            <summary>
            Gets a value indicating whether this instance is info enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is info enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.Log4NetLog.IsWarnEnabled">
            <summary>
            Gets a value indicating whether this instance is warn enabled.
            </summary>
            <value>
            	<c>true</c> if this instance is warn enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.Log4NetLogFactory">
            <summary>
            Log4NetLogFactory
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Logging.LogFactoryBase">
            <summary>
            LogFactory Base class
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.LogFactoryBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Logging.LogFactoryBase"/> class.
            </summary>
            <param name="configFile">The config file.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.LogFactoryBase.GetLog(System.String)">
            <summary>
            Gets the log by name.
            </summary>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.LogFactoryBase.ConfigFile">
            <summary>
            Gets the config file file path.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Logging.LogFactoryBase.IsSharedConfig">
            <summary>
            Gets a value indicating whether the server instance is running in isolation mode and the multiple server instances share the same logging configuration.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLogFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Logging.Log4NetLogFactory"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLogFactory.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Logging.Log4NetLogFactory"/> class.
            </summary>
            <param name="log4netConfig">The log4net config.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Logging.Log4NetLogFactory.GetLog(System.String)">
            <summary>
            Gets the log by name.
            </summary>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Metadata.AppServerMetadataTypeAttribute">
            <summary>
            StatusInfoMetadata type attribute
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Metadata.AppServerMetadataTypeAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Metadata.AppServerMetadataTypeAttribute"/> class.
            </summary>
            <param name="metadataType">Type of the metadata.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.AppServerMetadataTypeAttribute.MetadataType">
            <summary>
            Gets the type of the metadata.
            </summary>
            <value>
            The type of the metadata.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Metadata.CommandFilterAttribute">
            <summary>
            Command filter attribute
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Metadata.CommandFilterAttribute.OnCommandExecuting(SuperSocket.SocketBase.CommandExecutingContext)">
            <summary>
            Called when [command executing].
            </summary>
            <param name="commandContext">The command context.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Metadata.CommandFilterAttribute.OnCommandExecuted(SuperSocket.SocketBase.CommandExecutingContext)">
            <summary>
            Called when [command executed].
            </summary>
            <param name="commandContext">The command context.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.CommandFilterAttribute.Order">
            <summary>
            Gets or sets the execution order.
            </summary>
            <value>
            The order.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Metadata.DefaultAppServerMetadata">
            <summary>
            AppServer's default metadata type
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Metadata.StatusInfoAttribute">
            <summary>
            StatusInfo Metadata
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Metadata.StatusInfoAttribute"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Metadata.StatusInfoAttribute"/> class.
            </summary>
            <param name="key">The key.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.Key">
            <summary>
            Gets or sets the key.
            </summary>
            <value>
            The key.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.ShortName">
            <summary>
            Gets or sets the short name.
            </summary>
            <value>
            The short name.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.Format">
            <summary>
            Gets or sets the format.
            </summary>
            <value>
            The format.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.Order">
            <summary>
            Gets or sets the order.
            </summary>
            <value>
            The order.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.OutputInPerfLog">
            <summary>
            Gets or sets a value indicating whether [output in perf log].
            </summary>
            <value>
              <c>true</c> if [output in perf log]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Metadata.StatusInfoAttribute.DataType">
            <summary>
            Gets or sets the type of the data.
            </summary>
            <value>
            The type of the data.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Metadata.StatusInfoKeys">
            <summary>
            Server StatusInfo Metadata
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.CpuUsage">
            <summary>
            The cpu usage
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.MemoryUsage">
            <summary>
            The memory usage
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.TotalThreadCount">
            <summary>
            The total thread count
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.AvailableWorkingThreads">
            <summary>
            The available working threads count
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.AvailableCompletionPortThreads">
            <summary>
            The available completion port threads count
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.MaxWorkingThreads">
            <summary>
            The max working threads count
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.MaxCompletionPortThreads">
            <summary>
            The max completion port threads count
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.StartedTime">
            <summary>
            The started time.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.IsRunning">
            <summary>
            	<c>true</c> if this instance is running; otherwise, <c>false</c>.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.TotalConnections">
            <summary>
            The total count of the connections.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.MaxConnectionNumber">
            <summary>
            The max connection number.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.TotalHandledRequests">
            <summary>
            The total handled requests count.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.RequestHandlingSpeed">
            <summary>
            Gets or sets the request handling speed, per second.
            </summary>
            <value>
            The request handling speed.
            </value>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.Listeners">
            <summary>
            Gets or sets the listeners.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.AvialableSendingQueueItems">
            <summary>
            The avialable sending queue items.
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Metadata.StatusInfoKeys.TotalSendingQueueItems">
            <summary>
            The total sending queue items.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.NodeStatus">
            <summary>
            The status of one SuperSocket node (one installation or deployment)
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.NodeStatus.Save(System.String)">
            <summary>
            Saves the specified file path.
            </summary>
            <param name="filePath">The file path.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.NodeStatus.LoadFrom(System.String)">
            <summary>
            Loads a NodeStatus instance from a file.
            </summary>
            <param name="filePath">The file path.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.NodeStatus.BootstrapStatus">
            <summary>
            Gets or sets the bootstrap status.
            </summary>
            <value>
            The bootstrap status.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.NodeStatus.InstancesStatus">
            <summary>
            Gets or sets the status of all server instances running in this node.
            </summary>
            <value>
            The instances status.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory">
            <summary>
            CommandLine RequestFilter Factory
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory">
            <summary>
            Terminator ReceiveFilter Factory
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IReceiveFilterFactory`1">
            <summary>
            Receive filter factory interface
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IReceiveFilterFactory">
            <summary>
            Receive filter factory interface
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.IReceiveFilterFactory`1.CreateFilter(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.IAppSession,System.Net.IPEndPoint)">
            <summary>
            Creates the Receive filter.
            </summary>
            <param name="appServer">The app server.</param>
            <param name="appSession">The app session.</param>
            <param name="remoteEndPoint">The remote end point.</param>
            <returns>
            the new created request filer assosiated with this socketSession
            </returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory.#ctor(System.String,System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
            <param name="encoding">The encoding.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory.#ctor(System.String,System.Text.Encoding,SuperSocket.SocketBase.Protocol.IRequestInfoParser{SuperSocket.SocketBase.Protocol.StringRequestInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
            <param name="encoding">The encoding.</param>
            <param name="requestInfoParser">The line parser.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilterFactory.CreateFilter(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.IAppSession,System.Net.IPEndPoint)">
            <summary>
            Creates the Receive filter.
            </summary>
            <param name="appServer">The app server.</param>
            <param name="appSession">The app session.</param>
            <param name="remoteEndPoint">The remote end point.</param>
            <returns>
            the new created request filer assosiated with this socketSession
            </returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory.#ctor(System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory"/> class.
            </summary>
            <param name="encoding">The encoding.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory.#ctor(System.Text.Encoding,SuperSocket.SocketBase.Protocol.IRequestInfoParser{SuperSocket.SocketBase.Protocol.StringRequestInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.CommandLineReceiveFilterFactory"/> class.
            </summary>
            <param name="encoding">The encoding.</param>
            <param name="requestInfoParser">The request info parser.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.DefaultReceiveFilterFactory`2">
            <summary>
            DefaultreceiveFilterFactory
            </summary>
            <typeparam name="TReceiveFilter">The type of the Receive filter.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.DefaultReceiveFilterFactory`2.CreateFilter(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.IAppSession,System.Net.IPEndPoint)">
            <summary>
            Creates the Receive filter.
            </summary>
            <param name="appServer">The app server.</param>
            <param name="appSession">The app session.</param>
            <param name="remoteEndPoint">The remote end point.</param>
            <returns>
            the new created request filer assosiated with this socketSession
            </returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.FilterState">
            <summary>
            Filter state enum
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Protocol.FilterState.Normal">
            <summary>
            Normal state
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Protocol.FilterState.Error">
            <summary>
            Error state
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IOffsetAdapter">
            <summary>
            The interface for a Receive filter to adapt receiving buffer offset
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IOffsetAdapter.OffsetDelta">
            <summary>
            Gets the offset delta.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IReceiveFilter`1">
            <summary>
            Receive filter interface
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.IReceiveFilter`1.Filter(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Int32@)">
            <summary>
            Filters received data of the specific session into request info.
            </summary>
            <param name="readBuffer">The read buffer.</param>
            <param name="offset">The offset of the current received data in this read buffer.</param>
            <param name="length">The length of the current received data.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
            <param name="rest">The rest, the length of the data which hasn't been parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.IReceiveFilter`1.Reset">
            <summary>
            Resets this instance to initial state.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IReceiveFilter`1.LeftBufferSize">
            <summary>
            Gets the size of the rest buffer.
            </summary>
            <value>
            The size of the rest buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IReceiveFilter`1.NextReceiveFilter">
            <summary>
            Gets the next Receive filter.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IReceiveFilter`1.State">
            <summary>
            Gets the filter state.
            </summary>
            <value>
            The filter state.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IReceiveFilterInitializer">
            <summary>
            Provide the initializing interface for ReceiveFilter
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.IReceiveFilterInitializer.Initialize(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.IAppSession)">
            <summary>
            Initializes the ReceiveFilter with the specified appServer and appSession
            </summary>
            <param name="appServer">The app server.</param>
            <param name="session">The session.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1">
            <summary>
            Receive filter base class
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.#ctor(SuperSocket.SocketBase.Protocol.ReceiveFilterBase{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1"/> class.
            </summary>
            <param name="previousRequestFilter">The previous Receive filter.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.Initialize(SuperSocket.SocketBase.Protocol.ReceiveFilterBase{`0})">
            <summary>
            Initializes the specified previous Receive filter.
            </summary>
            <param name="previousRequestFilter">The previous Receive filter.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.Filter(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Int32@)">
            <summary>
            Filters received data of the specific session into request info.
            </summary>
            <param name="readBuffer">The read buffer.</param>
            <param name="offset">The offset of the current received data in this read buffer.</param>
            <param name="length">The length of the current received data.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
            <param name="rest">The rest, the length of the data which hasn't been parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.AddArraySegment(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Adds the array segment.
            </summary>
            <param name="buffer">The buffer.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.ClearBufferSegments">
            <summary>
            Clears the buffer segments.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.Reset">
            <summary>
            Resets this instance to initial state.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.BufferSegments">
            <summary>
            Gets the buffer segments which can help you parse your request info conviniently.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.LeftBufferSize">
            <summary>
            Gets the size of the rest buffer.
            </summary>
            <value>
            The size of the rest buffer.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.NextReceiveFilter">
            <summary>
            Gets or sets the next Receive filter.
            </summary>
            <value>
            The next Receive filter.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.ReceiveFilterBase`1.State">
            <summary>
            Gets the filter state.
            </summary>
            <value>
            The state.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1">
            <summary>
            Terminator Receive filter
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="F:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1.NullRequestInfo">
            <summary>
            Null RequestInfo
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1.Filter(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Int32@)">
            <summary>
            Filters received data of the specific session into request info.
            </summary>
            <param name="readBuffer">The read buffer.</param>
            <param name="offset">The offset of the current received data in this read buffer.</param>
            <param name="length">The length of the current received data.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
            <param name="rest">The rest, the length of the data which hasn't been parsed.</param>
            <returns>return the parsed TRequestInfo</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1.Reset">
            <summary>
            Resets this instance.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1.ProcessMatchedRequest(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Resolves the specified data to TRequestInfo.
            </summary>
            <param name="data">The data.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter`1.Session">
            <summary>
            Gets the session assosiated with the Receive filter.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter">
            <summary>
            TerminatorRequestFilter
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter.#ctor(System.Byte[],System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
            <param name="encoding">The encoding.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter.#ctor(System.Byte[],System.Text.Encoding,SuperSocket.SocketBase.Protocol.IRequestInfoParser{SuperSocket.SocketBase.Protocol.StringRequestInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter"/> class.
            </summary>
            <param name="terminator">The terminator.</param>
            <param name="encoding">The encoding.</param>
            <param name="requestParser">The request parser.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.TerminatorReceiveFilter.ProcessMatchedRequest(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Resolves the specified data to StringRequestInfo.
            </summary>
            <param name="data">The data.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Provider.ExportFactory">
            <summary>
            Export Factory
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ExportFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ExportFactory"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ExportFactory.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ExportFactory"/> class.
            </summary>
            <param name="instance">The instance.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ExportFactory.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ExportFactory"/> class.
            </summary>
            <param name="typeName">Name of the type.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ExportFactory.EnsureInstance">
            <summary>
            Ensures the instance's existance.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ExportFactory.CreateExport``1">
            <summary>
            Creates the export type instance.
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ExportFactory.CreateExport``1(System.Func{System.Type,System.Object})">
            <summary>
            Creates the export type instance from the instance creator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="creator">The creator.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ExportFactory.TypeName">
            <summary>
            Gets or sets the type.
            </summary>
            <value>
            The type.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Provider.ProviderFactoryInfo">
            <summary>
            Provider factory infomation
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ProviderFactoryInfo"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.#ctor(SuperSocket.SocketBase.Provider.ProviderKey,System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ProviderFactoryInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="name">The name.</param>
            <param name="instance">The instance.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.#ctor(SuperSocket.SocketBase.Provider.ProviderKey,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ProviderFactoryInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="name">The name.</param>
            <param name="typeName">Name of the type.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.#ctor(SuperSocket.SocketBase.Provider.ProviderKey,System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Provider.ProviderFactoryInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="name">The name.</param>
            <param name="type">The type.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.Key">
            <summary>
            Gets the key.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderFactoryInfo.ExportFactory">
            <summary>
            Gets or sets the export factory.
            </summary>
            <value>
            The export factory.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Provider.ProviderKey">
            <summary>
            ProviderKey
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.Type">
            <summary>
            Gets or sets the type.
            </summary>
            <value>
            The type.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.ServerType">
            <summary>
            Gets the service.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.SocketServerFactory">
            <summary>
            Gets the socket server factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.ConnectionFilter">
            <summary>
            Gets the connection filter.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.LogFactory">
            <summary>
            Gets the log factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.ReceiveFilterFactory">
            <summary>
            Gets the Receive filter factory.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Provider.ProviderKey.CommandLoader">
            <summary>
            Gets the command loader.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.RequestHandler`2">
            <summary>
            Request handler
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.IListenerConfig">
            <summary>
            The listener configuration interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IListenerConfig.Ip">
            <summary>
            Gets the ip of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IListenerConfig.Port">
            <summary>
            Gets the port of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IListenerConfig.Backlog">
            <summary>
            Gets the backlog.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.IListenerConfig.Security">
            <summary>
            Gets the security option, None/Default/Tls/Ssl/...
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.ListenerConfig">
            <summary>
            Listener configuration model
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Config.ListenerConfig.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Config.ListenerConfig"/> class.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ListenerConfig.Ip">
            <summary>
            Gets the ip of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ListenerConfig.Port">
            <summary>
            Gets the port of listener
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ListenerConfig.Backlog">
            <summary>
            Gets the backlog.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ListenerConfig.Security">
            <summary>
            Gets/sets the security option, None/Default/Tls/Ssl/...
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.ListenerInfo">
            <summary>
            Listener inforamtion
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ListenerInfo.EndPoint">
            <summary>
            Gets or sets the listen endpoint.
            </summary>
            <value>
            The end point.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.ListenerInfo.BackLog">
            <summary>
            Gets or sets the listen backlog.
            </summary>
            <value>
            The back log.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.ListenerInfo.Security">
            <summary>
            Gets or sets the security protocol.
            </summary>
            <value>
            The security.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.BinaryRequestInfo">
            <summary>
            Binary type request information
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.RequestInfo`1">
            <summary>
            RequestInfo basic class
            </summary>
            <typeparam name="TRequestBody">The type of the request body.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IRequestInfo`1">
            <summary>
            Request information interface
            </summary>
            <typeparam name="TRequestBody">The type of the request body.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IRequestInfo">
            <summary>
            Request information interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IRequestInfo.Key">
            <summary>
            Gets the key of this request.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IRequestInfo`1.Body">
            <summary>
            Gets the body of this request.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.RequestInfo`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.RequestInfo`1"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.RequestInfo`1.#ctor(System.String,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.RequestInfo`1"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="body">The body.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.RequestInfo`1.Initialize(System.String,`0)">
            <summary>
            Initializes the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="body">The body.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.RequestInfo`1.Key">
            <summary>
            Gets the key of this request.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.RequestInfo`1.Body">
            <summary>
            Gets the body.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.BinaryRequestInfo.#ctor(System.String,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.BinaryRequestInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="body">The body.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.CommandBase`2">
            <summary>
            Command base class
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.ICommand`2">
            <summary>
            Command basic interface
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.ICommand">
            <summary>
            Command basic interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Command.ICommand.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ICommand`2.ExecuteCommand(`0,`1)">
            <summary>
            Executes the command.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandBase`2.ExecuteCommand(`0,`1)">
            <summary>
            Executes the command.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.CommandBase`2.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Command.CommandBase`2.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.CommandUpdateAction">
            <summary>
            Command update action enum
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Command.CommandUpdateAction.Add">
            <summary>
            Add command
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Command.CommandUpdateAction.Remove">
            <summary>
            Remove command
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.Command.CommandUpdateAction.Update">
            <summary>
            Update command
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.CommandUpdateInfo`1">
            <summary>
            Command update information
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:SuperSocket.SocketBase.Command.CommandUpdateInfo`1.UpdateAction">
            <summary>
            Gets or sets the update action.
            </summary>
            <value>
            The update action.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Command.CommandUpdateInfo`1.Command">
            <summary>
            Gets or sets the target command.
            </summary>
            <value>
            The command.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.MockupCommand`2">
            <summary>
            Mockup command
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.MockupCommand`2.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Command.MockupCommand`2"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.MockupCommand`2.ExecuteCommand(`0,`1)">
            <summary>
            Executes the command.
            </summary>
            <param name="session">The session.</param>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Command.MockupCommand`2.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IRequestInfo`2">
            <summary>
            Request information interface
            </summary>
            <typeparam name="TRequestHeader">The type of the request header.</typeparam>
            <typeparam name="TRequestBody">The type of the request body.</typeparam>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.IRequestInfo`2.Header">
            <summary>
            Gets the header of the request.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.RequestInfo`2">
            <summary>
            RequestInfo with header
            </summary>
            <typeparam name="TRequestHeader">The type of the request header.</typeparam>
            <typeparam name="TRequestBody">The type of the request body.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.RequestInfo`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.RequestInfo`2"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.RequestInfo`2.#ctor(System.String,`0,`1)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.RequestInfo`2"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="header">The header.</param>
            <param name="body">The body.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.RequestInfo`2.Initialize(System.String,`0,`1)">
            <summary>
            Initializes the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="header">The header.</param>
            <param name="body">The body.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.RequestInfo`2.Header">
            <summary>
            Gets the header.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.StringRequestInfo">
            <summary>
            String type request information
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.StringRequestInfo.#ctor(System.String,System.String,System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.StringRequestInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="body">The body.</param>
            <param name="parameters">The parameters.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.StringRequestInfo.GetFirstParam">
            <summary>
            Gets the first param.
            </summary>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.StringRequestInfo.Parameters">
            <summary>
            Gets the parameters.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.StringRequestInfo.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:System.String"/> at the specified index.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.ReflectCommandLoader`1">
            <summary>
            A command loader which loads commands from assembly by reflection
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ReflectCommandLoader`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Command.ReflectCommandLoader`1"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ReflectCommandLoader`1.Initialize(SuperSocket.SocketBase.Config.IRootConfig,SuperSocket.SocketBase.IAppServer)">
            <summary>
            Initializes the command loader by the root config and the server instance.
            </summary>
            <param name="rootConfig">The root config.</param>
            <param name="appServer">The app server.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.Command.ReflectCommandLoader`1.TryLoadCommands(System.Collections.Generic.IEnumerable{`0}@)">
            <summary>
            Tries to load commands.
            </summary>
            <param name="commands">The commands.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.UdpRequestInfo">
            <summary>
            UdpRequestInfo, it is designed for passing in business session ID to udp request info
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.UdpRequestInfo.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.UdpRequestInfo"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="sessionID">The session ID.</param>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.UdpRequestInfo.Key">
            <summary>
            Gets the key of this request.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Protocol.UdpRequestInfo.SessionID">
            <summary>
            Gets the session ID.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.CertificateConfig">
            <summary>
            Certificate config model class
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Config.ICertificateConfig">
            <summary>
            Certificate configuration interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.FilePath">
            <summary>
            Gets the file path.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.Password">
            <summary>
            Gets the password.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.StoreName">
            <summary>
            Gets the the store where certificate locates.
            </summary>
            <value>
            The name of the store.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.Thumbprint">
            <summary>
            Gets the thumbprint.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.StoreLocation">
            <summary>
            Gets the store location of the certificate.
            </summary>
            <value>
            The store location.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.ClientCertificateRequired">
            <summary>
            Gets a value indicating whether [client certificate required].
            </summary>
            <value>
            <c>true</c> if [client certificate required]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.ICertificateConfig.KeyStorageFlags">
            <summary>
            Gets a value that will be used to instantiate the X509Certificate2 object in the CertificateManager
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.FilePath">
            <summary>
            Gets/sets the file path.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.Password">
            <summary>
            Gets/sets the password.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.StoreName">
            <summary>
            Gets/sets the the store where certificate locates.
            </summary>
            <value>
            The name of the store.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.StoreLocation">
            <summary>
            Gets/sets the store location of the certificate.
            </summary>
            <value>
            The store location.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.Thumbprint">
            <summary>
            Gets/sets the thumbprint.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.ClientCertificateRequired">
            <summary>
            Gets/sets a value indicating whether [client certificate required].
            </summary>
            <value>
            <c>true</c> if [client certificate required]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.Config.CertificateConfig.KeyStorageFlags">
            <summary>
            Gets/sets a value that will be used to instantiate the X509Certificate2 object in the CertificateManager
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IAppSession">
            <summary>
            The basic interface for appSession
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.ISessionBase">
            <summary>
            The basic session interface
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISessionBase.SessionID">
            <summary>
            Gets the session ID.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISessionBase.RemoteEndPoint">
            <summary>
            Gets the remote endpoint.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppSession.Close">
            <summary>
            Closes this session.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppSession.Close(SuperSocket.SocketBase.CloseReason)">
            <summary>
            Closes the session by the specified reason.
            </summary>
            <param name="reason">The close reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppSession.ProcessRequest(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Processes the request.
            </summary>
            <param name="readBuffer">The read buffer.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
            <returns>return offset delta of next receiving buffer</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppSession.StartSession">
            <summary>
            Starts the session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.AppServer">
            <summary>
            Gets the app server.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.SocketSession">
            <summary>
            Gets the socket session of the AppSession.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.Items">
            <summary>
            Gets the items.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.Config">
            <summary>
            Gets the config of the server.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.LocalEndPoint">
            <summary>
            Gets the local listening endpoint.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.LastActiveTime">
            <summary>
            Gets or sets the last active time of the session.
            </summary>
            <value>
            The last active time.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.StartTime">
            <summary>
            Gets the start time of the session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.Connected">
            <summary>
            Gets a value indicating whether this <see cref="T:SuperSocket.SocketBase.IAppSession"/> is connected.
            </summary>
            <value>
              <c>true</c> if connected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.Charset">
            <summary>
            Gets or sets the charset which is used for transfering text message.
            </summary>
            <value>The charset.</value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.PrevCommand">
            <summary>
            Gets or sets the previous command.
            </summary>
            <value>
            The prev command.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.CurrentCommand">
            <summary>
            Gets or sets the current executing command.
            </summary>
            <value>
            The current command.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.IAppSession.Logger">
            <summary>
            Gets the logger assosiated with this session.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.IAppSession`2">
            <summary>
            The interface for appSession
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.IAppSession`2.Initialize(SuperSocket.SocketBase.IAppServer{`0,`1},SuperSocket.SocketBase.ISocketSession)">
            <summary>
            Initializes the specified session.
            </summary>
            <param name="server">The server.</param>
            <param name="socketSession">The socket session.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.IConnectionFilter">
            <summary>
            The basic interface of connection filter
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.IConnectionFilter.Initialize(System.String,SuperSocket.SocketBase.IAppServer)">
            <summary>
            Initializes the connection filter
            </summary>
            <param name="name">The name.</param>
            <param name="appServer">The app server.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.IConnectionFilter.AllowConnect(System.Net.IPEndPoint)">
            <summary>
            Whether allows the connect according the remote endpoint
            </summary>
            <param name="remoteAddress">The remote address.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.IConnectionFilter.Name">
            <summary>
            Gets the name of the filter.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.ISocketServer">
            <summary>
            It is the basic interface of SocketServer,
            SocketServer is the abstract server who really listen the comming sockets directly.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketServer.Start">
            <summary>
            Starts this instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketServer.ResetSessionSecurity(SuperSocket.SocketBase.IAppSession,System.Security.Authentication.SslProtocols)">
            <summary>
            Resets the session's security protocol.
            </summary>
            <param name="session">The session.</param>
            <param name="security">The security protocol.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketServer.Stop">
            <summary>
            Stops this instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketServer.IsRunning">
            <summary>
            Gets a value indicating whether this instance is running.
            </summary>
            <value>
            	<c>true</c> if this instance is running; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketServer.SendingQueuePool">
            <summary>
            Gets the information of the sending queue pool.
            </summary>
            <value>
            The sending queue pool.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.ISocketServerFactory">
            <summary>
            The interface for socket server factory
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketServerFactory.CreateSocketServer``1(SuperSocket.SocketBase.IAppServer,SuperSocket.SocketBase.ListenerInfo[],SuperSocket.SocketBase.Config.IServerConfig)">
            <summary>
            Creates the socket server instance.
            </summary>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
            <param name="appServer">The app server.</param>
            <param name="listeners">The listeners.</param>
            <param name="config">The config.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.CloseReason">
            <summary>
            CloseReason enum
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.Unknown">
            <summary>
            The socket is closed for unknown reason
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.ServerShutdown">
            <summary>
            Close for server shutdown
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.ClientClosing">
            <summary>
            The client close the socket
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.ServerClosing">
            <summary>
            The server side close the socket
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.ApplicationError">
            <summary>
            Application error
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.SocketError">
            <summary>
            The socket is closed for a socket error
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.TimeOut">
            <summary>
            The socket is closed by server for timeout
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.ProtocolError">
            <summary>
            Protocol error 
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.CloseReason.InternalError">
            <summary>
            SuperSocket internal error
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.ISocketSession">
            <summary>
            The interface for socket session
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketSession.Initialize(SuperSocket.SocketBase.IAppSession)">
            <summary>
            Initializes the specified app session.
            </summary>
            <param name="appSession">The app session.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketSession.Start">
            <summary>
            Starts this instance.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketSession.Close(SuperSocket.SocketBase.CloseReason)">
            <summary>
            Closes the socket session for the specified reason.
            </summary>
            <param name="reason">The reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketSession.TrySend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Tries to send array segment.
            </summary>
            <param name="segments">The segments.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketSession.TrySend(System.ArraySegment{System.Byte})">
            <summary>
            Tries to send array segment.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.ISocketSession.ApplySecureProtocol">
            <summary>
            Applies the secure protocol.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketSession.Client">
            <summary>
            Gets the client socket.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketSession.LocalEndPoint">
            <summary>
            Gets the local listening endpoint.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketSession.SecureProtocol">
            <summary>
            Gets or sets the secure protocol.
            </summary>
            <value>
            The secure protocol.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketSession.Closed">
            <summary>
            Occurs when [closed].
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketSession.AppSession">
            <summary>
            Gets the app session assosiated with this socket session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.ISocketSession.OrigReceiveOffset">
            <summary>
            Gets the original receive buffer offset.
            </summary>
            <value>
            The original receive buffer offset.
            </value>
        </member>
        <member name="T:SuperSocket.SocketBase.LoggerExtension">
            <summary>
            Logger extension class
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.LoggerExtension.Error(SuperSocket.SocketBase.Logging.ILog,SuperSocket.SocketBase.ISessionBase,System.String,System.Exception)">
            <summary>
            Logs the error
            </summary>
            <param name="logger">The logger.</param>
            <param name="session">The session.</param>
            <param name="title">The title.</param>
            <param name="e">The e.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.LoggerExtension.Error(SuperSocket.SocketBase.Logging.ILog,SuperSocket.SocketBase.ISessionBase,System.String)">
            <summary>
            Logs the error
            </summary>
            <param name="logger">The logger.</param>
            <param name="session">The session.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.LoggerExtension.Info(SuperSocket.SocketBase.Logging.ILog,SuperSocket.SocketBase.ISessionBase,System.String)">
            <summary>
            Logs the information
            </summary>
            <param name="logger">The logger.</param>
            <param name="session">The session.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.LoggerExtension.Debug(SuperSocket.SocketBase.Logging.ILog,SuperSocket.SocketBase.ISessionBase,System.String)">
            <summary>
            Logs the debug message
            </summary>
            <param name="logger">The logger.</param>
            <param name="session">The session.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.LoggerExtension.LogPerf(SuperSocket.SocketBase.IAppServer,System.String)">
            <summary>
            Logs the performance message
            </summary>
            <param name="appServer">The app server.</param>
            <param name="message">The message.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser">
            <summary>
            Basic request info parser, which parse request info by separating
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Protocol.IRequestInfoParser`1">
            <summary>
            The interface for request info parser 
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.IRequestInfoParser`1.ParseRequestInfo(System.String)">
            <summary>
            Parses the request info from the source string.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="F:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser.DefaultInstance">
            <summary>
            The default singlegton instance
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser"/> class.
            </summary>
            <param name="spliter">The spliter between command name and command parameters.</param>
            <param name="parameterSpliter">The parameter spliter.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.Protocol.BasicRequestInfoParser.ParseRequestInfo(System.String)">
            <summary>
            Parses the request info.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.SocketBase.ServerState">
            <summary>
            Server's state enum class
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.ServerState.NotInitialized">
            <summary>
            Not initialized
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.ServerState.Initializing">
            <summary>
            In initializing
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.ServerState.NotStarted">
            <summary>
            Has been initialized, but not started
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.ServerState.Starting">
            <summary>
            In starting
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.ServerState.Running">
            <summary>
            In running
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.ServerState.Stopping">
            <summary>
            In stopping
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.SessionHandler`1">
            <summary>
            Used for session level event handler
            </summary>
            <typeparam name="TAppSession">the type of the target session</typeparam>
            <param name="session">the target session</param>
        </member>
        <member name="T:SuperSocket.SocketBase.SessionHandler`2">
            <summary>
            Used for session level event handler
            </summary>
            <typeparam name="TAppSession">the type of the target session</typeparam>
            <typeparam name="TParam">the target session</typeparam>
            <param name="session">the target session</param>
            <param name="value">the event parameter</param>
        </member>
        <member name="T:SuperSocket.SocketBase.SocketMode">
            <summary>
            Socket server running mode
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.SocketMode.Tcp">
            <summary>
            Tcp mode
            </summary>
        </member>
        <member name="F:SuperSocket.SocketBase.SocketMode.Udp">
            <summary>
            Udp mode
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.AppServer">
            <summary>
            AppServer class
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.AppServer`1">
            <summary>
            AppServer class
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.AppServer`2">
            <summary>
            AppServer basic class
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServer`2"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.#ctor(SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{`1})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServer`2"/> class.
            </summary>
            <param name="protocol">The protocol.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.Start">
            <summary>
            Starts this AppServer instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.RegisterSession(System.String,`0)">
            <summary>
            Registers the session into the session container.
            </summary>
            <param name="sessionID">The session ID.</param>
            <param name="appSession">The app session.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.GetAppSessionByID(System.String)">
            <summary>
            Gets the app session by ID.
            </summary>
            <param name="sessionID">The session ID.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.GetSessionByID(System.String)">
            <summary>
            Gets the app session by ID.
            </summary>
            <param name="sessionID">The session ID.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.OnSessionClosed(`0,SuperSocket.SocketBase.CloseReason)">
            <summary>
            Called when [socket session closed].
            </summary>
            <param name="session">The session.</param>
            <param name="reason">The reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.ClearIdleSession(System.Object)">
            <summary>
            Clears the idle session.
            </summary>
            <param name="state">The state.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.GetSessions(System.Func{`0,System.Boolean})">
            <summary>
            Gets the matched sessions from sessions snapshot.
            </summary>
            <param name="critera">The prediction critera.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.GetAllSessions">
            <summary>
            Gets all sessions in sessions snapshot.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`2.Stop">
            <summary>
            Stops this instance.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppServer`2.SessionCount">
            <summary>
            Gets the total session count.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServer`1"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer`1.#ctor(SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{SuperSocket.SocketBase.Protocol.StringRequestInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServer`1"/> class.
            </summary>
            <param name="receiveFilterFactory">The Receive filter factory.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServer"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppServer.#ctor(SuperSocket.SocketBase.Protocol.IReceiveFilterFactory{SuperSocket.SocketBase.Protocol.StringRequestInfo})">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppServer"/> class.
            </summary>
            <param name="receiveFilterFactory">The Receive filter factory.</param>
        </member>
        <member name="T:SuperSocket.SocketBase.AppSession`2">
            <summary>
            AppSession base class
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
            <typeparam name="TRequestInfo">The type of the request info.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppSession`2"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Initialize(SuperSocket.SocketBase.IAppServer{`0,`1},SuperSocket.SocketBase.ISocketSession)">
            <summary>
            Initializes the specified app session by AppServer and SocketSession.
            </summary>
            <param name="appServer">The app server.</param>
            <param name="socketSession">The socket session.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.SuperSocket#SocketBase#IAppSession#StartSession">
            <summary>
            Starts the session.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.OnInit">
            <summary>
            Called when [init].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.OnSessionStarted">
            <summary>
            Called when [session started].
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.OnSessionClosed(SuperSocket.SocketBase.CloseReason)">
            <summary>
            Called when [session closed].
            </summary>
            <param name="reason">The reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.HandleException(System.Exception)">
            <summary>
            Handles the exceptional error, it only handles application error.
            </summary>
            <param name="e">The exception.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.HandleUnknownRequest(`1)">
            <summary>
            Handles the unknown request.
            </summary>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Close(SuperSocket.SocketBase.CloseReason)">
            <summary>
            Closes the session by the specified reason.
            </summary>
            <param name="reason">The close reason.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Close">
            <summary>
            Closes this session.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.TrySend(System.String)">
            <summary>
            Try to send the message to client.
            </summary>
            <param name="message">The message which will be sent.</param>
            <returns>Indicate whether the message was pushed into the sending queue</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Send(System.String)">
            <summary>
            Sends the message to client.
            </summary>
            <param name="message">The message which will be sent.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.TrySend(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Try to send the data to client.
            </summary>
            <param name="data">The data which will be sent.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns>Indicate whether the message was pushed into the sending queue</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Send(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Sends the data to client.
            </summary>
            <param name="data">The data which will be sent.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.TrySend(System.ArraySegment{System.Byte})">
            <summary>
            Try to send the data segment to client.
            </summary>
            <param name="segment">The segment which will be sent.</param>
            <returns>Indicate whether the message was pushed into the sending queue</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Send(System.ArraySegment{System.Byte})">
            <summary>
            Sends the data segment to client.
            </summary>
            <param name="segment">The segment which will be sent.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.TrySend(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Try to send the data segments to client.
            </summary>
            <param name="segments">The segments.</param>
            <returns>Indicate whether the message was pushed into the sending queue; if it returns false, the sending queue may be full or the socket is not connected</returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Send(System.Collections.Generic.IList{System.ArraySegment{System.Byte}})">
            <summary>
            Sends the data segments to client.
            </summary>
            <param name="segments">The segments.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.Send(System.String,System.Object[])">
            <summary>
            Sends the response.
            </summary>
            <param name="message">The message which will be sent.</param>
            <param name="paramValues">The parameter values.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.SetNextReceiveFilter(SuperSocket.SocketBase.Protocol.IReceiveFilter{`1})">
            <summary>
            Sets the next Receive filter which will be used when next data block received
            </summary>
            <param name="nextReceiveFilter">The next receive filter.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.GetMaxRequestLength">
            <summary>
            Gets the maximum allowed length of the request.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.FilterRequest(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Int32@,System.Int32@)">
            <summary>
            Filters the request.
            </summary>
            <param name="readBuffer">The read buffer.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
            <param name="rest">The rest, the size of the data which has not been processed</param>
            <param name="offsetDelta">return offset delta of next receiving buffer.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`2.SuperSocket#SocketBase#IAppSession#ProcessRequest(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Processes the request data.
            </summary>
            <param name="readBuffer">The read buffer.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
            <returns>
            return offset delta of next receiving buffer
            </returns>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.AppServer">
            <summary>
            Gets the app server instance assosiated with the session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.SuperSocket#SocketBase#IAppSession#AppServer">
            <summary>
            Gets the app server instance assosiated with the session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.Charset">
            <summary>
            Gets or sets the charset which is used for transfering text message.
            </summary>
            <value>
            The charset.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.Items">
            <summary>
            Gets the items dictionary, only support 10 items maximum
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.Connected">
            <summary>
            Gets a value indicating whether this <see cref="T:SuperSocket.SocketBase.IAppSession"/> is connected.
            </summary>
            <value>
              <c>true</c> if connected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.PrevCommand">
            <summary>
            Gets or sets the previous command.
            </summary>
            <value>
            The prev command.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.CurrentCommand">
            <summary>
            Gets or sets the current executing command.
            </summary>
            <value>
            The current command.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.SecureProtocol">
            <summary>
            Gets or sets the secure protocol of transportation layer.
            </summary>
            <value>
            The secure protocol.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.LocalEndPoint">
            <summary>
            Gets the local listening endpoint.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.RemoteEndPoint">
            <summary>
            Gets the remote endpoint of client.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.Logger">
            <summary>
            Gets the logger.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.LastActiveTime">
            <summary>
            Gets or sets the last active time of the session.
            </summary>
            <value>
            The last active time.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.StartTime">
            <summary>
            Gets the start time of the session.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.SessionID">
            <summary>
            Gets the session ID.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.SocketSession">
            <summary>
            Gets the socket session of the AppSession.
            </summary>
        </member>
        <member name="P:SuperSocket.SocketBase.AppSession`2.Config">
            <summary>
            Gets the config of the server.
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.AppSession`1">
            <summary>
            AppServer basic class for whose request infoe type is StringRequestInfo
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppSession`1"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`1.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.SocketBase.AppSession`1"/> class.
            </summary>
            <param name="appendNewLineForResponse">if set to <c>true</c> [append new line for response].</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`1.HandleUnknownRequest(SuperSocket.SocketBase.Protocol.StringRequestInfo)">
            <summary>
            Handles the unknown request.
            </summary>
            <param name="requestInfo">The request info.</param>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`1.ProcessSendingMessage(System.String)">
            <summary>
            Processes the sending message.
            </summary>
            <param name="rawMessage">The raw message.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`1.Send(System.String)">
            <summary>
            Sends the specified message.
            </summary>
            <param name="message">The message.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.SocketBase.AppSession`1.Send(System.String,System.Object[])">
            <summary>
            Sends the response.
            </summary>
            <param name="message">The message.</param>
            <param name="paramValues">The param values.</param>
            <returns>Indicate whether the message was pushed into the sending queue</returns>
        </member>
        <member name="T:SuperSocket.SocketBase.AppSession">
            <summary>
            AppServer basic class for whose request infoe type is StringRequestInfo
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.StringCommandBase`1">
            <summary>
            A command type for whose request info type is StringRequestInfo
            </summary>
            <typeparam name="TAppSession">The type of the app session.</typeparam>
        </member>
        <member name="T:SuperSocket.SocketBase.Command.StringCommandBase">
            <summary>
            A command type for whose request info type is StringRequestInfo
            </summary>
        </member>
        <member name="T:SuperSocket.SocketBase.StatusInfoCollection">
            <summary>
            Status information collection
            </summary>
        </member>
        <member name="M:SuperSocket.SocketBase.StatusInfoCollection.GetValue``1(System.String,``0)">
            <summary>
            Gets the value.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="name">The name.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.SocketBase.StatusInfoCollection.Values">
            <summary>
            Gets the values.
            </summary>
            <value>
            The values.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.StatusInfoCollection.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.StatusInfoCollection.Tag">
            <summary>
            Gets or sets the tag.
            </summary>
            <value>
            The tag.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.StatusInfoCollection.CollectedTime">
            <summary>
            Gets or sets the collected time.
            </summary>
            <value>
            The collected time.
            </value>
        </member>
        <member name="P:SuperSocket.SocketBase.StatusInfoCollection.Item(System.String)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified name.
            </summary>
            <value>
            The <see cref="T:System.Object"/>.
            </value>
            <param name="name">The name.</param>
            <returns></returns>
        </member>
    </members>
</doc>
