&lt;Window x:Class="Lite3DogMES.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="云深处科技 Lite3小狗 MES生产管理系统" 
        Height="1000" Width="1600" 
        WindowStartupLocation="CenterScreen" 
        WindowState="Maximized"
        Background="#FF2D2D30">
    
    &lt;Window.Resources>
        &lt;Style x:Key="ModernButtonStyle" TargetType="Button">
            &lt;Setter Property="Background" Value="#FF007ACC"/>
            &lt;Setter Property="Foreground" Value="White"/>
            &lt;Setter Property="BorderThickness" Value="0"/>
            &lt;Setter Property="Padding" Value="15,8"/>
            &lt;Setter Property="Margin" Value="5"/>
            &lt;Setter Property="FontSize" Value="14"/>
            &lt;Setter Property="FontWeight" Value="SemiBold"/>
            &lt;Setter Property="Cursor" Value="Hand"/>
            &lt;Setter Property="Template">
                &lt;Setter.Value>
                    &lt;ControlTemplate TargetType="Button">
                        &lt;Border Background="{TemplateBinding Background}" 
                                CornerRadius="3"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            &lt;ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        &lt;/Border>
                        &lt;ControlTemplate.Triggers>
                            &lt;Trigger Property="IsMouseOver" Value="True">
                                &lt;Setter Property="Background" Value="#FF1E90FF"/>
                            &lt;/Trigger>
                            &lt;Trigger Property="IsPressed" Value="True">
                                &lt;Setter Property="Background" Value="#FF0066CC"/>
                            &lt;/Trigger>
                        &lt;/ControlTemplate.Triggers>
                    &lt;/ControlTemplate>
                &lt;/Setter.Value>
            &lt;/Setter>
        &lt;/Style>

        &lt;Style x:Key="ModernTabItemStyle" TargetType="TabItem">
            &lt;Setter Property="Background" Value="#FF3F3F46"/>
            &lt;Setter Property="Foreground" Value="White"/>
            &lt;Setter Property="BorderThickness" Value="0"/>
            &lt;Setter Property="Padding" Value="20,10"/>
            &lt;Setter Property="Margin" Value="2,0"/>
            &lt;Setter Property="FontSize" Value="14"/>
            &lt;Setter Property="FontWeight" Value="SemiBold"/>
            &lt;Setter Property="Template">
                &lt;Setter.Value>
                    &lt;ControlTemplate TargetType="TabItem">
                        &lt;Border Background="{TemplateBinding Background}" 
                                CornerRadius="5,5,0,0"
                                BorderThickness="0">
                            &lt;ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"
                                            ContentSource="Header"/>
                        &lt;/Border>
                        &lt;ControlTemplate.Triggers>
                            &lt;Trigger Property="IsSelected" Value="True">
                                &lt;Setter Property="Background" Value="#FF007ACC"/>
                            &lt;/Trigger>
                            &lt;Trigger Property="IsMouseOver" Value="True">
                                &lt;Setter Property="Background" Value="#FF4A4A4F"/>
                            &lt;/Trigger>
                        &lt;/ControlTemplate.Triggers>
                    &lt;/ControlTemplate>
                &lt;/Setter.Value>
            &lt;/Setter>
        &lt;/Style>
    &lt;/Window.Resources>

    &lt;Grid>
        &lt;Grid.RowDefinitions>
            &lt;RowDefinition Height="80"/>
            &lt;RowDefinition Height="*"/>
            &lt;RowDefinition Height="30"/>
        &lt;/Grid.RowDefinitions>

        &lt;!-- 标题栏 -->
        &lt;Border Grid.Row="0" Background="#FF1E1E1E" BorderBrush="#FF007ACC" BorderThickness="0,0,0,2">
            &lt;Grid>
                &lt;Grid.ColumnDefinitions>
                    &lt;ColumnDefinition Width="*"/>
                    &lt;ColumnDefinition Width="Auto"/>
                &lt;/Grid.ColumnDefinitions>
                
                &lt;StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    &lt;Image Source="/Images/logo.png" Width="50" Height="50" Margin="0,0,15,0"/>
                    &lt;StackPanel VerticalAlignment="Center">
                        &lt;TextBlock Text="云深处科技" FontSize="24" FontWeight="Bold" Foreground="#FF007ACC"/>
                        &lt;TextBlock Text="Lite3小狗 MES生产管理系统" FontSize="16" Foreground="White"/>
                    &lt;/StackPanel>
                &lt;/StackPanel>

                &lt;StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    &lt;TextBlock x:Name="txtCurrentUser" Text="当前用户: 操作员" Foreground="White" Margin="0,0,20,0" VerticalAlignment="Center"/>
                    &lt;TextBlock x:Name="txtCurrentTime" Text="2024-01-01 12:00:00" Foreground="White" Margin="0,0,20,0" VerticalAlignment="Center"/>
                    &lt;Button x:Name="btnLogout" Content="退出登录" Style="{StaticResource ModernButtonStyle}" Click="BtnLogout_Click"/>
                &lt;/StackPanel>
            &lt;/Grid>
        &lt;/Border>

        &lt;!-- 主内容区 -->
        &lt;TabControl Grid.Row="1" Background="#FF2D2D30" BorderThickness="0">
            &lt;TabItem Header="工序管理" Style="{StaticResource ModernTabItemStyle}">
                &lt;Grid Background="#FF2D2D30">
                    &lt;Grid.ColumnDefinitions>
                        &lt;ColumnDefinition Width="300"/>
                        &lt;ColumnDefinition Width="*"/>
                    &lt;/Grid.ColumnDefinitions>

                    &lt;!-- 左侧控制面板 -->
                    &lt;Border Grid.Column="0" Background="#FF3F3F46" Margin="10" CornerRadius="5">
                        &lt;StackPanel Margin="15">
                            &lt;TextBlock Text="工序控制" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,0,15"/>
                            
                            &lt;GroupBox Header="产品信息" Foreground="White" Margin="0,0,0,15">
                                &lt;StackPanel>
                                    &lt;TextBox x:Name="txtProductSN" Margin="0,5" Height="30" FontSize="14" 
                                           Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"/>
                                    &lt;Button x:Name="btnScanQR" Content="扫描二维码" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnScanQR_Click"/>
                                &lt;/StackPanel>
                            &lt;/GroupBox>

                            &lt;GroupBox Header="工序操作" Foreground="White" Margin="0,0,0,15">
                                &lt;StackPanel>
                                    &lt;Button x:Name="btnStartAssembly" Content="开始装配" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnStartAssembly_Click"/>
                                    &lt;Button x:Name="btnCompleteAssembly" Content="完成装配" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnCompleteAssembly_Click"/>
                                    &lt;Button x:Name="btnCalibration" Content="标零操作" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnCalibration_Click"/>
                                    &lt;Button x:Name="btnFirstTest" Content="一测" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnFirstTest_Click"/>
                                    &lt;Button x:Name="btnMaintenance" Content="维护" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnMaintenance_Click"/>
                                    &lt;Button x:Name="btnSecondTest" Content="二测" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnSecondTest_Click"/>
                                    &lt;Button x:Name="btnPackaging" Content="打包" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnPackaging_Click"/>
                                    &lt;Button x:Name="btnShipping" Content="发货" Style="{StaticResource ModernButtonStyle}" 
                                          Click="BtnShipping_Click"/>
                                &lt;/StackPanel>
                            &lt;/GroupBox>
                        &lt;/StackPanel>
                    &lt;/Border>

                    &lt;!-- 右侧显示区域 -->
                    &lt;Border Grid.Column="1" Background="#FF3F3F46" Margin="10" CornerRadius="5">
                        &lt;Grid Margin="15">
                            &lt;Grid.RowDefinitions>
                                &lt;RowDefinition Height="Auto"/>
                                &lt;RowDefinition Height="*"/>
                            &lt;/Grid.RowDefinitions>

                            &lt;TextBlock Grid.Row="0" Text="产品状态监控" FontSize="18" FontWeight="Bold" 
                                     Foreground="White" Margin="0,0,0,15"/>

                            &lt;DataGrid x:Name="dgProductStatus" Grid.Row="1" 
                                    Background="#FF2D2D30" 
                                    Foreground="White"
                                    GridLinesVisibility="Horizontal"
                                    HeadersVisibility="Column"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    IsReadOnly="True">
                                &lt;DataGrid.Columns>
                                    &lt;DataGridTextColumn Header="产品序号" Binding="{Binding ProductSN}" Width="120"/>
                                    &lt;DataGridTextColumn Header="当前状态" Binding="{Binding CurrentStatus}" Width="100"/>
                                    &lt;DataGridTextColumn Header="当前位置" Binding="{Binding CurrentLocation}" Width="120"/>
                                    &lt;DataGridTextColumn Header="完成进度" Binding="{Binding CompletionRate, StringFormat={}{0:F1}%}" Width="100"/>
                                    &lt;DataGridTextColumn Header="操作员" Binding="{Binding CurrentOperatorID}" Width="100"/>
                                    &lt;DataGridTextColumn Header="开始时间" Binding="{Binding CurrentProcessStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                &lt;/DataGrid.Columns>
                            &lt;/DataGrid>
                        &lt;/Grid>
                    &lt;/Border>
                &lt;/Grid>
            &lt;/TabItem>

            &lt;TabItem Header="产品追溯" Style="{StaticResource ModernTabItemStyle}">
                &lt;Grid Background="#FF2D2D30" Margin="10">
                    &lt;Grid.RowDefinitions>
                        &lt;RowDefinition Height="Auto"/>
                        &lt;RowDefinition Height="*"/>
                    &lt;/Grid.RowDefinitions>

                    &lt;!-- 查询条件 -->
                    &lt;Border Grid.Row="0" Background="#FF3F3F46" Margin="0,0,0,10" CornerRadius="5" Padding="15">
                        &lt;StackPanel Orientation="Horizontal">
                            &lt;TextBlock Text="产品序号:" Foreground="White" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            &lt;TextBox x:Name="txtTraceProductSN" Width="150" Height="30" Margin="0,0,20,0"
                                   Background="#FF2D2D30" Foreground="White" BorderBrush="#FF007ACC"/>
                            &lt;Button x:Name="btnQueryTrace" Content="查询追溯" Style="{StaticResource ModernButtonStyle}" 
                                  Click="BtnQueryTrace_Click"/>
                            &lt;Button x:Name="btnGenerateReport" Content="生成报告" Style="{StaticResource ModernButtonStyle}" 
                                  Click="BtnGenerateReport_Click"/>
                        &lt;/StackPanel>
                    &lt;/Border>

                    &lt;!-- 追溯结果显示 -->
                    &lt;TabControl Grid.Row="1" Background="#FF3F3F46" BorderThickness="0">
                        &lt;TabItem Header="追溯时间线" Style="{StaticResource ModernTabItemStyle}">
                            &lt;ListView x:Name="lvTraceTimeline" Background="#FF2D2D30" Foreground="White" Margin="10">
                                &lt;ListView.View>
                                    &lt;GridView>
                                        &lt;GridViewColumn Header="时间" DisplayMemberBinding="{Binding EventTime, StringFormat=yyyy-MM-dd HH:mm:ss}" Width="150"/>
                                        &lt;GridViewColumn Header="事件类型" DisplayMemberBinding="{Binding EventType}" Width="120"/>
                                        &lt;GridViewColumn Header="事件描述" DisplayMemberBinding="{Binding EventDescription}" Width="300"/>
                                        &lt;GridViewColumn Header="位置" DisplayMemberBinding="{Binding Location}" Width="120"/>
                                        &lt;GridViewColumn Header="操作员" DisplayMemberBinding="{Binding OperatorID}" Width="100"/>
                                    &lt;/GridView>
                                &lt;/ListView.View>
                            &lt;/ListView>
                        &lt;/TabItem>

                        &lt;TabItem Header="工序记录" Style="{StaticResource ModernTabItemStyle}">
                            &lt;DataGrid x:Name="dgProcessRecords" Background="#FF2D2D30" Foreground="White" Margin="10"
                                    AutoGenerateColumns="False" IsReadOnly="True">
                                &lt;DataGrid.Columns>
                                    &lt;DataGridTextColumn Header="工序名称" Binding="{Binding ProcessName}" Width="120"/>
                                    &lt;DataGridTextColumn Header="开始时间" Binding="{Binding StartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                    &lt;DataGridTextColumn Header="结束时间" Binding="{Binding EndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                    &lt;DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                                    &lt;DataGridTextColumn Header="质量结果" Binding="{Binding QualityResult}" Width="100"/>
                                    &lt;DataGridTextColumn Header="用时(分钟)" Binding="{Binding ProcessDuration.TotalMinutes, StringFormat=F0}" Width="100"/>
                                    &lt;DataGridTextColumn Header="操作员" Binding="{Binding OperatorID}" Width="100"/>
                                &lt;/DataGrid.Columns>
                            &lt;/DataGrid>
                        &lt;/TabItem>

                        &lt;TabItem Header="质量记录" Style="{StaticResource ModernTabItemStyle}">
                            &lt;DataGrid x:Name="dgQualityRecords" Background="#FF2D2D30" Foreground="White" Margin="10"
                                    AutoGenerateColumns="False" IsReadOnly="True">
                                &lt;DataGrid.Columns>
                                    &lt;DataGridTextColumn Header="测试类型" Binding="{Binding TestType}" Width="120"/>
                                    &lt;DataGridTextColumn Header="测试参数" Binding="{Binding TestParameter}" Width="120"/>
                                    &lt;DataGridTextColumn Header="标准值" Binding="{Binding StandardValue}" Width="100"/>
                                    &lt;DataGridTextColumn Header="实际值" Binding="{Binding ActualValue}" Width="100"/>
                                    &lt;DataGridTextColumn Header="偏差%" Binding="{Binding DeviationPercent, StringFormat=F2}" Width="80"/>
                                    &lt;DataGridTextColumn Header="结果" Binding="{Binding TestResult}" Width="80"/>
                                    &lt;DataGridTextColumn Header="测试时间" Binding="{Binding TestTime, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                &lt;/DataGrid.Columns>
                            &lt;/DataGrid>
                        &lt;/TabItem>
                    &lt;/TabControl>
                &lt;/Grid>
            &lt;/TabItem>

            &lt;TabItem Header="质量管理" Style="{StaticResource ModernTabItemStyle}">
                &lt;Grid Background="#FF2D2D30" Margin="10">
                    &lt;!-- 质量管理内容将在后续添加 -->
                    &lt;TextBlock Text="质量管理功能开发中..." FontSize="24" Foreground="White" 
                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                &lt;/Grid>
            &lt;/TabItem>

            &lt;TabItem Header="统计报表" Style="{StaticResource ModernTabItemStyle}">
                &lt;Grid Background="#FF2D2D30" Margin="10">
                    &lt;!-- 统计报表内容将在后续添加 -->
                    &lt;TextBlock Text="统计报表功能开发中..." FontSize="24" Foreground="White" 
                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                &lt;/Grid>
            &lt;/TabItem>

            &lt;TabItem Header="系统设置" Style="{StaticResource ModernTabItemStyle}">
                &lt;Grid Background="#FF2D2D30" Margin="10">
                    &lt;!-- 系统设置内容将在后续添加 -->
                    &lt;TextBlock Text="系统设置功能开发中..." FontSize="24" Foreground="White" 
                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                &lt;/Grid>
            &lt;/TabItem>
        &lt;/TabControl>

        &lt;!-- 状态栏 -->
        &lt;Border Grid.Row="2" Background="#FF1E1E1E" BorderBrush="#FF007ACC" BorderThickness="0,1,0,0">
            &lt;Grid>
                &lt;Grid.ColumnDefinitions>
                    &lt;ColumnDefinition Width="*"/>
                    &lt;ColumnDefinition Width="Auto"/>
                &lt;/Grid.ColumnDefinitions>
                
                &lt;TextBlock x:Name="txtStatusMessage" Grid.Column="0" Text="系统就绪" 
                         Foreground="White" VerticalAlignment="Center" Margin="10,0"/>
                
                &lt;StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    &lt;TextBlock x:Name="txtConnectionStatus" Text="数据库连接: 正常" 
                             Foreground="LightGreen" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    &lt;TextBlock x:Name="txtSystemVersion" Text="版本: v1.0.0" 
                             Foreground="White" VerticalAlignment="Center"/>
                &lt;/StackPanel>
            &lt;/Grid>
        &lt;/Border>
    &lt;/Grid>
&lt;/Window>
