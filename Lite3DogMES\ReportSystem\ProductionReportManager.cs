using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace Lite3DogMES.ReportSystem
{
    /// <summary>
    /// 生产报表管理器
    /// </summary>
    public class ProductionReportManager
    {
        private readonly string _connectionString;

        public ProductionReportManager(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 生成日生产报表
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>日生产报表</returns>
        public DailyProductionReport GenerateDailyReport(DateTime date)
        {
            var report = new DailyProductionReport
            {
                ReportDate = date,
                GeneratedTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 获取当日生产统计
                    report.ProductionSummary = GetDailyProductionSummary(connection, date);
                    
                    // 获取工序统计
                    report.ProcessStatistics = GetDailyProcessStatistics(connection, date);
                    
                    // 获取质量统计
                    report.QualityStatistics = GetDailyQualityStatistics(connection, date);
                    
                    // 获取效率统计
                    report.EfficiencyStatistics = GetDailyEfficiencyStatistics(connection, date);
                    
                    // 获取异常统计
                    report.ExceptionStatistics = GetDailyExceptionStatistics(connection, date);
                }
            }
            catch (Exception ex)
            {
                report.ErrorMessage = $"生成日报表失败: {ex.Message}";
            }

            return report;
        }

        /// <summary>
        /// 生成周生产报表
        /// </summary>
        /// <param name="startDate">周开始日期</param>
        /// <returns>周生产报表</returns>
        public WeeklyProductionReport GenerateWeeklyReport(DateTime startDate)
        {
            var endDate = startDate.AddDays(6);
            var report = new WeeklyProductionReport
            {
                StartDate = startDate,
                EndDate = endDate,
                GeneratedTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 获取周生产趋势
                    report.DailyTrends = GetWeeklyProductionTrends(connection, startDate, endDate);
                    
                    // 获取周汇总统计
                    report.WeeklySummary = GetWeeklyProductionSummary(connection, startDate, endDate);
                    
                    // 获取工序效率对比
                    report.ProcessEfficiencyComparison = GetWeeklyProcessEfficiency(connection, startDate, endDate);
                    
                    // 获取质量趋势
                    report.QualityTrends = GetWeeklyQualityTrends(connection, startDate, endDate);
                }
            }
            catch (Exception ex)
            {
                report.ErrorMessage = $"生成周报表失败: {ex.Message}";
            }

            return report;
        }

        /// <summary>
        /// 生成月生产报表
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="month">月份</param>
        /// <returns>月生产报表</returns>
        public MonthlyProductionReport GenerateMonthlyReport(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);
            
            var report = new MonthlyProductionReport
            {
                Year = year,
                Month = month,
                StartDate = startDate,
                EndDate = endDate,
                GeneratedTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 获取月度生产统计
                    report.MonthlySummary = GetMonthlyProductionSummary(connection, startDate, endDate);
                    
                    // 获取日产量趋势
                    report.DailyProductionTrends = GetMonthlyDailyTrends(connection, startDate, endDate);
                    
                    // 获取工序性能分析
                    report.ProcessPerformanceAnalysis = GetMonthlyProcessPerformance(connection, startDate, endDate);
                    
                    // 获取质量分析
                    report.QualityAnalysis = GetMonthlyQualityAnalysis(connection, startDate, endDate);
                    
                    // 获取设备利用率
                    report.EquipmentUtilization = GetMonthlyEquipmentUtilization(connection, startDate, endDate);
                }
            }
            catch (Exception ex)
            {
                report.ErrorMessage = $"生成月报表失败: {ex.Message}";
            }

            return report;
        }

        /// <summary>
        /// 生成产品追溯报表
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <returns>产品追溯报表</returns>
        public ProductTraceabilityReport GenerateProductTraceabilityReport(string productSN)
        {
            var report = new ProductTraceabilityReport
            {
                ProductSN = productSN,
                GeneratedTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 获取产品基本信息
                    report.ProductInfo = GetProductBasicInfo(connection, productSN);
                    
                    if (report.ProductInfo == null)
                    {
                        report.ErrorMessage = "产品不存在";
                        return report;
                    }

                    // 获取工序执行详情
                    report.ProcessExecutionDetails = GetProductProcessDetails(connection, report.ProductInfo.ProductID);
                    
                    // 获取质量检测详情
                    report.QualityTestDetails = GetProductQualityDetails(connection, report.ProductInfo.ProductID);
                    
                    // 获取异常记录
                    report.ExceptionRecords = GetProductExceptionRecords(connection, report.ProductInfo.ProductID);
                    
                    // 获取操作员记录
                    report.OperatorRecords = GetProductOperatorRecords(connection, report.ProductInfo.ProductID);
                    
                    // 计算统计信息
                    report.Statistics = CalculateProductStatistics(report);
                }
            }
            catch (Exception ex)
            {
                report.ErrorMessage = $"生成产品追溯报表失败: {ex.Message}";
            }

            return report;
        }

        /// <summary>
        /// 生成质量分析报表
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>质量分析报表</returns>
        public QualityAnalysisReport GenerateQualityAnalysisReport(DateTime startDate, DateTime endDate)
        {
            var report = new QualityAnalysisReport
            {
                StartDate = startDate,
                EndDate = endDate,
                GeneratedTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 获取质量概览
                    report.QualityOverview = GetQualityOverview(connection, startDate, endDate);
                    
                    // 获取工序质量分析
                    report.ProcessQualityAnalysis = GetProcessQualityAnalysis(connection, startDate, endDate);
                    
                    // 获取缺陷分析
                    report.DefectAnalysis = GetDefectAnalysis(connection, startDate, endDate);
                    
                    // 获取质量趋势
                    report.QualityTrends = GetQualityTrendAnalysis(connection, startDate, endDate);
                    
                    // 获取改进建议
                    report.ImprovementSuggestions = GenerateQualityImprovementSuggestions(report);
                }
            }
            catch (Exception ex)
            {
                report.ErrorMessage = $"生成质量分析报表失败: {ex.Message}";
            }

            return report;
        }

        /// <summary>
        /// 生成效率分析报表
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>效率分析报表</returns>
        public EfficiencyAnalysisReport GenerateEfficiencyAnalysisReport(DateTime startDate, DateTime endDate)
        {
            var report = new EfficiencyAnalysisReport
            {
                StartDate = startDate,
                EndDate = endDate,
                GeneratedTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 获取效率概览
                    report.EfficiencyOverview = GetEfficiencyOverview(connection, startDate, endDate);
                    
                    // 获取工序效率分析
                    report.ProcessEfficiencyAnalysis = GetProcessEfficiencyAnalysis(connection, startDate, endDate);
                    
                    // 获取瓶颈分析
                    report.BottleneckAnalysis = GetBottleneckAnalysis(connection, startDate, endDate);
                    
                    // 获取操作员效率分析
                    report.OperatorEfficiencyAnalysis = GetOperatorEfficiencyAnalysis(connection, startDate, endDate);
                    
                    // 获取设备效率分析
                    report.EquipmentEfficiencyAnalysis = GetEquipmentEfficiencyAnalysis(connection, startDate, endDate);
                    
                    // 生成效率改进建议
                    report.ImprovementRecommendations = GenerateEfficiencyImprovementRecommendations(report);
                }
            }
            catch (Exception ex)
            {
                report.ErrorMessage = $"生成效率分析报表失败: {ex.Message}";
            }

            return report;
        }

        /// <summary>
        /// 导出报表到Excel
        /// </summary>
        /// <param name="report">报表对象</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool ExportToExcel(object report, string filePath)
        {
            try
            {
                // 使用EPPlus或其他Excel库导出报表
                // 这里提供一个基本的实现框架
                
                switch (report)
                {
                    case DailyProductionReport dailyReport:
                        return ExportDailyReportToExcel(dailyReport, filePath);
                    case WeeklyProductionReport weeklyReport:
                        return ExportWeeklyReportToExcel(weeklyReport, filePath);
                    case MonthlyProductionReport monthlyReport:
                        return ExportMonthlyReportToExcel(monthlyReport, filePath);
                    case QualityAnalysisReport qualityReport:
                        return ExportQualityReportToExcel(qualityReport, filePath);
                    case EfficiencyAnalysisReport efficiencyReport:
                        return ExportEfficiencyReportToExcel(efficiencyReport, filePath);
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出Excel失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导出报表到PDF
        /// </summary>
        /// <param name="report">报表对象</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool ExportToPDF(object report, string filePath)
        {
            try
            {
                // 使用iTextSharp或其他PDF库导出报表
                // 这里提供一个基本的实现框架
                
                // 实现PDF导出逻辑
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出PDF失败: {ex.Message}");
                return false;
            }
        }

        // 私有辅助方法
        private DailyProductionSummary GetDailyProductionSummary(SqlConnection connection, DateTime date)
        {
            var summary = new DailyProductionSummary();
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            var sql = @"
                SELECT
                    COUNT(DISTINCT p.ProductID) as TotalProducts,
                    COUNT(DISTINCT CASE WHEN p.IsCompleted = 1 THEN p.ProductID END) as CompletedProducts,
                    COUNT(DISTINCT CASE WHEN pr.QualityResult = '合格' THEN p.ProductID END) as QualifiedProducts
                FROM Products p
                LEFT JOIN ProcessRecords pr ON p.ProductID = pr.ProductID
                WHERE p.ProductionDate >= @StartDate AND p.ProductionDate < @EndDate";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        summary.TotalProducts = Convert.ToInt32(reader["TotalProducts"]);
                        summary.CompletedProducts = Convert.ToInt32(reader["CompletedProducts"]);
                        summary.QualifiedProducts = Convert.ToInt32(reader["QualifiedProducts"]);

                        if (summary.TotalProducts > 0)
                        {
                            summary.CompletionRate = (decimal)summary.CompletedProducts / summary.TotalProducts * 100;
                            summary.QualificationRate = (decimal)summary.QualifiedProducts / summary.TotalProducts * 100;
                        }
                    }
                }
            }

            return summary;
        }

        private List<ProcessStatistic> GetDailyProcessStatistics(SqlConnection connection, DateTime date)
        {
            var statistics = new List<ProcessStatistic>();
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            var sql = @"
                SELECT
                    pd.ProcessName,
                    pd.ProcessCode,
                    COUNT(pr.RecordID) as TotalCount,
                    COUNT(CASE WHEN pr.Status = '完成' THEN 1 END) as CompletedCount,
                    COUNT(CASE WHEN pr.QualityResult = '合格' THEN 1 END) as QualifiedCount,
                    AVG(CASE WHEN pr.EndTime IS NOT NULL AND pr.StartTime IS NOT NULL
                        THEN DATEDIFF(MINUTE, pr.StartTime, pr.EndTime) END) as AvgProcessTime
                FROM ProcessDefinitions pd
                LEFT JOIN ProcessRecords pr ON pd.ProcessID = pr.ProcessID
                    AND pr.StartTime >= @StartDate AND pr.StartTime < @EndDate
                WHERE pd.IsActive = 1
                GROUP BY pd.ProcessName, pd.ProcessCode, pd.ProcessOrder
                ORDER BY pd.ProcessOrder";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var statistic = new ProcessStatistic
                        {
                            ProcessName = reader["ProcessName"].ToString(),
                            ProcessCode = reader["ProcessCode"].ToString(),
                            TotalCount = Convert.ToInt32(reader["TotalCount"]),
                            CompletedCount = Convert.ToInt32(reader["CompletedCount"]),
                            QualifiedCount = Convert.ToInt32(reader["QualifiedCount"]),
                            AvgProcessTime = reader["AvgProcessTime"] == DBNull.Value ? 0 : Convert.ToDouble(reader["AvgProcessTime"])
                        };

                        if (statistic.TotalCount > 0)
                        {
                            statistic.CompletionRate = (decimal)statistic.CompletedCount / statistic.TotalCount * 100;
                            statistic.QualificationRate = (decimal)statistic.QualifiedCount / statistic.TotalCount * 100;
                        }

                        statistics.Add(statistic);
                    }
                }
            }

            return statistics;
        }

        private QualityStatistic GetDailyQualityStatistics(SqlConnection connection, DateTime date)
        {
            var statistic = new QualityStatistic();
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            var sql = @"
                SELECT
                    COUNT(*) as TotalTests,
                    COUNT(CASE WHEN TestResult = '合格' THEN 1 END) as PassedTests,
                    AVG(CASE WHEN StandardValue > 0
                        THEN ABS(ActualValue - StandardValue) / StandardValue * 100 END) as AvgDeviation
                FROM QualityTests
                WHERE TestTime >= @StartDate AND TestTime < @EndDate";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        statistic.TotalTests = Convert.ToInt32(reader["TotalTests"]);
                        statistic.PassedTests = Convert.ToInt32(reader["PassedTests"]);
                        statistic.AvgDeviation = reader["AvgDeviation"] == DBNull.Value ? 0 : Convert.ToDouble(reader["AvgDeviation"]);

                        if (statistic.TotalTests > 0)
                        {
                            statistic.PassRate = (decimal)statistic.PassedTests / statistic.TotalTests * 100;
                        }
                    }
                }
            }

            return statistic;
        }

        private EfficiencyStatistic GetDailyEfficiencyStatistics(SqlConnection connection, DateTime date)
        {
            var statistic = new EfficiencyStatistic();
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            // 计算平均生产周期时间
            var sql = @"
                SELECT
                    AVG(DATEDIFF(MINUTE, first_process.StartTime, last_process.EndTime)) as AvgCycleTime,
                    COUNT(DISTINCT p.ProductID) as ProductCount
                FROM Products p
                INNER JOIN (
                    SELECT ProductID, MIN(StartTime) as StartTime
                    FROM ProcessRecords
                    WHERE StartTime >= @StartDate AND StartTime < @EndDate
                    GROUP BY ProductID
                ) first_process ON p.ProductID = first_process.ProductID
                INNER JOIN (
                    SELECT ProductID, MAX(EndTime) as EndTime
                    FROM ProcessRecords
                    WHERE EndTime >= @StartDate AND EndTime < @EndDate AND Status = '完成'
                    GROUP BY ProductID
                ) last_process ON p.ProductID = last_process.ProductID
                WHERE p.IsCompleted = 1";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        statistic.AvgCycleTime = reader["AvgCycleTime"] == DBNull.Value ? 0 : Convert.ToDouble(reader["AvgCycleTime"]);
                        statistic.ProductCount = Convert.ToInt32(reader["ProductCount"]);

                        // 计算生产效率（产品数/小时）
                        if (statistic.AvgCycleTime > 0)
                        {
                            statistic.ProductionEfficiency = 60.0 / statistic.AvgCycleTime * statistic.ProductCount;
                        }
                    }
                }
            }

            return statistic;
        }

        private ExceptionStatistic GetDailyExceptionStatistics(SqlConnection connection, DateTime date)
        {
            var statistic = new ExceptionStatistic();
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            var sql = @"
                SELECT
                    COUNT(CASE WHEN QualityResult = '不合格' THEN 1 END) as QualityExceptions,
                    COUNT(CASE WHEN Status = '失败' THEN 1 END) as ProcessExceptions,
                    COUNT(CASE WHEN DATEDIFF(MINUTE, StartTime, ISNULL(EndTime, GETDATE())) >
                        (SELECT RequiredTime * 1.5 FROM ProcessDefinitions WHERE ProcessID = pr.ProcessID) THEN 1 END) as TimeoutExceptions
                FROM ProcessRecords pr
                WHERE StartTime >= @StartDate AND StartTime < @EndDate";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        statistic.QualityExceptions = Convert.ToInt32(reader["QualityExceptions"]);
                        statistic.ProcessExceptions = Convert.ToInt32(reader["ProcessExceptions"]);
                        statistic.TimeoutExceptions = Convert.ToInt32(reader["TimeoutExceptions"]);
                        statistic.TotalExceptions = statistic.QualityExceptions + statistic.ProcessExceptions + statistic.TimeoutExceptions;
                    }
                }
            }

            return statistic;
        }

        // Excel导出方法的基本实现
        private bool ExportDailyReportToExcel(DailyProductionReport report, string filePath)
        {
            // 使用EPPlus或其他Excel库实现
            // 这里提供基本框架
            return true;
        }

        private bool ExportWeeklyReportToExcel(WeeklyProductionReport report, string filePath)
        {
            return true;
        }

        private bool ExportMonthlyReportToExcel(MonthlyProductionReport report, string filePath)
        {
            return true;
        }

        private bool ExportQualityReportToExcel(QualityAnalysisReport report, string filePath)
        {
            return true;
        }

        private bool ExportEfficiencyReportToExcel(EfficiencyAnalysisReport report, string filePath)
        {
            return true;
        }

        // 其他辅助方法的占位符实现
        private List<DailyTrend> GetWeeklyProductionTrends(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<DailyTrend>();
        private WeeklySummary GetWeeklyProductionSummary(SqlConnection connection, DateTime startDate, DateTime endDate) => new WeeklySummary();
        private List<ProcessEfficiencyComparison> GetWeeklyProcessEfficiency(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<ProcessEfficiencyComparison>();
        private List<QualityTrend> GetWeeklyQualityTrends(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<QualityTrend>();
        private MonthlySummary GetMonthlyProductionSummary(SqlConnection connection, DateTime startDate, DateTime endDate) => new MonthlySummary();
        private List<DailyProductionTrend> GetMonthlyDailyTrends(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<DailyProductionTrend>();
        private List<ProcessPerformanceAnalysis> GetMonthlyProcessPerformance(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<ProcessPerformanceAnalysis>();
        private QualityAnalysis GetMonthlyQualityAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new QualityAnalysis();
        private List<EquipmentUtilization> GetMonthlyEquipmentUtilization(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<EquipmentUtilization>();
        private ProductBasicInfo GetProductBasicInfo(SqlConnection connection, string productSN) => new ProductBasicInfo();
        private List<ProcessExecutionDetail> GetProductProcessDetails(SqlConnection connection, string productID) => new List<ProcessExecutionDetail>();
        private List<QualityTestDetail> GetProductQualityDetails(SqlConnection connection, string productID) => new List<QualityTestDetail>();
        private List<ExceptionRecord> GetProductExceptionRecords(SqlConnection connection, string productID) => new List<ExceptionRecord>();
        private List<OperatorRecord> GetProductOperatorRecords(SqlConnection connection, string productID) => new List<OperatorRecord>();
        private ProductStatistics CalculateProductStatistics(ProductTraceabilityReport report) => new ProductStatistics();
        private QualityOverview GetQualityOverview(SqlConnection connection, DateTime startDate, DateTime endDate) => new QualityOverview();
        private List<ProcessQualityAnalysis> GetProcessQualityAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<ProcessQualityAnalysis>();
        private DefectAnalysis GetDefectAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new DefectAnalysis();
        private List<QualityTrendAnalysis> GetQualityTrendAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<QualityTrendAnalysis>();
        private List<string> GenerateQualityImprovementSuggestions(QualityAnalysisReport report) => new List<string>();
        private EfficiencyOverview GetEfficiencyOverview(SqlConnection connection, DateTime startDate, DateTime endDate) => new EfficiencyOverview();
        private List<ProcessEfficiencyAnalysis> GetProcessEfficiencyAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<ProcessEfficiencyAnalysis>();
        private BottleneckAnalysis GetBottleneckAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new BottleneckAnalysis();
        private List<OperatorEfficiencyAnalysis> GetOperatorEfficiencyAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<OperatorEfficiencyAnalysis>();
        private List<EquipmentEfficiencyAnalysis> GetEquipmentEfficiencyAnalysis(SqlConnection connection, DateTime startDate, DateTime endDate) => new List<EquipmentEfficiencyAnalysis>();
        private List<string> GenerateEfficiencyImprovementRecommendations(EfficiencyAnalysisReport report) => new List<string>();
    }
