<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SuperSocket.Common</name>
    </assembly>
    <members>
        <member name="P:SuperSocket.Common.ArraySegmentEx`1.Array">
            <summary>
            Gets the array.
            </summary>
        </member>
        <member name="P:SuperSocket.Common.ArraySegmentEx`1.Count">
            <summary>
            Gets the count.
            </summary>
        </member>
        <member name="P:SuperSocket.Common.ArraySegmentEx`1.Offset">
            <summary>
            Gets the offset.
            </summary>
        </member>
        <member name="T:SuperSocket.Common.ArraySegmentList`1">
            <summary>
            ArraySegmentList
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.ArraySegmentList`1"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.IndexOf(`0)">
            <summary>
            Determines the index of a specific item in the <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.IList`1"/>.</param>
            <returns>
            The index of <paramref name="item"/> if found in the list; otherwise, -1.
            </returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.Insert(System.Int32,`0)">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.RemoveAt(System.Int32)">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.Add(`0)">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.Clear">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.Contains(`0)">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.CopyTo(`0[],System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.Remove(`0)">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.GetEnumerator">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            NotSupported
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.RemoveSegmentAt(System.Int32)">
            <summary>
            Removes the segment at.
            </summary>
            <param name="index">The index.</param>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.AddSegment(`0[],System.Int32,System.Int32)">
            <summary>
            Adds the segment to the list.
            </summary>
            <param name="array">The array.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.AddSegment(`0[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Adds the segment to the list.
            </summary>
            <param name="array">The array.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="toBeCopied">if set to <c>true</c> [to be copied].</param>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.ClearSegements">
            <summary>
            Clears all the segements.
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.ToArrayData">
            <summary>
            Read all data in this list to the array data.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.ToArrayData(System.Int32,System.Int32)">
            <summary>
            Read the data in specific range to the array data.
            </summary>
            <param name="startIndex">The start index.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.TrimEnd(System.Int32)">
            <summary>
            Trims the end.
            </summary>
            <param name="trimSize">Size of the trim.</param>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.SearchLastSegment(SuperSocket.Common.SearchMarkState{`0})">
            <summary>
            Searches the last segment.
            </summary>
            <param name="state">The state.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.CopyTo(`0[])">
            <summary>
            Copies to.
            </summary>
            <param name="to">To.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList`1.CopyTo(`0[],System.Int32,System.Int32,System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="to">To.</param>
            <param name="srcIndex">Index of the SRC.</param>
            <param name="toIndex">To index.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="P:SuperSocket.Common.ArraySegmentList`1.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
            <returns>
            The element at the specified index.
              </returns>
              
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index in the <see cref="T:System.Collections.Generic.IList`1"/>.
              </exception>
              
            <exception cref="T:System.NotSupportedException">
            The property is set and the <see cref="T:System.Collections.Generic.IList`1"/> is read-only.
              </exception>
        </member>
        <member name="P:SuperSocket.Common.ArraySegmentList`1.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <returns>
            The number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1"/>.
              </returns>
        </member>
        <member name="P:SuperSocket.Common.ArraySegmentList`1.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1"/> is read-only; otherwise, false.
              </returns>
        </member>
        <member name="P:SuperSocket.Common.ArraySegmentList`1.SegmentCount">
            <summary>
            Gets the segment count.
            </summary>
        </member>
        <member name="T:SuperSocket.Common.ArraySegmentList">
            <summary>
            ArraySegmentList
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList.Decode(System.Text.Encoding)">
            <summary>
            Decodes bytes to string by the specified encoding.
            </summary>
            <param name="encoding">The encoding.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList.Decode(System.Text.Encoding,System.Int32,System.Int32)">
            <summary>
            Decodes bytes to string by the specified encoding.
            </summary>
            <param name="encoding">The encoding.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ArraySegmentList.DecodeMask(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Decodes data by the mask.
            </summary>
            <param name="mask">The mask.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
        </member>
        <member name="T:SuperSocket.Common.AssemblyUtil">
            <summary>
            Assembly Util Class
            </summary>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.CreateInstance``1(System.String)">
            <summary>
            Creates the instance from type name.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.CreateInstance``1(System.String,System.Object[])">
            <summary>
            Creates the instance from type name and parameters.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="type">The type.</param>
            <param name="parameters">The parameters.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.GetType(System.String,System.Boolean,System.Boolean)">
            <summary>
            Gets the type by the full name, also return matched generic type without checking generic type parameters in the name.
            </summary>
            <param name="fullTypeName">Full name of the type.</param>
            <param name="throwOnError">if set to <c>true</c> [throw on error].</param>
            <param name="ignoreCase">if set to <c>true</c> [ignore case].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.GetImplementTypes``1(System.Reflection.Assembly)">
            <summary>
            Gets the implement types from assembly.
            </summary>
            <typeparam name="TBaseType">The type of the base type.</typeparam>
            <param name="assembly">The assembly.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.GetImplementedObjectsByInterface``1(System.Reflection.Assembly)">
            <summary>
            Gets the implemented objects by interface.
            </summary>
            <typeparam name="TBaseInterface">The type of the base interface.</typeparam>
            <param name="assembly">The assembly.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.GetImplementedObjectsByInterface``1(System.Reflection.Assembly,System.Type)">
            <summary>
            Gets the implemented objects by interface.
            </summary>
            <typeparam name="TBaseInterface">The type of the base interface.</typeparam>
            <param name="assembly">The assembly.</param>
            <param name="targetType">Type of the target.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.BinaryClone``1(``0)">
            <summary>
            Clone object in binary format.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="target">The target.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.CopyPropertiesTo``1(``0,``0)">
            <summary>
            Copies the properties of one object to another object.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.CopyPropertiesTo``1(``0,System.Predicate{System.Reflection.PropertyInfo},``0)">
            <summary>
            Copies the properties of one object to another object.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="predict">The properties predict.</param>
            <param name="target">The target.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.GetAssembliesFromString(System.String)">
            <summary>
            Gets the assemblies from string.
            </summary>
            <param name="assemblyDef">The assembly def.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.AssemblyUtil.GetAssembliesFromStrings(System.String[])">
            <summary>
            Gets the assemblies from strings.
            </summary>
            <param name="assemblies">The assemblies.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.BinaryUtil">
            <summary>
            Binary util class
            </summary>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.IndexOf``1(System.Collections.Generic.IList{``0},``0,System.Int32,System.Int32)">
            <summary>
            Search target from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="target">The target.</param>
            <param name="pos">The pos.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},``0[],System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},``0[])">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[])">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[],System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[],System.Int32)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <param name="matched">The matched.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[],System.Int32,System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <param name="matched">The matched.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,SuperSocket.Common.SearchMarkState{``0},System.Int32@)">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="searchState">State of the search.</param>
            <param name="parsedLength">Length of the parsed.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.SearchMark``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,SuperSocket.Common.SearchMarkState{``0})">
            <summary>
            Searches the mark from source.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="searchState">State of the search.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.StartsWith``1(System.Collections.Generic.IList{``0},``0[])">
            <summary>
            Startses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.StartsWith``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[])">
            <summary>
            Startses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.EndsWith``1(System.Collections.Generic.IList{``0},``0[])">
            <summary>
            Endses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.EndsWith``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,``0[])">
            <summary>
            Endses the with.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="mark">The mark.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.BinaryUtil.CloneRange``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32)">
            <summary>
            Clones the elements in the specific range.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="source">The source.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.BufferManager">
            <summary>
            This class creates a single large buffer which can be divided up and assigned to SocketAsyncEventArgs objects for use
            with each socket I/O operation.  This enables bufffers to be easily reused and gaurds against fragmenting heap memory.
            
            The operations exposed on the BufferManager class are not thread safe.
            </summary>
        </member>
        <member name="M:SuperSocket.Common.BufferManager.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.BufferManager"/> class.
            </summary>
            <param name="totalBytes">The total bytes.</param>
            <param name="bufferSize">Size of the buffer.</param>
        </member>
        <member name="M:SuperSocket.Common.BufferManager.InitBuffer">
            <summary>
            Allocates buffer space used by the buffer pool
            </summary>
        </member>
        <member name="M:SuperSocket.Common.BufferManager.SetBuffer(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            Assigns a buffer from the buffer pool to the specified SocketAsyncEventArgs object
            </summary>
            <returns>true if the buffer was successfully set, else false</returns>
        </member>
        <member name="M:SuperSocket.Common.BufferManager.FreeBuffer(System.Net.Sockets.SocketAsyncEventArgs)">
            <summary>
            Removes the buffer from a SocketAsyncEventArg object.  This frees the buffer back to the 
            buffer pool
            </summary>
        </member>
        <member name="T:SuperSocket.Common.ConfigurationElementBase">
            <summary>
            ConfigurationElementBase
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.ConfigurationElementBase"/> class.
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.ConfigurationElementBase"/> class.
            </summary>
            <param name="nameRequired">if set to <c>true</c> [name required].</param>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.DeserializeElement(System.Xml.XmlReader,System.Boolean)">
            <summary>
            Reads XML from the configuration file.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> that reads from the configuration file.</param>
            <param name="serializeCollectionKey">true to serialize only the collection key properties; otherwise, false.</param>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element to read is locked.- or -An attribute of the current node is not recognized.- or -The lock status of the current node cannot be determined.  </exception>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Gets a value indicating whether an unknown attribute is encountered during deserialization.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns>
            true when an unknown attribute is encountered while deserializing; otherwise, false.
            </returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Modifies the <see cref="T:System.Configuration.ConfigurationElement" /> object to remove all values that should not be saved.
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement" /> at the current level containing a merged view of the properties.</param>
            <param name="parentElement">The parent <see cref="T:System.Configuration.ConfigurationElement" />, or null if this is the top level.</param>
            <param name="saveMode">A <see cref="T:System.Configuration.ConfigurationSaveMode" /> that determines which property values to include.</param>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.SerializeElement(System.Xml.XmlWriter,System.Boolean)">
            <summary>
            Writes the contents of this configuration element to the configuration file when implemented in a derived class.
            </summary>
            <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> that writes to the configuration file.</param>
            <param name="serializeCollectionKey">true to serialize only the collection key properties; otherwise, false.</param>
            <returns>
            true if any data was actually serialized; otherwise, false.
            </returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationElementBase.OnDeserializeUnrecognizedElement(System.String,System.Xml.XmlReader)">
            <summary>
            Gets a value indicating whether an unknown element is encountered during deserialization.
            </summary>
            <param name="elementName">The name of the unknown subelement.</param>
            <param name="reader">The <see cref="T:System.Xml.XmlReader"/> being used for deserialization.</param>
            <returns>
            true when an unknown element is encountered while deserializing; otherwise, false.
            </returns>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The element identified by <paramref name="elementName"/> is locked.- or -One or more of the element's attributes is locked.- or -<paramref name="elementName"/> is unrecognized, or the element has an unrecognized attribute.- or -The element has a Boolean attribute with an invalid value.- or -An attempt was made to deserialize a property more than once.- or -An attempt was made to deserialize a property that is not a valid member of the element.- or -The element cannot contain a CDATA or text element.</exception>
        </member>
        <member name="P:SuperSocket.Common.ConfigurationElementBase.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="P:SuperSocket.Common.ConfigurationElementBase.Options">
            <summary>
            Gets the options.
            </summary>
        </member>
        <member name="P:SuperSocket.Common.ConfigurationElementBase.OptionElements">
            <summary>
            Gets the option elements.
            </summary>
        </member>
        <member name="T:SuperSocket.Common.ConfigurationExtension">
            <summary>
            Configuration extension class
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.GetValue(System.Collections.Specialized.NameValueCollection,System.String)">
            <summary>
            Gets the value from namevalue collection by key.
            </summary>
            <param name="collection">The collection.</param>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.GetValue(System.Collections.Specialized.NameValueCollection,System.String,System.String)">
            <summary>
            Gets the value from namevalue collection by key.
            </summary>
            <param name="collection">The collection.</param>
            <param name="key">The key.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.Deserialize``1(``0,System.Xml.XmlReader)">
            <summary>
            Deserializes the specified configuration section.
            </summary>
            <typeparam name="TElement">The type of the element.</typeparam>
            <param name="section">The section.</param>
            <param name="reader">The reader.</param>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.DeserializeChildConfig``1(System.String)">
            <summary>
            Deserializes the child configuration.
            </summary>
            <typeparam name="TConfig">The type of the configuration.</typeparam>
            <param name="childConfig">The child configuration string.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.GetChildConfig``1(System.Collections.Specialized.NameValueCollection,System.String)">
            <summary>
            Gets the child config.
            </summary>
            <typeparam name="TConfig">The type of the config.</typeparam>
            <param name="childElements">The child elements.</param>
            <param name="childConfigName">Name of the child config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.GetConfigSource(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the config source path.
            </summary>
            <param name="config">The config.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.LoadFrom(System.Configuration.ConfigurationElement,System.Object)">
            <summary>
            Loads configuration element's node information from a model.
            </summary>
            <param name="configElement">The config element.</param>
            <param name="source">The source.</param>
            <exception cref="T:System.Exception">Cannot find expected property 'Item' from the type 'ConfigurationElement'.</exception>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.GetCurrentConfiguration(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the current configuration of the configuration element.
            </summary>
            <returns>The current configuration.</returns>
            <param name="configElement">Configuration element.</param>
        </member>
        <member name="M:SuperSocket.Common.ConfigurationExtension.ResetConfiguration(System.AppDomain,System.String)">
            <summary>
            Reset application's configuration to a another config file
            </summary>
            <param name="appDomain">the assosiated AppDomain</param>
            <param name="configFilePath">the config file path want to reset to</param>
        </member>
        <member name="T:SuperSocket.Common.DictionaryExtension">
            <summary>
            Extension class for IDictionary
            </summary>
        </member>
        <member name="M:SuperSocket.Common.DictionaryExtension.GetValue``1(System.Collections.Generic.IDictionary{System.Object,System.Object},System.Object)">
            <summary>
            Gets the value by key.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dictionary">The dictionary.</param>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.DictionaryExtension.GetValue``1(System.Collections.Generic.IDictionary{System.Object,System.Object},System.Object,``0)">
            <summary>
            Gets the value by key and default value.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dictionary">The dictionary.</param>
            <param name="key">The key.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.ErrorEventArgs">
            <summary>
            EventArgs for error and exception
            </summary>
        </member>
        <member name="M:SuperSocket.Common.ErrorEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.ErrorEventArgs"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:SuperSocket.Common.ErrorEventArgs.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.ErrorEventArgs"/> class.
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="P:SuperSocket.Common.ErrorEventArgs.Exception">
            <summary>
            Gets the exception.
            </summary>
        </member>
        <member name="T:SuperSocket.Common.GenericConfigurationElementCollectionBase`2">
            <summary>
            GenericConfigurationElementCollectionBase
            </summary>
            <typeparam name="TConfigElement">The type of the config element.</typeparam>
            <typeparam name="TConfigInterface">The type of the config interface.</typeparam>
        </member>
        <member name="M:SuperSocket.Common.GenericConfigurationElementCollectionBase`2.CreateNewElement">
            <summary>
            When overridden in a derived class, creates a new <see cref="T:System.Configuration.ConfigurationElement"/>.
            </summary>
            <returns>
            A new <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
        </member>
        <member name="M:SuperSocket.Common.GenericConfigurationElementCollectionBase`2.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key for a specified configuration element when overridden in a derived class.
            </summary>
            <param name="element">The <see cref="T:System.Configuration.ConfigurationElement"/> to return the key for.</param>
            <returns>
            An <see cref="T:System.Object"/> that acts as the key for the specified <see cref="T:System.Configuration.ConfigurationElement"/>.
            </returns>
        </member>
        <member name="M:SuperSocket.Common.GenericConfigurationElementCollectionBase`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:SuperSocket.Common.GenericConfigurationElementCollectionBase`2.Item(System.Int32)">
            <summary>
            Gets or sets a property, attribute, or child element of this configuration element.
            </summary>
            <returns>The specified property, attribute, or child element</returns>
        </member>
        <member name="T:SuperSocket.Common.GenericConfigurationElementCollection`2">
            <summary>
            GenericConfigurationElementCollection
            </summary>
            <typeparam name="TConfigElement">The type of the config element.</typeparam>
            <typeparam name="TConfigInterface">The type of the config interface.</typeparam>
        </member>
        <member name="M:SuperSocket.Common.GenericConfigurationElementCollection`2.GetElementKey(System.Configuration.ConfigurationElement)">
            <summary>
            Gets the element key.
            </summary>
            <param name="element">The element.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.Platform">
            <summary>
            This class is designed for detect platform attribute in runtime
            </summary>
        </member>
        <member name="P:SuperSocket.Common.Platform.SupportSocketIOControlByCodeEnum">
            <summary>
            Gets a value indicating whether [support socket IO control by code enum].
            </summary>
            <value>
            	<c>true</c> if [support socket IO control by code enum]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:SuperSocket.Common.Platform.IsMono">
            <summary>
            Gets a value indicating whether this instance is mono.
            </summary>
            <value>
              <c>true</c> if this instance is mono; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:SuperSocket.Common.SearchMarkState`1">
            <summary>
            SearchMarkState
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.Common.SearchMarkState`1.#ctor(`0[])">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.SearchMarkState`1"/> class.
            </summary>
            <param name="mark">The mark.</param>
        </member>
        <member name="P:SuperSocket.Common.SearchMarkState`1.Mark">
            <summary>
            Gets the mark.
            </summary>
        </member>
        <member name="P:SuperSocket.Common.SearchMarkState`1.Matched">
            <summary>
            Gets or sets whether matched already.
            </summary>
            <value>
            The matched.
            </value>
        </member>
        <member name="T:SuperSocket.Common.SendingQueue">
            <summary>
            SendingQueue
            </summary>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.#ctor(System.ArraySegment{System.Byte}[],System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.SendingQueue"/> class.
            </summary>
            <param name="globalQueue">The global queue.</param>
            <param name="offset">The offset.</param>
            <param name="capacity">The capacity.</param>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Enqueue(System.ArraySegment{System.Byte},System.UInt16)">
            <summary>
            Enqueues the specified item.
            </summary>
            <param name="item">The item.</param>
            <param name="trackID">The track ID.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Enqueue(System.Collections.Generic.IList{System.ArraySegment{System.Byte}},System.UInt16)">
            <summary>
            Enqueues the specified items.
            </summary>
            <param name="items">The items.</param>
            <param name="trackID">The track ID.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.StopEnqueue">
            <summary>
            Stops the enqueue, and then wait all current excueting enqueu threads exit.
            </summary>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.StartEnqueue">
            <summary>
            Starts to allow enqueue.
            </summary>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.IndexOf(System.ArraySegment{System.Byte})">
            <summary>
            Determines the index of a specific item in the <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.IList`1"/>.</param>
            <returns>
            The index of <paramref name="item"/> if found in the list; otherwise, -1.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Insert(System.Int32,System.ArraySegment{System.Byte})">
            <summary>
            Inserts an item to the <see cref="T:System.Collections.Generic.IList`1"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item"/> should be inserted.</param>
            <param name="item">The object to insert into the <see cref="T:System.Collections.Generic.IList`1"/>.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.RemoveAt(System.Int32)">
            <summary>
            Removes the <see cref="T:System.Collections.Generic.IList`1"/> item at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Add(System.ArraySegment{System.Byte})">
            <summary>
            Adds an item to the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <param name="item">The object to add to the <see cref="T:System.Collections.Generic.ICollection`1"/>.</param>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Contains(System.ArraySegment{System.Byte})">
            <summary>
            Determines whether the <see cref="T:System.Collections.Generic.ICollection`1"/> contains a specific value.
            </summary>
            <param name="item">The object to locate in the <see cref="T:System.Collections.Generic.ICollection`1"/>.</param>
            <returns>
            true if <paramref name="item"/> is found in the <see cref="T:System.Collections.Generic.ICollection`1"/>; otherwise, false.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.CopyTo(System.ArraySegment{System.Byte}[],System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.Remove(System.ArraySegment{System.Byte})">
            <summary>
            Removes the first occurrence of a specific object from the <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </summary>
            <param name="item">The object to remove from the <see cref="T:System.Collections.Generic.ICollection`1"/>.</param>
            <returns>
            true if <paramref name="item"/> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1"/>; otherwise, false. This method also returns false if <paramref name="item"/> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:SuperSocket.Common.SendingQueue.InternalTrim(System.Int32)">
            <summary>
            Trim the internal segments at the begining by the binary data size.
            </summary>
            <param name="offset">The binary data size should be trimed at the begining.</param>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.TrackID">
            <summary>
            Gets the track ID.
            </summary>
            <value>
            The track ID.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.GlobalQueue">
            <summary>
            Gets the global queue.
            </summary>
            <value>
            The global queue.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.Offset">
            <summary>
            Gets the offset.
            </summary>
            <value>
            The offset.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.Capacity">
            <summary>
            Gets the capacity.
            </summary>
            <value>
            The capacity.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <returns>The number of elements contained in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.Position">
            <summary>
            Gets or sets the position.
            </summary>
            <value>
            The position.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="P:SuperSocket.Common.SendingQueue.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.</returns>
        </member>
        <member name="T:SuperSocket.Common.SendingQueueSourceCreator">
            <summary>
            SendingQueueSourceCreator
            </summary>
        </member>
        <member name="T:SuperSocket.Common.ISmartPoolSourceCreator`1">
            <summary>
            ISmartPoolSourceCreator
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.Common.ISmartPoolSourceCreator`1.Create(System.Int32,`0[]@)">
            <summary>
            Creates the specified size.
            </summary>
            <param name="size">The size.</param>
            <param name="poolItems">The pool items.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.SendingQueueSourceCreator.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.SendingQueueSourceCreator"/> class.
            </summary>
            <param name="sendingQueueSize">Size of the sending queue.</param>
        </member>
        <member name="M:SuperSocket.Common.SendingQueueSourceCreator.Create(System.Int32,SuperSocket.Common.SendingQueue[]@)">
            <summary>
            Creates the specified size.
            </summary>
            <param name="size">The size.</param>
            <param name="poolItems">The pool items.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.IPoolInfo">
            <summary>
            The pool information class
            </summary>
        </member>
        <member name="P:SuperSocket.Common.IPoolInfo.MinPoolSize">
            <summary>
            Gets the min size of the pool.
            </summary>
            <value>
            The min size of the pool.
            </value>
        </member>
        <member name="P:SuperSocket.Common.IPoolInfo.MaxPoolSize">
            <summary>
            Gets the max size of the pool.
            </summary>
            <value>
            The max size of the pool.
            </value>
        </member>
        <member name="P:SuperSocket.Common.IPoolInfo.AvialableItemsCount">
            <summary>
            Gets the avialable items count.
            </summary>
            <value>
            The avialable items count.
            </value>
        </member>
        <member name="P:SuperSocket.Common.IPoolInfo.TotalItemsCount">
            <summary>
            Gets the total items count, include items in the pool and outside the pool.
            </summary>
            <value>
            The total items count.
            </value>
        </member>
        <member name="T:SuperSocket.Common.ISmartPool`1">
            <summary>
            The basic interface of smart pool
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.Common.ISmartPool`1.Initialize(System.Int32,System.Int32,SuperSocket.Common.ISmartPoolSourceCreator{`0})">
            <summary>
            Initializes the specified min pool size.
            </summary>
            <param name="minPoolSize">The min size of the pool.</param>
            <param name="maxPoolSize">The max size of the pool.</param>
            <param name="sourceCreator">The source creator.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.ISmartPool`1.Push(`0)">
            <summary>
            Pushes the specified item into the pool.
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:SuperSocket.Common.ISmartPool`1.TryGet(`0@)">
            <summary>
            Tries to get one item from the pool.
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.ISmartPoolSource">
            <summary>
            ISmartPoolSource
            </summary>
        </member>
        <member name="P:SuperSocket.Common.ISmartPoolSource.Count">
            <summary>
            Gets the count.
            </summary>
            <value>
            The count.
            </value>
        </member>
        <member name="T:SuperSocket.Common.SmartPoolSource">
            <summary>
            SmartPoolSource
            </summary>
        </member>
        <member name="M:SuperSocket.Common.SmartPoolSource.#ctor(System.Object,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:SuperSocket.Common.SmartPoolSource"/> class.
            </summary>
            <param name="source">The source.</param>
            <param name="itemsCount">The items count.</param>
        </member>
        <member name="P:SuperSocket.Common.SmartPoolSource.Source">
            <summary>
            Gets the source.
            </summary>
            <value>
            The source.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SmartPoolSource.Count">
            <summary>
            Gets the count.
            </summary>
            <value>
            The count.
            </value>
        </member>
        <member name="T:SuperSocket.Common.SmartPool`1">
            <summary>
            The smart pool
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:SuperSocket.Common.SmartPool`1.Initialize(System.Int32,System.Int32,SuperSocket.Common.ISmartPoolSourceCreator{`0})">
            <summary>
            Initializes the specified min and max pool size.
            </summary>
            <param name="minPoolSize">The min size of the pool.</param>
            <param name="maxPoolSize">The max size of the pool.</param>
            <param name="sourceCreator">The source creator.</param>
        </member>
        <member name="M:SuperSocket.Common.SmartPool`1.Push(`0)">
            <summary>
            Pushes the specified item into the pool.
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:SuperSocket.Common.SmartPool`1.TryGet(`0@)">
            <summary>
            Tries to get one item from the pool.
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="P:SuperSocket.Common.SmartPool`1.MinPoolSize">
            <summary>
            Gets the size of the min pool.
            </summary>
            <value>
            The size of the min pool.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SmartPool`1.MaxPoolSize">
            <summary>
            Gets the size of the max pool.
            </summary>
            <value>
            The size of the max pool.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SmartPool`1.AvialableItemsCount">
            <summary>
            Gets the avialable items count.
            </summary>
            <value>
            The avialable items count.
            </value>
        </member>
        <member name="P:SuperSocket.Common.SmartPool`1.TotalItemsCount">
            <summary>
            Gets the total items count, include items in the pool and outside the pool.
            </summary>
            <value>
            The total items count.
            </value>
        </member>
        <member name="T:SuperSocket.Common.SocketEx">
            <summary>
            Socket extension class
            </summary>
        </member>
        <member name="M:SuperSocket.Common.SocketEx.SafeClose(System.Net.Sockets.Socket)">
            <summary>
            Close the socket safely.
            </summary>
            <param name="socket">The socket.</param>
        </member>
        <member name="M:SuperSocket.Common.SocketEx.SendData(System.Net.Sockets.Socket,System.Byte[])">
            <summary>
            Sends the data.
            </summary>
            <param name="client">The client.</param>
            <param name="data">The data.</param>
        </member>
        <member name="M:SuperSocket.Common.SocketEx.SendData(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Sends the data.
            </summary>
            <param name="client">The client.</param>
            <param name="data">The data.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
        </member>
        <member name="T:SuperSocket.Common.StringExtension">
            <summary>
            String extension class
            </summary>
            <summary>
            String extension
            </summary>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToInt32(System.String)">
            <summary>
            Convert string to int32.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToInt32(System.String,System.Int32)">
            <summary>
            Convert string to int32.
            </summary>
            <param name="source">The source.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToLong(System.String)">
            <summary>
            Convert string to long.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToLong(System.String,System.Int64)">
            <summary>
            Convert string to long.
            </summary>
            <param name="source">The source.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToShort(System.String)">
            <summary>
            Convert string to short.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToShort(System.String,System.Int16)">
            <summary>
            Convert string to short.
            </summary>
            <param name="source">The source.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToDecimal(System.String)">
            <summary>
            Convert string to decimal.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToDecimal(System.String,System.Decimal)">
            <summary>
            Convert string to decimal.
            </summary>
            <param name="source">The source.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToDateTime(System.String)">
            <summary>
            Convert string to date time.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToDateTime(System.String,System.DateTime)">
            <summary>
            Convert string to date time.
            </summary>
            <param name="source">The source.</param>
            <param name="defaultValue">The default value.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToBoolean(System.String)">
            <summary>
            Convert string to boolean.
            </summary>
            <param name="source">The source.</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.ToBoolean(System.String,System.Boolean)">
            <summary>
            Convert string tp boolean.
            </summary>
            <param name="source">The source.</param>
            <param name="defaultValue">if set to <c>true</c> [default value].</param>
            <returns></returns>
        </member>
        <member name="M:SuperSocket.Common.StringExtension.TryParseEnum``1(System.String,System.Boolean,``0@)">
            <summary>
            Tries parse string to enum.
            </summary>
            <typeparam name="T">the enum type</typeparam>
            <param name="value">The value.</param>
            <param name="ignoreCase">if set to <c>true</c> [ignore case].</param>
            <param name="enumValue">The enum value.</param>
            <returns></returns>
        </member>
        <member name="T:SuperSocket.Common.TheadPoolEx">
            <summary>
            Thread pool extension class
            </summary>
        </member>
        <member name="M:SuperSocket.Common.TheadPoolEx.ResetThreadPool(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Resets the thread pool.
            </summary>
            <param name="maxWorkingThreads">The max working threads.</param>
            <param name="maxCompletionPortThreads">The max completion port threads.</param>
            <param name="minWorkingThreads">The min working threads.</param>
            <param name="minCompletionPortThreads">The min completion port threads.</param>
            <returns></returns>
        </member>
    </members>
</doc>
