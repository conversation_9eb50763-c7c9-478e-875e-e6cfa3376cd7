﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{18F86945-9CB9-4149-A09C-85B8C5C7C4ED}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TagConfig</RootNamespace>
    <AssemblyName>TagConfig</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Wizard.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Office.Interop.Excel, Version=12.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>bin\Debug\Microsoft.Office.Interop.Excel.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AlarmForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AlarmForm.Designer.cs">
      <DependentUpon>AlarmForm.cs</DependentUpon>
    </Compile>
    <Compile Include="AlarmParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AlarmParam.Designer.cs">
      <DependentUpon>AlarmParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Condition.cs" />
    <Compile Include="DataConvert.cs" />
    <Compile Include="ExMethos.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="RegisterSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RegisterSet.Designer.cs">
      <DependentUpon>RegisterSet.cs</DependentUpon>
    </Compile>
    <Compile Include="ScaleParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ScaleParam.Designer.cs">
      <DependentUpon>ScaleParam.cs</DependentUpon>
    </Compile>
    <Compile Include="TagParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TagParam.Designer.cs">
      <DependentUpon>TagParam.cs</DependentUpon>
    </Compile>
    <Compile Include="DriverSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DriverSet.Designer.cs">
      <DependentUpon>DriverSet.cs</DependentUpon>
    </Compile>
    <Compile Include="GroupParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="GroupParam.Designer.cs">
      <DependentUpon>GroupParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TagData.cs" />
    <EmbeddedResource Include="AlarmForm.resx">
      <DependentUpon>AlarmForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="AlarmParam.resx">
      <DependentUpon>AlarmParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RegisterSet.resx">
      <DependentUpon>RegisterSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ScaleParam.resx">
      <DependentUpon>ScaleParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TagParam.resx">
      <DependentUpon>TagParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DriverSet.resx">
      <DependentUpon>DriverSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="GroupParam.resx">
      <DependentUpon>GroupParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Exit.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Find.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PPrint.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PSave.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Timer.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Tool.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Attribute.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Collect.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Copy.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Delete.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Excel.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Help.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Clear.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Database.ico" />
    <None Include="Resources\Link.ico" />
    <None Include="Resources\Page-Setup.ico" />
    <None Include="Resources\Tip.ico" />
    <None Include="Resources\Database1.ico" />
    <None Include="Resources\Link1.ico" />
    <None Include="Resources\Tip1.ico" />
    <None Include="Resources\Page-Setup1.ico" />
    <Content Include="Wizard.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DataHelper\DataHelper.csproj">
      <Project>{755c5459-bca4-4729-a93c-0c73a41bdf3c}</Project>
      <Name>DataHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>