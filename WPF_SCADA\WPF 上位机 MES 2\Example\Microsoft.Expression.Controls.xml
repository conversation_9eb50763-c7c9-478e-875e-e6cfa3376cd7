<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Expression.Controls</name>
    </assembly>
    <members>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.Initialize">
            <summary>
            Called before the polylines are looped over.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.ComputeAutoCapacity">
            <summary>
            Computes the value used when LayoutPath.Capacity is set to Auto.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.ShouldBreak(System.Int32)">
            <summary>
            Checks whether or not the polylines should continue being looped over.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.OnPolylineBegin(Microsoft.Expression.Drawing.Core.PolylineData)">
            <summary>
            Called before a new polyline is marched.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.OnPolylineCompleted(System.Double)">
            <summary>
            Called when a polyline is completed.
            </summary>
            <param name="remaingLength">The remaining arclength in the polyline.</param>
        </member>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.OnStepCompleted(System.Double)">
            <summary>
            Called when a step is successfully completed.
            </summary>
            <param name="actualStepDistance">The actual step distance.
            This may be different than Step if another polyline has been wrapped to.</param>
        </member>
        <member name="M:Microsoft.Expression.Controls.DistributionStrategy.Distribute(Microsoft.Expression.Controls.PathPanel,System.Int32,System.Int32)">
            <summary>
            Distributes the children of a path panel along a given layout path.
            </summary>
            <param name="pathPanel">The path panel.</param>
            <param name="pathIndex">The index of the path in pathPanel.LayoutPaths.</param>
            <param name="childIndex">The index of the first child to be laid out on this path.</param>
            <returns>The index of the next child to be distributed. If childIndex is returned, this function has done nothing.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.DistributionStrategy.ChildIndex">
            <summary>
            The index of the next child to be arranged.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.DistributionStrategy.Span">
            <summary>
            The distance in arc length that will be used for layout.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.DistributionStrategy.Step">
            <summary>
            The distance in arc length to walk along the current polyline.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.DistributionStrategy.Capacity">
            <summary>
            The total number of elements to be arranged on the LayoutPath.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.EvenDistributionStrategy">
            <summary>
            Distributes the children of a path panel along a layout path.
            Even distribution means that there is equal arclength between the centers of all
            the elements on the path.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.ChangedPathLayoutProperties">
            <summary>
            Specifies which path layout properties have changed.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PathLayoutUpdatedEventArgs">
            <summary>
            Provides data for the <see cref="E:Microsoft.Expression.Controls.IPathLayoutItem.PathLayoutUpdated"/> event.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathLayoutUpdatedEventArgs.#ctor(Microsoft.Expression.Controls.ChangedPathLayoutProperties)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Expression.Controls.PathLayoutUpdatedEventArgs"/> class.
            </summary>
            <param name="changedProperties">The data for the event.</param>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutUpdatedEventArgs.ChangedProperties">
            <summary>
            Gets the properties changed in a <see cref="E:Microsoft.Expression.Controls.IPathLayoutItem.PathLayoutUpdated"/> event.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PathLayoutData">
            <summary>
            Encapsulates the data needed to update a <see cref="T:Microsoft.Expression.Controls.IPathLayoutItem"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.LayoutPathIndex">
            <summary>
            Gets or sets the index of the path on which an element is arranged.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.GlobalIndex">
            <summary>
            Gets or sets the index of an element in a collection of elements.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.LocalIndex">
            <summary>
            Gets or sets the index of an element on a path.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.GlobalOffset">
            <summary>
            Gets or sets the offset of an element proportional to the total length of a collection of paths.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.LocalOffset">
            <summary>
            Gets or sets the offset of an element proportional to the length of a path.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.NormalAngle">
            <summary>
            Gets or sets the angle perpendicular to the path at an element's location.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.OrientationAngle">
            <summary>
            Gets or sets the angle perpendicular to the path at an element's location if the element is oriented to the path.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathLayoutData.IsArranged">
            <summary>
            Gets or sets whether or not an element is arranged on a path.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.IPathLayoutItem">
            <summary>
            Represents an element that is arranged by a <see cref="T:Microsoft.Expression.Controls.PathPanel"/>.
            </summary>
            <remarks>
            Implementing this interface enables <see cref="T:Microsoft.Expression.Controls.PathPanel"/> to set these properties when the element is arranged.
            This interface is not intended for general implementation.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Controls.IPathLayoutItem.Update(Microsoft.Expression.Controls.PathLayoutData)">
            <summary>
            Updates the <see cref="T:Microsoft.Expression.Controls.IPathLayoutItem"/> properties based on the given <see cref="T:Microsoft.Expression.Controls.PathLayoutData"/> and raises the <see cref="E:Microsoft.Expression.Controls.IPathLayoutItem.PathLayoutUpdated"/> event.
            </summary>
            <param name="data">The data needed to update the <see cref="T:Microsoft.Expression.Controls.IPathLayoutItem"/>.</param>
        </member>
        <member name="E:Microsoft.Expression.Controls.IPathLayoutItem.PathLayoutUpdated">
            <summary>
            Occurs when one or more of the <see cref="T:Microsoft.Expression.Controls.IPathLayoutItem"/> properties change.
            </summary>
            <remarks>
            This event is raised whenever the item’s position along the path is updated. This can occur due to changes in the properties on the <see cref="T:Microsoft.Expression.Controls.LayoutPath"/> or changes to <see cref="P:Microsoft.Expression.Controls.LayoutPath.SourceElement"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.LayoutPathIndex">
            <summary>
            Gets the index of the path on which the item is arranged.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.GlobalIndex">
            <summary>
            Gets the index of the item in a collection of items.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.LocalIndex">
            <summary>
            Gets the index of the item on the path on which it is arranged.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.GlobalOffset">
            <summary>
            Gets the offset of the item proportional to the total length of a collection of paths.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.LocalOffset">
            <summary>
            Gets the offset of the item proportional to the length of the path on which it is arranged.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.NormalAngle">
            <summary>
            Gets the angle perpendicular to the path at the item's location.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.OrientationAngle">
            <summary>
            Gets the angle perpendicular to the path at the items's location if the item is oriented to the path.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.IPathLayoutItem.IsArranged">
            <summary>
            Gets or sets whether or not the item is arranged on the path.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.IsArrangedToScaleConverter">
            <summary>
            Converts a <see cref="T:System.Boolean"/> representing whether or not an element is arranged to a <see cref="T:System.Double"/> value used to scale the element.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.IsArrangedToScaleConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Throws a <see cref="T:System.NotImplementedException" /> exception in all cases.
            </summary>
            <param name="value">The target data being passed to the source.</param>
            <param name="targetType">The <see cref="T:System.Type" /> of data expected by the source object.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>The value to be passed to the source object.</returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.IsArrangedToScaleConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts the specified <see cref="T:System.Boolean"/> to a <see cref="T:System.Double"/>. 
            </summary>		
            <param name="value">The source data being passed to the target.</param>
            <param name="targetType">The <see cref="T:System.Type"/> of data expected by the target dependency property.</param>
            <param name="parameter">An optional parameter to be used in the converter logic.</param>
            <param name="culture">The culture of the conversion.</param>
            <returns>If the <see cref="T:System.Boolean"/> is true, 1 is returned. Else, 0 is returned.</returns>
        </member>
        <member name="T:Microsoft.Expression.Controls.Distribution">
            <summary>
            Specifies the distribution of a <see cref="T:Microsoft.Expression.Controls.LayoutPath"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.Orientation">
            <summary>
            Specifies the orientation of elements on a <see cref="T:Microsoft.Expression.Controls.LayoutPath"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.FillBehavior">
            <summary>
            Specifies the method for altering the <see cref="P:Microsoft.Expression.Controls.LayoutPath.Span"/> on an open path.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.LayoutPath">
            <summary>
            Describes a path along which elements are arranged.
            </summary>
        </member>
        <member name="F:Microsoft.Expression.Controls.LayoutPath.transformedPolylines">
            <summary>
            Ideally, we want to cache the Bezier curves before flattening, because transform will affect flattening.
            That requires us to cache curves that we don't support very well yet.  Instead, we cache the flatten points
            in distribution strategy and transform them on demand.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.SourceElement">
            <summary>
            Gets or sets the element that defines the path.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.Distribution">
            <summary>
            Gets or sets whether items are laid out evenly or with a fixed padding.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.Capacity">
            <summary>
            Gets or sets the maximum number of items displayed on the <see cref="T:Microsoft.Expression.Controls.LayoutPath"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.Padding">
            <summary>
            Gets or sets the distance in pixels along the arc length of the <see cref="T:Microsoft.Expression.Controls.LayoutPath"/> between two items.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.Orientation">
            <summary>
            Gets or sets the orientation of the items.
            </summary>
            <remarks>
            This property is supported on <see cref="T:Microsoft.Expression.Controls.PathListBox"/>, but not <see cref="T:Microsoft.Expression.Controls.PathPanel"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.Start">
            <summary>
            Gets or sets the distance from the start of the <see cref="T:Microsoft.Expression.Controls.LayoutPath"/> to place the first item.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.Span">
            <summary>
            Gets or sets the percentage of the <see cref="T:Microsoft.Expression.Controls.LayoutPath"/> that participates in layout.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.FillBehavior">
            <summary>
            Gets or set the method to alter <see cref="P:Microsoft.Expression.Controls.LayoutPath.Span"/> on an open path.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.ActualCapacity">
            <summary>
            Gets the computed Capacity.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.LayoutPath.IsValid">
            <summary>
            Gets whether or not the <see cref="P:Microsoft.Expression.Controls.LayoutPath.SourceElement"/> is valid in the context.
            A <see cref="P:Microsoft.Expression.Controls.LayoutPath.SourceElement"/> is not valid if it is a descendant of the <see cref="T:Microsoft.Expression.Controls.PathPanel"/> in which it is used.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PaddedDistributionStrategy">
            <summary>
            Distributes the children of a layout path based on the size of the children and a given padding between the children.
            The distance in arclength between children is determined by the padding plus the sum of the radii of the children's bounding boxes.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PathListBox">
            <summary>
            Contains a list of items that are laid out along one or more paths.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathListBox.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Expression.Controls.PathListBox"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathListBox.GetContainerForItemOverride">
            <summary>
            Creates the element used to display a specified item.
            </summary>
            <returns>A <see cref="T:Microsoft.Expression.Controls.PathListBoxItem"/> corresponding to a specified item.</returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathListBox.IsItemItsOwnContainerOverride(System.Object)">
            <summary>
            Determines if the specified item is (or is eligible to be) its own item container.
            </summary>
            <returns>true if the item is its own item container; otherwise, false.</returns>
            <param name="item">The specified item.</param>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathListBox.ArrangeOverride(System.Windows.Size)">
            <summary>
            Positions child elements.
            </summary>
            <param name="finalSize">The size that this object should use to arrange its child elements.</param>
            <returns>The actual size used.</returns>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBox.LayoutPaths">
            <summary>
            Gets or sets a collection of objects that define the path(s) used for layout.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBox.StartItemIndex">
            <summary>
            Gets or sets the index to use within the list of items.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBox.WrapItems">
            <summary>
            Gets or sets whether the item index wraps to the beginning of the collection when StartItemIndex is greater than 0.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PathListBoxItem">
            <summary>
            Represents an item in a <see cref="T:Microsoft.Expression.Controls.PathListBox"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathListBoxItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Expression.Controls.PathListBoxItem"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathListBoxItem.Update(Microsoft.Expression.Controls.PathLayoutData)">
            <summary>
            For a description of this member, see <see cref="M:Microsoft.Expression.Controls.IPathLayoutItem.Update(Microsoft.Expression.Controls.PathLayoutData)"/>.
            </summary>
        </member>
        <member name="E:Microsoft.Expression.Controls.PathListBoxItem.PathLayoutUpdated">
            <summary>
            For a description of this member, see <see cref="E:Microsoft.Expression.Controls.IPathLayoutItem.PathLayoutUpdated"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.LayoutPathIndex">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.LayoutPathIndex"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.GlobalIndex">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.GlobalIndex"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.LocalIndex">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.LocalIndex"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.GlobalOffset">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.GlobalOffset"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.LocalOffset">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.LocalOffset"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.NormalAngle">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.NormalAngle"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.OrientationAngle">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.OrientationAngle"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathListBoxItem.IsArranged">
            <summary>
            For a description of this member, see <see cref="P:Microsoft.Expression.Controls.IPathLayoutItem.IsArranged"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Expression.Controls.PathPanel">
            <summary>
            Arranges child elements along one or more path.
            </summary>
            <remarks>
            <see cref="T:Microsoft.Expression.Controls.PathPanel"/> can be used as an alternative to <see cref="T:Microsoft.Expression.Controls.PathListBox"/> when selection is not necessary.
            The <see cref="P:Microsoft.Expression.Controls.LayoutPath.Orientation"/> property is not supported on <see cref="T:Microsoft.Expression.Controls.PathPanel"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathPanel.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Expression.Controls.PathPanel"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathPanel.MeasureOverride(System.Windows.Size)">
            <summary>
            Measures the size required for child elements.
            </summary>
            <param name="availableSize">The available size that this element can give to child elements. </param>
            <returns>The size that this element determines it needs during layout, based on its calculations of child element sizes.</returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathPanel.ArrangeOverride(System.Windows.Size)">
            <summary>
            Positions child elements.
            </summary>
            <param name="finalSize">The size that this object should use to arrange its child elements.</param>
            <returns>The actual size used.</returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathPanel.ArrangeChild(System.Int32,System.Int32,Microsoft.Expression.Drawing.Core.PolylineData,Microsoft.Expression.Drawing.Core.MarchLocation,System.Int32)">
            <summary>
            Arranges the child at the given index at a particular location on a LayoutPath.
            </summary>
            <param name="indirectIndex">An index from the range 0 to PathPanel.Count.</param>
            <param name="pathIndex">The index of the layout path on which to arrange the child.</param>
            <param name="polyline">The polyline in the layout path.</param>
            <param name="location">The location on the polyline.</param>
            <param name="localIndex">The index of the child on the LayoutPath.</param>
        </member>
        <member name="M:Microsoft.Expression.Controls.PathPanel.GetChildRadius(System.Int32)">
            <summary>
            Calculates the radius of the bounding circle for the child at the given index.
            </summary>
            <param name="indirectIndex">An index from the range 0 to PathPanel.Count.</param>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathPanel.LayoutPaths">
            <summary>
            Gets or sets a collection of objects that define the path(s) used for layout.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathPanel.StartItemIndex">
            <summary>
            Gets or sets the index to use within the list of items.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathPanel.WrapItems">
            <summary>
            Gets or sets whether the item index wraps to the beginning of the collection when <see cref="P:Microsoft.Expression.Controls.PathPanel.StartItemIndex"/> is greater than 0.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathPanel.ValidPaths">
            <summary>
            The subset of LayoutPaths that are attached and not collapsed.
            </summary>
        </member>
        <member name="P:Microsoft.Expression.Controls.PathPanel.Count">
            <summary>
            Count is the number of Children that the PathPanel will lay out.
            If StartItemIndex > 0 and WrapItems is false, then this can be less than PathPanel.Children.Count
            </summary>
        </member>
        <member name="M:Microsoft.Expression.Controls.Utilities.GetItemsHost(System.Windows.Controls.ItemsControl)">
            <summary>
            Gets the panel that contains the containers of an ItemsControl.
            </summary>
            <param name="control">The ItemsControl.</param>
            <returns>
            The panel that contains the containers of an ItemsControl, or null
            if the panel could not be found.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="control" /> is null.
            </exception>
        </member>
        <member name="M:Microsoft.Expression.Controls.Utilities.TraverseBreadthFirst``1(``0,System.Func{``0,System.Collections.Generic.IEnumerable{``0}},System.Func{``0,System.Boolean})">
            <summary>
            Traverses a tree by accepting an initial value and a function that 
            retrieves the child nodes of a node.
            </summary>
            <typeparam name="T">The type of the stream.</typeparam>
            <param name="initialNode">The initial node.</param>
            <param name="getChildNodes">A function that retrieves the child
            nodes of a node.</param>
            <param name="traversePredicate">A predicate that evaluates a node
            and returns a value indicating whether that node and it's children
            should be traversed.</param>
            <returns>A stream of nodes.</returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.Utilities.GetVisualChildren(System.Windows.DependencyObject)">
            <summary>
            Get the visual tree children of an element.
            </summary>
            <param name="element">The element.</param>
            <returns>The visual tree children of an element.</returns>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="element"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.Expression.Controls.Utilities.GetVisualChildrenAndSelfIterator(System.Windows.DependencyObject)">
            <summary>
            Get the visual tree children of an element and the element itself.
            </summary>
            <param name="element">The element.</param>
            <returns>
            The visual tree children of an element and the element itself.
            </returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.Utilities.GetLogicalChildren(System.Windows.FrameworkElement)">
            <summary>
            Retrieves all the logical children of a framework element using a 
            breadth-first search. For performance reasons this method manually 
            manages the stack instead of using recursion.
            </summary>
            <param name="parent">The parent framework element.</param>
            <returns>The logical children of the framework element.</returns>
        </member>
        <member name="M:Microsoft.Expression.Controls.Utilities.GetLogicalDescendents(System.Windows.FrameworkElement)">
            <summary>
            Retrieves all the logical descendents of a framework element using a 
            breadth-first search. For performance reasons this method manually 
            manages the stack instead of using recursion.
            </summary>
            <param name="parent">The parent framework element.</param>
            <returns>The logical children of the framework element.</returns>
        </member>
    </members>
</doc>
