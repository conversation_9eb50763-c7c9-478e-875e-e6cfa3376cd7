using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using System.IO;
using System.Configuration;
using Lite3DogMES.UI;
using Lite3DogMES.Configuration;
using Lite3DogMES.Tests;

namespace Lite3DogMES
{
    /// <summary>
    /// 应用程序主入口点
    /// </summary>
    public class Program
    {
        private static SystemMonitorService _monitorService;
        private static bool _isTestMode = false;

        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                // 检查命令行参数
                ParseCommandLineArguments(args);

                // 初始化应用程序
                InitializeApplication();

                if (_isTestMode)
                {
                    // 运行测试模式
                    RunTestMode().Wait();
                }
                else
                {
                    // 运行正常模式
                    RunNormalMode();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 解析命令行参数
        /// </summary>
        private static void ParseCommandLineArguments(string[] args)
        {
            foreach (var arg in args)
            {
                switch (arg.ToLower())
                {
                    case "/test":
                    case "-test":
                        _isTestMode = true;
                        break;
                    case "/help":
                    case "-help":
                    case "/?":
                        ShowHelp();
                        Environment.Exit(0);
                        break;
                }
            }
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private static void ShowHelp()
        {
            var helpText = @"
云深处科技 Lite3小狗 MES生产管理系统

用法: Lite3DogMES.exe [选项]

选项:
  /test, -test     运行系统测试模式
  /help, -help, /? 显示此帮助信息

示例:
  Lite3DogMES.exe          # 正常启动应用程序
  Lite3DogMES.exe /test    # 运行系统测试

版本: 1.0.0
公司: 云深处科技
";
            Console.WriteLine(helpText);
        }

        /// <summary>
        /// 初始化应用程序
        /// </summary>
        private static void InitializeApplication()
        {
            // 创建必要的目录
            CreateDirectories();

            // 初始化日志系统
            InitializeLogging();

            // 验证数据库连接
            ValidateDatabaseConnection();

            // 初始化系统监控
            InitializeSystemMonitoring();

            Console.WriteLine("应用程序初始化完成");
        }

        /// <summary>
        /// 创建必要的目录
        /// </summary>
        private static void CreateDirectories()
        {
            var directories = new[]
            {
                "Logs",
                "Reports", 
                "Backup",
                "Images",
                "Temp"
            };

            foreach (var dir in directories)
            {
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
            }
        }

        /// <summary>
        /// 初始化日志系统
        /// </summary>
        private static void InitializeLogging()
        {
            var logPath = ConfigurationManager.AppSettings["LogPath"] ?? "Logs";
            var logFile = Path.Combine(logPath, $"MES_{DateTime.Now:yyyyMMdd}.log");
            
            // 配置日志监听器
            System.Diagnostics.Trace.Listeners.Clear();
            System.Diagnostics.Trace.Listeners.Add(new System.Diagnostics.TextWriterTraceListener(logFile));
            System.Diagnostics.Trace.AutoFlush = true;
            
            System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] 应用程序启动");
        }

        /// <summary>
        /// 验证数据库连接
        /// </summary>
        private static void ValidateDatabaseConnection()
        {
            try
            {
                var connectionString = ConfigurationManager.ConnectionStrings["Lite3DogMES"]?.ConnectionString;
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new Exception("未找到数据库连接字符串");
                }

                using (var connection = new System.Data.SqlClient.SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    // 检查关键表是否存在
                    var checkTableSql = @"
                        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_NAME IN ('Products', 'ProcessDefinitions', 'ProcessRecords', 'QualityTests')";
                    
                    using (var command = new System.Data.SqlClient.SqlCommand(checkTableSql, connection))
                    {
                        var tableCount = Convert.ToInt32(command.ExecuteScalar());
                        if (tableCount < 4)
                        {
                            throw new Exception("数据库表结构不完整，请运行数据库初始化脚本");
                        }
                    }
                }

                Console.WriteLine("数据库连接验证成功");
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库连接验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化系统监控
        /// </summary>
        private static void InitializeSystemMonitoring()
        {
            var monitoringEnabled = bool.Parse(ConfigurationManager.AppSettings["MonitoringEnabled"] ?? "true");
            if (monitoringEnabled)
            {
                var connectionString = ConfigurationManager.ConnectionStrings["Lite3DogMES"].ConnectionString;
                _monitorService = new SystemMonitorService(connectionString);
                
                _monitorService.HealthStatusChanged += OnHealthStatusChanged;
                _monitorService.SystemAlert += OnSystemAlert;
                
                _monitorService.StartMonitoring();
                Console.WriteLine("系统监控服务已启动");
            }
        }

        /// <summary>
        /// 运行正常模式
        /// </summary>
        private static void RunNormalMode()
        {
            var app = new Application();
            
            // 设置全局异常处理
            app.DispatcherUnhandledException += OnDispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            
            // 创建并显示主窗口
            var mainWindow = new MainWindow();
            app.MainWindow = mainWindow;
            
            Console.WriteLine("启动用户界面");
            app.Run(mainWindow);
        }

        /// <summary>
        /// 运行测试模式
        /// </summary>
        private static async Task RunTestMode()
        {
            Console.WriteLine("=== 云深处科技 Lite3小狗 MES系统测试 ===");
            Console.WriteLine();

            var testSuite = new SystemTestSuite();
            
            try
            {
                Console.WriteLine("开始执行系统测试...");
                var testResult = await testSuite.RunFullSystemTest();
                
                // 显示测试结果
                Console.WriteLine();
                Console.WriteLine("=== 测试结果 ===");
                Console.WriteLine($"测试名称: {testResult.TestName}");
                Console.WriteLine($"开始时间: {testResult.StartTime:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"结束时间: {testResult.EndTime:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"总执行时间: {testResult.TotalExecutionTime.TotalSeconds:F2} 秒");
                Console.WriteLine($"总测试数: {testResult.TotalTests}");
                Console.WriteLine($"通过测试: {testResult.PassedTests}");
                Console.WriteLine($"失败测试: {testResult.FailedTests}");
                Console.WriteLine($"成功率: {testResult.SuccessRate:F1}%");
                Console.WriteLine($"整体结果: {(testResult.OverallSuccess ? "成功" : "失败")}");
                Console.WriteLine();

                // 显示详细测试结果
                Console.WriteLine("=== 详细测试结果 ===");
                foreach (var testCase in testResult.TestCases)
                {
                    var status = testCase.Passed ? "✓" : "✗";
                    Console.WriteLine($"{status} {testCase.TestName} ({testCase.ExecutionTime.TotalMilliseconds:F0}ms)");
                    
                    if (testCase.Passed)
                    {
                        if (!string.IsNullOrEmpty(testCase.Message))
                        {
                            Console.WriteLine($"    {testCase.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"    错误: {testCase.ErrorMessage}");
                    }
                }

                // 清理测试数据
                Console.WriteLine();
                Console.WriteLine("清理测试数据...");
                await testSuite.CleanupTestData();
                
                Console.WriteLine("测试完成");
                Environment.Exit(testResult.OverallSuccess ? 0 : 1);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试执行失败: {ex.Message}");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 健康状态变化事件处理
        /// </summary>
        private static void OnHealthStatusChanged(object sender, SystemHealthStatus e)
        {
            System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] 系统健康状态: {e.OverallHealth}");
            
            if (e.OverallHealth == HealthLevel.Critical)
            {
                // 可以在这里添加紧急处理逻辑
                System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] 系统健康状态严重，需要立即处理");
            }
        }

        /// <summary>
        /// 系统告警事件处理
        /// </summary>
        private static void OnSystemAlert(object sender, string e)
        {
            System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] 系统告警: {e}");
            Console.WriteLine($"系统告警: {e}");
        }

        /// <summary>
        /// WPF调度器未处理异常
        /// </summary>
        private static void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            var errorMessage = $"应用程序异常: {e.Exception.Message}";
            System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] {errorMessage}");
            
            MessageBox.Show(errorMessage, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }

        /// <summary>
        /// 应用程序域未处理异常
        /// </summary>
        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            var errorMessage = $"未处理异常: {exception?.Message ?? "未知错误"}";
            System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] {errorMessage}");
            
            if (e.IsTerminating)
            {
                System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] 应用程序即将终止");
            }
        }

        /// <summary>
        /// 应用程序退出清理
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                _monitorService?.StopMonitoring();
                _monitorService?.Dispose();
                
                System.Diagnostics.Trace.WriteLine($"[{DateTime.Now}] 应用程序正常退出");
                System.Diagnostics.Trace.Flush();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理资源时发生错误: {ex.Message}");
            }
        }
    }
}
