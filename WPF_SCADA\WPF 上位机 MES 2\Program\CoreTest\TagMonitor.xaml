﻿<Window x:Class="CoreTest.TagMonitor"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:CoreTest"
        xmlns:extToolkit="http://schemas.microsoft.com/winfx/2006/xaml/presentation/toolkit/extended"
        FontFamily="SimSun" TextOptions.TextFormattingMode="Display"
        Title="变量监测" Height="700" Width="900" Loaded="Window_Loaded" Closed="Window_Closed">
    <Window.Resources>
        <ContextMenu x:Key="sampleContextMenu" >
            <MenuItem Header="写入" Command="{x:Static local:MyCommands.Edit}" CommandParameter="0"/>
            <MenuItem Header="仿真写入" Command="{x:Static local:MyCommands.Edit}" CommandParameter="1"/>
            <Separator />
        </ContextMenu>
    </Window.Resources>
    <Grid>
        <DataGrid  Name="list1" GridLinesVisibility="All" IsReadOnly="True"  AutoGenerateColumns="False" RowBackground="LightYellow" ContextMenu="{StaticResource sampleContextMenu}">
            <DataGrid.Columns>
                <DataGridTextColumn Width="350" Header="变量名" Binding="{Binding Path=TagName}"/>
                <DataGridTextColumn Width="200" Header="地址" Binding="{Binding Path=Address}"/>
                <DataGridTextColumn Width="150" Header="变量值" Binding="{Binding Path=TagValue}"/>
                <DataGridTextColumn Width="150" Header="时间戳" Binding="{Binding Path=TimeStamp}"/>
            </DataGrid.Columns>
        </DataGrid>
        <extToolkit:ChildWindow Grid.Row="2" Height="86" WindowStartupLocation="Center" Name="childWindow1" Width="226" IsModal="True" Caption="写入值">
            <Canvas>
                <TextBox Height="30" Name="textBox1" Width="122" FontSize="20" FontWeight="Bold" Canvas.Left="16" Canvas.Top="9" />
                <Button Width="60" Height="30" Content="确定" Canvas.Left="144" Canvas.Top="9" Click="Button_Click" IsDefault="True"/>
            </Canvas>
        </extToolkit:ChildWindow>
    </Grid>
</Window>

