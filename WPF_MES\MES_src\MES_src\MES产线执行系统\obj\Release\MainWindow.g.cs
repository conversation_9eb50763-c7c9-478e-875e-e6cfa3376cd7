﻿#pragma checksum "..\..\MainWindow.xaml" "{406ea660-64cf-4c82-b6f0-42d48172a799}" "E47082C475D0FB86E72D91AC91E92C20"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.17929
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using MMIS;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MMIS {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 8 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Root;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid UserInfo;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid backgroundGrid;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path Stroke;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtUser;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtDesignation;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Logo;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Path circle;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel Title1;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMin;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem TableItem1;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_AGV_Start_Close;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cb_OrderStyle;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cb_OrderNum;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Booking;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Execute_order;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_State;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_State;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A1_State;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A2_State;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Hand_Up_TrayStyle;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Hand_Down_TrayStyle;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Hand_Up_Tray;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Hand_Down_Tray;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Tray_parameter;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_WH_Start_Close;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Reset;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem TableItem2;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_AGV_com_state;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_AGV_pos_state;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_W_com_state;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_W_oper_state;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A_com_state;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A_sys_state;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A_Area1_state;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A_Area2_state;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A_Robot_state;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_A_Cor_state;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_com_state;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_sys_state;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_Area1_state;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_Area2_state;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_Marking_state;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_D_Robot_state;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_com_state;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_sys_state;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Manualup_Area;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Manualdown_Area;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Area1;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Area2;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Mazak1_State;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Mazak2_State;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Big_State;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_P_Robot_State;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Other_info;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem HistoryRecord;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker1;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EndDatePicker2;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Search;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_export_data;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cb_BookingStyle;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cb_SearchStyle;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dg_Order;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dg_detection;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem KuweiShow;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A0;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A1;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A2;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A3;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A4;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A5;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_A6;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_B0;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_B1;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_B2;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_C;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_D;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Tary_Empty;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_kuwei_mannger;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem HandOperate;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Hand_Action_Style;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Hand_Position;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Hand_Tray_Style;
        
        #line default
        #line hidden
        
        
        #line 419 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Hand_WH_OK;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Hand_WH_Execute_State;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Hand_Map_Start;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox Hand_Map_End;
        
        #line default
        #line hidden
        
        
        #line 455 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_Hand_AGV_OK;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_Hand_AGV_Execute_State;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_WH_ES;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_AGV_ES;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_AGV_NES;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label lb_show_state;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MMIS;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 4 "..\..\MainWindow.xaml"
            ((MMIS.MainWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.Root = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.UserInfo = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.backgroundGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.Stroke = ((System.Windows.Shapes.Path)(target));
            return;
            case 6:
            this.txtUser = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.txtDesignation = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.Logo = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.circle = ((System.Windows.Shapes.Path)(target));
            return;
            case 10:
            this.Title1 = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\MainWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.btnClose_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnMin = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\MainWindow.xaml"
            this.btnMin.Click += new System.Windows.RoutedEventHandler(this.btnMin_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TableItem1 = ((System.Windows.Controls.TabItem)(target));
            return;
            case 14:
            this.btn_AGV_Start_Close = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\MainWindow.xaml"
            this.btn_AGV_Start_Close.Click += new System.Windows.RoutedEventHandler(this.btn_AGV_Start_Close_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.cb_OrderStyle = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.cb_OrderNum = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.btn_Booking = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\MainWindow.xaml"
            this.btn_Booking.Click += new System.Windows.RoutedEventHandler(this.btn_Booking_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btn_Execute_order = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\MainWindow.xaml"
            this.btn_Execute_order.Click += new System.Windows.RoutedEventHandler(this.btn_Execute_order_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.lb_P_State = ((System.Windows.Controls.Label)(target));
            return;
            case 20:
            this.lb_D_State = ((System.Windows.Controls.Label)(target));
            return;
            case 21:
            this.lb_A1_State = ((System.Windows.Controls.Label)(target));
            return;
            case 22:
            this.lb_A2_State = ((System.Windows.Controls.Label)(target));
            return;
            case 23:
            this.Hand_Up_TrayStyle = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 24:
            this.Hand_Down_TrayStyle = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 25:
            this.btn_Hand_Up_Tray = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\MainWindow.xaml"
            this.btn_Hand_Up_Tray.Click += new System.Windows.RoutedEventHandler(this.btn_Hand_Up_Tray_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.btn_Hand_Down_Tray = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\MainWindow.xaml"
            this.btn_Hand_Down_Tray.Click += new System.Windows.RoutedEventHandler(this.btn_Hand_Down_Tray_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.Tray_parameter = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.btn_WH_Start_Close = ((System.Windows.Controls.Button)(target));
            
            #line 233 "..\..\MainWindow.xaml"
            this.btn_WH_Start_Close.Click += new System.Windows.RoutedEventHandler(this.btn_WH_Start_Close_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.btn_Reset = ((System.Windows.Controls.Button)(target));
            
            #line 234 "..\..\MainWindow.xaml"
            this.btn_Reset.Click += new System.Windows.RoutedEventHandler(this.btn_Reset_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.TableItem2 = ((System.Windows.Controls.TabItem)(target));
            return;
            case 31:
            this.lb_AGV_com_state = ((System.Windows.Controls.Label)(target));
            return;
            case 32:
            this.lb_AGV_pos_state = ((System.Windows.Controls.Label)(target));
            return;
            case 33:
            this.lb_W_com_state = ((System.Windows.Controls.Label)(target));
            return;
            case 34:
            this.lb_W_oper_state = ((System.Windows.Controls.Label)(target));
            return;
            case 35:
            this.lb_A_com_state = ((System.Windows.Controls.Label)(target));
            return;
            case 36:
            this.lb_A_sys_state = ((System.Windows.Controls.Label)(target));
            return;
            case 37:
            this.lb_A_Area1_state = ((System.Windows.Controls.Label)(target));
            return;
            case 38:
            this.lb_A_Area2_state = ((System.Windows.Controls.Label)(target));
            return;
            case 39:
            this.lb_A_Robot_state = ((System.Windows.Controls.Label)(target));
            return;
            case 40:
            this.lb_A_Cor_state = ((System.Windows.Controls.Label)(target));
            return;
            case 41:
            this.lb_D_com_state = ((System.Windows.Controls.Label)(target));
            return;
            case 42:
            this.lb_D_sys_state = ((System.Windows.Controls.Label)(target));
            return;
            case 43:
            this.lb_D_Area1_state = ((System.Windows.Controls.Label)(target));
            return;
            case 44:
            this.lb_D_Area2_state = ((System.Windows.Controls.Label)(target));
            return;
            case 45:
            this.lb_D_Marking_state = ((System.Windows.Controls.Label)(target));
            return;
            case 46:
            this.lb_D_Robot_state = ((System.Windows.Controls.Label)(target));
            return;
            case 47:
            this.lb_P_com_state = ((System.Windows.Controls.Label)(target));
            return;
            case 48:
            this.lb_P_sys_state = ((System.Windows.Controls.Label)(target));
            return;
            case 49:
            this.lb_P_Manualup_Area = ((System.Windows.Controls.Label)(target));
            return;
            case 50:
            this.lb_P_Manualdown_Area = ((System.Windows.Controls.Label)(target));
            return;
            case 51:
            this.lb_P_Area1 = ((System.Windows.Controls.Label)(target));
            return;
            case 52:
            this.lb_P_Area2 = ((System.Windows.Controls.Label)(target));
            return;
            case 53:
            this.lb_P_Mazak1_State = ((System.Windows.Controls.Label)(target));
            return;
            case 54:
            this.lb_P_Mazak2_State = ((System.Windows.Controls.Label)(target));
            return;
            case 55:
            this.lb_P_Big_State = ((System.Windows.Controls.Label)(target));
            return;
            case 56:
            this.lb_P_Robot_State = ((System.Windows.Controls.Label)(target));
            return;
            case 57:
            this.lb_Other_info = ((System.Windows.Controls.Label)(target));
            return;
            case 58:
            this.HistoryRecord = ((System.Windows.Controls.TabItem)(target));
            return;
            case 59:
            this.StartDatePicker1 = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 60:
            this.EndDatePicker2 = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 61:
            this.btn_Search = ((System.Windows.Controls.Button)(target));
            
            #line 323 "..\..\MainWindow.xaml"
            this.btn_Search.Click += new System.Windows.RoutedEventHandler(this.btn_Search_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.btn_export_data = ((System.Windows.Controls.Button)(target));
            
            #line 324 "..\..\MainWindow.xaml"
            this.btn_export_data.Click += new System.Windows.RoutedEventHandler(this.btn_export_data_Click);
            
            #line default
            #line hidden
            return;
            case 63:
            this.cb_BookingStyle = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 64:
            this.cb_SearchStyle = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 65:
            this.dg_Order = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 66:
            this.dg_detection = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 67:
            this.KuweiShow = ((System.Windows.Controls.TabItem)(target));
            return;
            case 68:
            this.lb_Tary_A0 = ((System.Windows.Controls.Label)(target));
            return;
            case 69:
            this.lb_Tary_A1 = ((System.Windows.Controls.Label)(target));
            return;
            case 70:
            this.lb_Tary_A2 = ((System.Windows.Controls.Label)(target));
            return;
            case 71:
            this.lb_Tary_A3 = ((System.Windows.Controls.Label)(target));
            return;
            case 72:
            this.lb_Tary_A4 = ((System.Windows.Controls.Label)(target));
            return;
            case 73:
            this.lb_Tary_A5 = ((System.Windows.Controls.Label)(target));
            return;
            case 74:
            this.lb_Tary_A6 = ((System.Windows.Controls.Label)(target));
            return;
            case 75:
            this.lb_Tary_B0 = ((System.Windows.Controls.Label)(target));
            return;
            case 76:
            this.lb_Tary_B1 = ((System.Windows.Controls.Label)(target));
            return;
            case 77:
            this.lb_Tary_B2 = ((System.Windows.Controls.Label)(target));
            return;
            case 78:
            this.lb_Tary_C = ((System.Windows.Controls.Label)(target));
            return;
            case 79:
            this.lb_Tary_D = ((System.Windows.Controls.Label)(target));
            return;
            case 80:
            this.lb_Tary_Empty = ((System.Windows.Controls.Label)(target));
            return;
            case 81:
            this.btn_kuwei_mannger = ((System.Windows.Controls.Button)(target));
            
            #line 389 "..\..\MainWindow.xaml"
            this.btn_kuwei_mannger.Click += new System.Windows.RoutedEventHandler(this.btn_Kuwei_mannger_Click);
            
            #line default
            #line hidden
            return;
            case 82:
            this.HandOperate = ((System.Windows.Controls.TabItem)(target));
            return;
            case 83:
            this.Hand_Action_Style = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 84:
            this.Hand_Position = ((System.Windows.Controls.TextBox)(target));
            return;
            case 85:
            this.Hand_Tray_Style = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 86:
            this.btn_Hand_WH_OK = ((System.Windows.Controls.Button)(target));
            
            #line 419 "..\..\MainWindow.xaml"
            this.btn_Hand_WH_OK.Click += new System.Windows.RoutedEventHandler(this.btn_Hand_WH_OK_Click);
            
            #line default
            #line hidden
            return;
            case 87:
            this.lb_Hand_WH_Execute_State = ((System.Windows.Controls.Label)(target));
            return;
            case 88:
            this.Hand_Map_Start = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 89:
            this.Hand_Map_End = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 90:
            this.btn_Hand_AGV_OK = ((System.Windows.Controls.Button)(target));
            
            #line 455 "..\..\MainWindow.xaml"
            this.btn_Hand_AGV_OK.Click += new System.Windows.RoutedEventHandler(this.btn_Hand_AGV_OK_Click);
            
            #line default
            #line hidden
            return;
            case 91:
            this.lb_Hand_AGV_Execute_State = ((System.Windows.Controls.Label)(target));
            return;
            case 92:
            this.btn_WH_ES = ((System.Windows.Controls.Button)(target));
            
            #line 460 "..\..\MainWindow.xaml"
            this.btn_WH_ES.Click += new System.Windows.RoutedEventHandler(this.btn_WH_ES_Click);
            
            #line default
            #line hidden
            return;
            case 93:
            this.btn_AGV_ES = ((System.Windows.Controls.Button)(target));
            
            #line 461 "..\..\MainWindow.xaml"
            this.btn_AGV_ES.Click += new System.Windows.RoutedEventHandler(this.btn_AGV_ES_Click);
            
            #line default
            #line hidden
            return;
            case 94:
            this.btn_AGV_NES = ((System.Windows.Controls.Button)(target));
            
            #line 462 "..\..\MainWindow.xaml"
            this.btn_AGV_NES.Click += new System.Windows.RoutedEventHandler(this.btn_AGV_NES_Click);
            
            #line default
            #line hidden
            return;
            case 95:
            this.lb_show_state = ((System.Windows.Controls.Label)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

