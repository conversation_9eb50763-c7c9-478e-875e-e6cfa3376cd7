using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace Lite3DogMES.QRCodeManager
{
    /// <summary>
    /// 二维码追溯管理器
    /// </summary>
    public class QRCodeTraceability
    {
        private readonly string _connectionString;

        public QRCodeTraceability(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 记录二维码扫描
        /// </summary>
        /// <param name="scanRecord">扫描记录</param>
        /// <returns>是否成功</returns>
        public bool RecordScan(ScanRecord scanRecord)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        INSERT INTO ScanRecords 
                        (ProductID, ProcessID, ScanTime, ScanLocation, OperatorID, ScanResult, DeviceID, Remarks)
                        VALUES 
                        (@ProductID, @ProcessID, @ScanTime, @ScanLocation, @OperatorID, @ScanResult, @DeviceID, @Remarks)";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@ProductID", scanRecord.ProductID);
                        command.Parameters.AddWithValue("@ProcessID", (object)scanRecord.ProcessID ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ScanTime", scanRecord.ScanTime);
                        command.Parameters.AddWithValue("@ScanLocation", scanRecord.ScanLocation ?? "");
                        command.Parameters.AddWithValue("@OperatorID", scanRecord.OperatorID ?? "");
                        command.Parameters.AddWithValue("@ScanResult", scanRecord.ScanResult);
                        command.Parameters.AddWithValue("@DeviceID", scanRecord.DeviceID ?? "");
                        command.Parameters.AddWithValue("@Remarks", scanRecord.Remarks ?? "");

                        command.ExecuteNonQuery();
                    }

                    // 同时记录到追溯表
                    RecordTraceabilityEvent(connection, scanRecord.ProductID, "二维码扫描", 
                        $"在{scanRecord.ScanLocation}扫描二维码", scanRecord.ScanLocation, scanRecord.OperatorID);

                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"记录扫描失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据产品序列号获取产品信息
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <returns>产品信息</returns>
        public ProductInfo GetProductBySerialNumber(string productSN)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT ProductID, ProductSN, QRCode, ProductModel, ProductionDate, 
                               CurrentStatus, CurrentLocation, IsCompleted, CreatedTime
                        FROM Products 
                        WHERE ProductSN = @ProductSN";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@ProductSN", productSN);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new ProductInfo
                                {
                                    ProductID = reader["ProductID"].ToString(),
                                    ProductSN = reader["ProductSN"].ToString(),
                                    QRCode = reader["QRCode"].ToString(),
                                    ProductModel = reader["ProductModel"].ToString(),
                                    ProductionDate = Convert.ToDateTime(reader["ProductionDate"]),
                                    CurrentStatus = reader["CurrentStatus"].ToString(),
                                    CurrentLocation = reader["CurrentLocation"].ToString(),
                                    IsCompleted = Convert.ToBoolean(reader["IsCompleted"]),
                                    CreatedTime = Convert.ToDateTime(reader["CreatedTime"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取产品信息失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 获取产品完整追溯信息
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <returns>追溯信息列表</returns>
        public List<TraceabilityInfo> GetProductTraceability(string productSN)
        {
            var traceabilityList = new List<TraceabilityInfo>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT pt.EventType, pt.EventDescription, pt.EventTime, pt.Location, 
                               pt.OperatorID, u.RealName as OperatorName, pt.RelatedData
                        FROM ProductTraceability pt
                        INNER JOIN Products p ON pt.ProductID = p.ProductID
                        LEFT JOIN Users u ON pt.OperatorID = u.UserID
                        WHERE p.ProductSN = @ProductSN
                        ORDER BY pt.EventTime";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@ProductSN", productSN);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                traceabilityList.Add(new TraceabilityInfo
                                {
                                    EventType = reader["EventType"].ToString(),
                                    EventDescription = reader["EventDescription"].ToString(),
                                    EventTime = Convert.ToDateTime(reader["EventTime"]),
                                    Location = reader["Location"].ToString(),
                                    OperatorID = reader["OperatorID"].ToString(),
                                    OperatorName = reader["OperatorName"].ToString(),
                                    RelatedData = reader["RelatedData"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取追溯信息失败: {ex.Message}");
            }

            return traceabilityList;
        }

        /// <summary>
        /// 获取产品工序进度
        /// </summary>
        /// <param name="productSN">产品序列号</param>
        /// <returns>工序进度列表</returns>
        public List<ProcessProgress> GetProductProcessProgress(string productSN)
        {
            var progressList = new List<ProcessProgress>();

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        SELECT pd.ProcessName, pd.ProcessCode, pd.ProcessOrder, 
                               pr.StartTime, pr.EndTime, pr.Status, pr.QualityResult,
                               pr.OperatorID, u.RealName as OperatorName, pr.Location, pr.Remarks
                        FROM ProcessDefinitions pd
                        LEFT JOIN ProcessRecords pr ON pd.ProcessID = pr.ProcessID 
                            AND pr.ProductID = (SELECT ProductID FROM Products WHERE ProductSN = @ProductSN)
                        LEFT JOIN Users u ON pr.OperatorID = u.UserID
                        WHERE pd.IsActive = 1
                        ORDER BY pd.ProcessOrder";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@ProductSN", productSN);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                progressList.Add(new ProcessProgress
                                {
                                    ProcessName = reader["ProcessName"].ToString(),
                                    ProcessCode = reader["ProcessCode"].ToString(),
                                    ProcessOrder = Convert.ToInt32(reader["ProcessOrder"]),
                                    StartTime = reader["StartTime"] == DBNull.Value ? null : (DateTime?)reader["StartTime"],
                                    EndTime = reader["EndTime"] == DBNull.Value ? null : (DateTime?)reader["EndTime"],
                                    Status = reader["Status"].ToString(),
                                    QualityResult = reader["QualityResult"].ToString(),
                                    OperatorID = reader["OperatorID"].ToString(),
                                    OperatorName = reader["OperatorName"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    Remarks = reader["Remarks"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工序进度失败: {ex.Message}");
            }

            return progressList;
        }

        /// <summary>
        /// 验证二维码并获取产品信息
        /// </summary>
        /// <param name="qrContent">二维码内容</param>
        /// <returns>验证结果</returns>
        public QRCodeValidationResult ValidateQRCode(string qrContent)
        {
            var result = new QRCodeValidationResult();

            try
            {
                // 解析二维码内容获取产品序列号
                string productSN = ExtractProductSN(qrContent);
                
                if (string.IsNullOrEmpty(productSN))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "无法从二维码中解析产品序列号";
                    return result;
                }

                // 获取产品信息
                var productInfo = GetProductBySerialNumber(productSN);
                
                if (productInfo == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "产品不存在";
                    return result;
                }

                result.IsValid = true;
                result.ProductInfo = productInfo;
                result.ProcessProgress = GetProductProcessProgress(productSN);
                
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessage = $"验证失败: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 从二维码内容中提取产品序列号
        /// </summary>
        /// <param name="qrContent">二维码内容</param>
        /// <returns>产品序列号</returns>
        private string ExtractProductSN(string qrContent)
        {
            try
            {
                // 尝试解析JSON格式
                var productInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(qrContent);
                return productInfo?.ProductSN;
            }
            catch
            {
                // 如果不是JSON格式，尝试简单格式
                if (qrContent.StartsWith("LITE3_"))
                {
                    return qrContent.Substring(6);
                }
                
                return qrContent; // 直接返回作为序列号
            }
        }

        /// <summary>
        /// 记录追溯事件
        /// </summary>
        private void RecordTraceabilityEvent(SqlConnection connection, string productID, string eventType, 
            string eventDescription, string location, string operatorID)
        {
            var sql = @"
                INSERT INTO ProductTraceability 
                (ProductID, EventType, EventDescription, Location, OperatorID)
                VALUES 
                (@ProductID, @EventType, @EventDescription, @Location, @OperatorID)";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@ProductID", productID);
                command.Parameters.AddWithValue("@EventType", eventType);
                command.Parameters.AddWithValue("@EventDescription", eventDescription);
                command.Parameters.AddWithValue("@Location", location ?? "");
                command.Parameters.AddWithValue("@OperatorID", operatorID ?? "");

                command.ExecuteNonQuery();
            }
        }
    }

    // 数据模型类
    public class ScanRecord
    {
        public string ProductID { get; set; }
        public int? ProcessID { get; set; }
        public DateTime ScanTime { get; set; }
        public string ScanLocation { get; set; }
        public string OperatorID { get; set; }
        public string ScanResult { get; set; }
        public string DeviceID { get; set; }
        public string Remarks { get; set; }
    }

    public class ProductInfo
    {
        public string ProductID { get; set; }
        public string ProductSN { get; set; }
        public string QRCode { get; set; }
        public string ProductModel { get; set; }
        public DateTime ProductionDate { get; set; }
        public string CurrentStatus { get; set; }
        public string CurrentLocation { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime CreatedTime { get; set; }
    }

    public class TraceabilityInfo
    {
        public string EventType { get; set; }
        public string EventDescription { get; set; }
        public DateTime EventTime { get; set; }
        public string Location { get; set; }
        public string OperatorID { get; set; }
        public string OperatorName { get; set; }
        public string RelatedData { get; set; }
    }

    public class ProcessProgress
    {
        public string ProcessName { get; set; }
        public string ProcessCode { get; set; }
        public int ProcessOrder { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string Status { get; set; }
        public string QualityResult { get; set; }
        public string OperatorID { get; set; }
        public string OperatorName { get; set; }
        public string Location { get; set; }
        public string Remarks { get; set; }
    }

    public class QRCodeValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public ProductInfo ProductInfo { get; set; }
        public List<ProcessProgress> ProcessProgress { get; set; }
    }
}
