<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="2.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="2.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="2.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="4.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ClientDriver\ClientDriver.csproj" />
    <ProjectReference Include="..\DataHelper\DataHelper.csproj" />
    <ProjectReference Include="..\DataService\DataService.csproj" />
    <ProjectReference Include="..\ModbusDriver\ModbusDriver.csproj" />
  </ItemGroup>

</Project>
