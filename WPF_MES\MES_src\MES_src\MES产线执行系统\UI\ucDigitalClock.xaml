﻿<UserControl x:Class="MMIS.ucDigitalClock"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="100">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="4*" />
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>
        <TextBlock x:Name="txtTime" Text="10:10" Grid.RowSpan="2" VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0" Foreground="White" FontFamily="Agency FB" FontSize="48" />
        <TextBlock x:Name="txtTimeSecond" Grid.Column="1" Grid.Row="0" Text="10" VerticalAlignment="Bottom" HorizontalAlignment="Center" Margin="0" Foreground="White" FontFamily="Agency FB" FontSize="18" />
        <TextBlock x:Name="txtTimeAMPM" Grid.Column="1" Grid.Row="1" Text="AM" VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0" Foreground="White" FontFamily="Agency FB" FontSize="18" />
    </Grid>
</UserControl>
