using System;
using System.Linq;
using System.Windows;
using System.Windows.Forms.Integration;
using System.Windows.Forms;
using Lite3DogMES.QRCodeManager;

namespace Lite3DogMES.UI
{
    public partial class QRCodeScanWindow : Window
    {
        private readonly QRCodeScanner _qrScanner;
        private PictureBox _pictureBox;
        private bool _isScanning = false;

        public string ScannedProductSN { get; private set; }
        public DateTime ScanTime { get; private set; }

        public QRCodeScanWindow(QRCodeScanner qrScanner)
        {
            InitializeComponent();
            _qrScanner = qrScanner;
            InitializeCamera();
            SetupEventHandlers();
        }

        private void InitializeCamera()
        {
            try
            {
                // 创建 PictureBox 控件用于显示摄像头画面
                _pictureBox = new PictureBox
                {
                    Dock = DockStyle.Fill,
                    SizeMode = PictureBoxSizeMode.StretchImage,
                    BackColor = System.Drawing.Color.Black
                };

                // 创建 WindowsFormsHost 来承载 PictureBox
                var host = new WindowsFormsHost
                {
                    Child = _pictureBox
                };

                // 替换占位符
                var grid = (Grid)pictureBoxCamera.Parent;
                grid.Children.Remove(pictureBoxCamera);
                grid.Children.Remove(txtCameraPlaceholder);
                grid.Children.Add(host);

                // 获取可用摄像头列表
                var cameras = _qrScanner.GetAvailableCameras();
                cmbCameras.ItemsSource = cameras.Cast<object>().Select((camera, index) => 
                    new { Index = index, Name = $"摄像头 {index + 1}" });
                cmbCameras.DisplayMemberPath = "Name";
                cmbCameras.SelectedValuePath = "Index";

                if (cameras.Count > 0)
                {
                    cmbCameras.SelectedIndex = 0;
                }
                else
                {
                    txtScanStatus.Text = "未检测到摄像头设备";
                    btnStartCamera.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                txtScanStatus.Text = $"初始化摄像头失败: {ex.Message}";
                btnStartCamera.IsEnabled = false;
            }
        }

        private void SetupEventHandlers()
        {
            _qrScanner.QRCodeScanned += QrScanner_QRCodeScanned;
            _qrScanner.ScanError += QrScanner_ScanError;
        }

        private void QrScanner_QRCodeScanned(object sender, QRCodeScanResult e)
        {
            Dispatcher.Invoke(() =>
            {
                if (e.Success)
                {
                    ScannedProductSN = e.ProductSN ?? ExtractProductSN(e.Content);
                    ScanTime = e.ScanTime;

                    txtScannedSN.Text = ScannedProductSN;
                    txtScanTime.Text = ScanTime.ToString("yyyy-MM-dd HH:mm:ss");
                    txtScanStatus.Text = "扫描成功";
                    txtScanStatus.Foreground = System.Windows.Media.Brushes.LightGreen;

                    btnOK.IsEnabled = true;

                    // 如果启用了自动扫描，自动确认
                    if (chkAutoScan.IsChecked == true)
                    {
                        DialogResult = true;
                        Close();
                    }
                }
                else
                {
                    txtScanStatus.Text = $"扫描失败: {e.ErrorMessage}";
                    txtScanStatus.Foreground = System.Windows.Media.Brushes.Red;
                }
            });
        }

        private void QrScanner_ScanError(object sender, string e)
        {
            Dispatcher.Invoke(() =>
            {
                txtScanStatus.Text = $"扫描错误: {e}";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.Red;
            });
        }

        private void BtnStartCamera_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (cmbCameras.SelectedValue != null)
                {
                    int cameraIndex = (int)cmbCameras.SelectedValue;
                    _qrScanner.StartCameraScanning(cameraIndex, _pictureBox);
                    
                    _isScanning = true;
                    btnStartCamera.IsEnabled = false;
                    btnStopCamera.IsEnabled = true;
                    btnTriggerScan.IsEnabled = true;
                    
                    txtScanStatus.Text = "摄像头已启动，等待扫描...";
                    txtScanStatus.Foreground = System.Windows.Media.Brushes.White;
                }
            }
            catch (Exception ex)
            {
                txtScanStatus.Text = $"启动摄像头失败: {ex.Message}";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.Red;
            }
        }

        private void BtnStopCamera_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _qrScanner.StopCameraScanning();
                
                _isScanning = false;
                btnStartCamera.IsEnabled = true;
                btnStopCamera.IsEnabled = false;
                btnTriggerScan.IsEnabled = false;
                
                txtScanStatus.Text = "摄像头已停止";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.White;
            }
            catch (Exception ex)
            {
                txtScanStatus.Text = $"停止摄像头失败: {ex.Message}";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.Red;
            }
        }

        private void BtnTriggerScan_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_isScanning)
                {
                    _qrScanner.TriggerScan();
                    txtScanStatus.Text = "正在扫描...";
                    txtScanStatus.Foreground = System.Windows.Media.Brushes.Yellow;
                }
            }
            catch (Exception ex)
            {
                txtScanStatus.Text = $"扫描失败: {ex.Message}";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.Red;
            }
        }

        private void BtnManualConfirm_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtManualInput.Text))
            {
                ScannedProductSN = txtManualInput.Text.Trim();
                ScanTime = DateTime.Now;

                txtScannedSN.Text = ScannedProductSN;
                txtScanTime.Text = ScanTime.ToString("yyyy-MM-dd HH:mm:ss");
                txtScanStatus.Text = "手动输入确认";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.LightGreen;

                btnOK.IsEnabled = true;
            }
            else
            {
                txtScanStatus.Text = "请输入产品序号";
                txtScanStatus.Foreground = System.Windows.Media.Brushes.Orange;
            }
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(ScannedProductSN))
            {
                DialogResult = true;
                Close();
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private string ExtractProductSN(string qrContent)
        {
            try
            {
                // 尝试解析JSON格式的二维码内容
                var productInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(qrContent);
                return productInfo?.ProductSN;
            }
            catch
            {
                // 如果不是JSON格式，尝试简单格式
                if (qrContent.StartsWith("LITE3_"))
                {
                    return qrContent.Substring(6);
                }
                
                return qrContent; // 直接返回作为序列号
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                if (_isScanning)
                {
                    _qrScanner.StopCameraScanning();
                }
            }
            catch (Exception ex)
            {
                // 忽略关闭时的错误
                System.Diagnostics.Debug.WriteLine($"关闭扫描窗口时发生错误: {ex.Message}");
            }
            
            base.OnClosed(e);
        }
    }
}
