﻿#pragma checksum "..\..\..\SiloProductLine.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "EAA362BED11964081A0F6E89DB7F03345B286488B7513DB639D8AA2D2C6048C6"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using HMIControl;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CoreTest {
    
    
    /// <summary>
    /// SiloProductLine
    /// </summary>
    public partial class SiloProductLine : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 7 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal CoreTest.SiloProductLine SiloProductLine1;
        
        #line default
        #line hidden
        
        
        #line 9 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas cvs1;
        
        #line default
        #line hidden
        
        
        #line 10 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Silo silo1;
        
        #line default
        #line hidden
        
        
        #line 11 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Silo silo2;
        
        #line default
        #line hidden
        
        
        #line 12 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Elevator elevator1;
        
        #line default
        #line hidden
        
        
        #line 13 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Elevator elevator2;
        
        #line default
        #line hidden
        
        
        #line 14 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.PreCleaner preCleaner1;
        
        #line default
        #line hidden
        
        
        #line 15 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Bucket bucket1;
        
        #line default
        #line hidden
        
        
        #line 16 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Truck truck1;
        
        #line default
        #line hidden
        
        
        #line 17 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor1;
        
        #line default
        #line hidden
        
        
        #line 18 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.MagnetCleaner magnetCleaner1;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor2;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor3;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.ChainConveyor chainConveyor4;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Cyclone cyclone1;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock productline;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo12;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SelectSwitch selectSwitch1;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SelectSwitch selectSwitch3;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo1;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo2;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo3;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate1;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate2;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo4;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo5;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.SlideGate slideGate3;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.Cyclone cyclone2;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\SiloProductLine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HMIControl.FromTo fromTo6;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CoreTest;component/siloproductline.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SiloProductLine.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SiloProductLine1 = ((CoreTest.SiloProductLine)(target));
            
            #line 8 "..\..\..\SiloProductLine.xaml"
            this.SiloProductLine1.Loaded += new System.Windows.RoutedEventHandler(this.HMI_Loaded);
            
            #line default
            #line hidden
            
            #line 8 "..\..\..\SiloProductLine.xaml"
            this.SiloProductLine1.Unloaded += new System.Windows.RoutedEventHandler(this.HMI_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cvs1 = ((System.Windows.Controls.Canvas)(target));
            return;
            case 3:
            this.silo1 = ((HMIControl.Silo)(target));
            return;
            case 4:
            this.silo2 = ((HMIControl.Silo)(target));
            return;
            case 5:
            this.elevator1 = ((HMIControl.Elevator)(target));
            return;
            case 6:
            this.elevator2 = ((HMIControl.Elevator)(target));
            return;
            case 7:
            this.preCleaner1 = ((HMIControl.PreCleaner)(target));
            return;
            case 8:
            this.bucket1 = ((HMIControl.Bucket)(target));
            return;
            case 9:
            this.truck1 = ((HMIControl.Truck)(target));
            return;
            case 10:
            this.chainConveyor1 = ((HMIControl.ChainConveyor)(target));
            return;
            case 11:
            this.magnetCleaner1 = ((HMIControl.MagnetCleaner)(target));
            return;
            case 12:
            this.chainConveyor2 = ((HMIControl.ChainConveyor)(target));
            return;
            case 13:
            this.chainConveyor3 = ((HMIControl.ChainConveyor)(target));
            return;
            case 14:
            this.chainConveyor4 = ((HMIControl.ChainConveyor)(target));
            return;
            case 15:
            this.cyclone1 = ((HMIControl.Cyclone)(target));
            return;
            case 16:
            this.productline = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.fromTo12 = ((HMIControl.FromTo)(target));
            return;
            case 18:
            this.selectSwitch1 = ((HMIControl.SelectSwitch)(target));
            return;
            case 19:
            this.selectSwitch3 = ((HMIControl.SelectSwitch)(target));
            return;
            case 20:
            this.fromTo1 = ((HMIControl.FromTo)(target));
            return;
            case 21:
            this.fromTo2 = ((HMIControl.FromTo)(target));
            return;
            case 22:
            this.fromTo3 = ((HMIControl.FromTo)(target));
            return;
            case 23:
            this.slideGate1 = ((HMIControl.SlideGate)(target));
            return;
            case 24:
            this.slideGate2 = ((HMIControl.SlideGate)(target));
            return;
            case 25:
            this.fromTo4 = ((HMIControl.FromTo)(target));
            return;
            case 26:
            this.fromTo5 = ((HMIControl.FromTo)(target));
            return;
            case 27:
            this.slideGate3 = ((HMIControl.SlideGate)(target));
            return;
            case 28:
            this.cyclone2 = ((HMIControl.Cyclone)(target));
            return;
            case 29:
            this.fromTo6 = ((HMIControl.FromTo)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

