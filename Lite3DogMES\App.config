<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <!-- 数据库连接字符串 -->
    <add name="Lite3DogMES" 
         connectionString="Server=.;Database=Lite3DogMES;Integrated Security=true;Connection Timeout=30;Command Timeout=60;" 
         providerName="System.Data.SqlClient" />
  </connectionStrings>
  
  <appSettings>
    <!-- 应用程序基本设置 -->
    <add key="ApplicationName" value="云深处科技 Lite3小狗 MES生产管理系统" />
    <add key="Version" value="1.0.0" />
    <add key="Company" value="云深处科技" />
    
    <!-- 二维码设置 -->
    <add key="QRCodeWidth" value="300" />
    <add key="QRCodeHeight" value="300" />
    <add key="QRCodeMargin" value="1" />
    <add key="QRCodeLogoPath" value="Images\logo.png" />
    
    <!-- 摄像头设置 -->
    <add key="DefaultCameraIndex" value="0" />
    <add key="CameraResolutionWidth" value="640" />
    <add key="CameraResolutionHeight" value="480" />
    <add key="AutoScanEnabled" value="true" />
    
    <!-- 工序设置 -->
    <add key="ProcessTimeoutWarningMinutes" value="30" />
    <add key="ProcessTimeoutErrorMinutes" value="60" />
    <add key="MaxConcurrentProcesses" value="10" />
    
    <!-- 质量控制设置 -->
    <add key="QualityPassThreshold" value="80" />
    <add key="QualityWarningThreshold" value="70" />
    <add key="AutoQualityCheck" value="true" />
    
    <!-- 报表设置 -->
    <add key="ReportOutputPath" value="Reports" />
    <add key="ReportRetentionDays" value="90" />
    <add key="AutoGenerateReports" value="true" />
    
    <!-- 系统监控设置 -->
    <add key="MonitoringEnabled" value="true" />
    <add key="MonitoringIntervalMinutes" value="5" />
    <add key="HealthCheckIntervalMinutes" value="60" />
    
    <!-- 备份设置 -->
    <add key="AutoBackupEnabled" value="true" />
    <add key="BackupIntervalHours" value="24" />
    <add key="BackupPath" value="C:\MESBackup" />
    <add key="BackupRetentionDays" value="30" />
    
    <!-- 日志设置 -->
    <add key="LogLevel" value="Info" />
    <add key="LogPath" value="Logs" />
    <add key="LogRetentionDays" value="30" />
    <add key="EnablePerformanceLogging" value="false" />
    
    <!-- 用户界面设置 -->
    <add key="Theme" value="Dark" />
    <add key="Language" value="zh-CN" />
    <add key="RefreshIntervalSeconds" value="5" />
    <add key="ShowAnimations" value="true" />
    
    <!-- 安全设置 -->
    <add key="SessionTimeoutMinutes" value="480" />
    <add key="PasswordMinLength" value="6" />
    <add key="EnableAuditLog" value="true" />
    
    <!-- 性能设置 -->
    <add key="DatabaseConnectionPoolSize" value="10" />
    <add key="QueryTimeoutSeconds" value="30" />
    <add key="CacheExpirationMinutes" value="5" />
    
    <!-- 集成设置 -->
    <add key="EnableWebAPI" value="false" />
    <add key="WebAPIPort" value="8080" />
    <add key="EnableMQTT" value="false" />
    <add key="MQTTBroker" value="localhost" />
    <add key="MQTTPort" value="1883" />
  </appSettings>
  
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="fileListener" 
             type="System.Diagnostics.TextWriterTraceListener" 
             initializeData="Logs\trace.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
</configuration>
