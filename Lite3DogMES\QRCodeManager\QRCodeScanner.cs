using System;
using System.Drawing;
using System.IO;
using ZXing;
using ZXing.Windows.Compatibility;
using System.Windows.Forms;
using AForge.Video;
using AForge.Video.DirectShow;

namespace Lite3DogMES.QRCodeManager
{
    /// <summary>
    /// 二维码扫描器
    /// </summary>
    public class QRCodeScanner : IDisposable
    {
        private VideoCaptureDevice _videoSource;
        private BarcodeReader _barcodeReader;
        private bool _isScanning;
        private PictureBox _previewControl;

        public event EventHandler<QRCodeScanResult> QRCodeScanned;
        public event EventHandler<string> ScanError;

        public QRCodeScanner()
        {
            InitializeBarcodeReader();
        }

        /// <summary>
        /// 初始化条码读取器
        /// </summary>
        private void InitializeBarcodeReader()
        {
            _barcodeReader = new BarcodeReader
            {
                AutoRotate = true,
                TryInverted = true,
                Options = new ZXing.Common.DecodingOptions
                {
                    TryHarder = true,
                    PossibleFormats = new[] { BarcodeFormat.QR_CODE }
                }
            };
        }

        /// <summary>
        /// 获取可用的摄像头设备
        /// </summary>
        /// <returns>摄像头设备列表</returns>
        public FilterInfoCollection GetAvailableCameras()
        {
            return new FilterInfoCollection(FilterCategory.VideoInputDevice);
        }

        /// <summary>
        /// 启动摄像头扫描
        /// </summary>
        /// <param name="cameraIndex">摄像头索引</param>
        /// <param name="previewControl">预览控件</param>
        public void StartCameraScanning(int cameraIndex = 0, PictureBox previewControl = null)
        {
            try
            {
                var cameras = GetAvailableCameras();
                if (cameras.Count == 0)
                {
                    throw new Exception("未找到可用的摄像头设备");
                }

                if (cameraIndex >= cameras.Count)
                {
                    cameraIndex = 0;
                }

                _previewControl = previewControl;
                _videoSource = new VideoCaptureDevice(cameras[cameraIndex].MonikerString);
                _videoSource.NewFrame += OnNewFrame;
                _videoSource.Start();
                _isScanning = true;
            }
            catch (Exception ex)
            {
                ScanError?.Invoke(this, $"启动摄像头失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止摄像头扫描
        /// </summary>
        public void StopCameraScanning()
        {
            try
            {
                _isScanning = false;
                if (_videoSource != null && _videoSource.IsRunning)
                {
                    _videoSource.SignalToStop();
                    _videoSource.WaitForStop();
                    _videoSource = null;
                }
            }
            catch (Exception ex)
            {
                ScanError?.Invoke(this, $"停止摄像头失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理新帧
        /// </summary>
        private void OnNewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            try
            {
                var frame = (Bitmap)eventArgs.Frame.Clone();

                // 更新预览
                if (_previewControl != null)
                {
                    _previewControl.Invoke(new Action(() =>
                    {
                        _previewControl.Image?.Dispose();
                        _previewControl.Image = (Bitmap)frame.Clone();
                    }));
                }

                // 扫描二维码
                if (_isScanning)
                {
                    ScanQRCodeFromBitmap(frame);
                }

                frame.Dispose();
            }
            catch (Exception ex)
            {
                ScanError?.Invoke(this, $"处理视频帧失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从图片文件扫描二维码
        /// </summary>
        /// <param name="imagePath">图片文件路径</param>
        /// <returns>扫描结果</returns>
        public QRCodeScanResult ScanFromFile(string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath))
                {
                    return new QRCodeScanResult { Success = false, ErrorMessage = "文件不存在" };
                }

                using (var bitmap = new Bitmap(imagePath))
                {
                    return ScanQRCodeFromBitmap(bitmap);
                }
            }
            catch (Exception ex)
            {
                return new QRCodeScanResult 
                { 
                    Success = false, 
                    ErrorMessage = $"扫描文件失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 从Bitmap扫描二维码
        /// </summary>
        /// <param name="bitmap">图片</param>
        /// <returns>扫描结果</returns>
        private QRCodeScanResult ScanQRCodeFromBitmap(Bitmap bitmap)
        {
            try
            {
                var result = _barcodeReader.Decode(bitmap);
                if (result != null)
                {
                    var scanResult = new QRCodeScanResult
                    {
                        Success = true,
                        Content = result.Text,
                        Format = result.BarcodeFormat.ToString(),
                        ScanTime = DateTime.Now,
                        RawBytes = result.RawBytes
                    };

                    // 解析产品信息
                    ParseProductInfo(scanResult);

                    // 触发扫描事件
                    QRCodeScanned?.Invoke(this, scanResult);

                    return scanResult;
                }
                else
                {
                    return new QRCodeScanResult 
                    { 
                        Success = false, 
                        ErrorMessage = "未识别到二维码" 
                    };
                }
            }
            catch (Exception ex)
            {
                return new QRCodeScanResult 
                { 
                    Success = false, 
                    ErrorMessage = $"扫描失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 解析产品信息
        /// </summary>
        /// <param name="scanResult">扫描结果</param>
        private void ParseProductInfo(QRCodeScanResult scanResult)
        {
            try
            {
                // 尝试解析JSON格式的产品信息
                var productInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(scanResult.Content);
                
                scanResult.ProductSN = productInfo?.ProductSN;
                scanResult.ProductModel = productInfo?.ProductModel;
                
                if (DateTime.TryParse(productInfo?.ProductionDate?.ToString(), out DateTime productionDate))
                {
                    scanResult.ProductionDate = productionDate;
                }
            }
            catch
            {
                // 如果不是JSON格式，尝试从简单格式解析
                if (scanResult.Content.StartsWith("LITE3_"))
                {
                    scanResult.ProductSN = scanResult.Content.Substring(6);
                    scanResult.ProductModel = "Lite3";
                }
            }
        }

        /// <summary>
        /// 手动触发单次扫描
        /// </summary>
        public void TriggerScan()
        {
            if (_previewControl?.Image != null)
            {
                var bitmap = new Bitmap(_previewControl.Image);
                var result = ScanQRCodeFromBitmap(bitmap);
                bitmap.Dispose();

                if (!result.Success)
                {
                    ScanError?.Invoke(this, result.ErrorMessage);
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopCameraScanning();
            _barcodeReader?.Dispose();
            _previewControl?.Image?.Dispose();
        }
    }

    /// <summary>
    /// 二维码扫描结果
    /// </summary>
    public class QRCodeScanResult
    {
        public bool Success { get; set; }
        public string Content { get; set; }
        public string Format { get; set; }
        public DateTime ScanTime { get; set; }
        public byte[] RawBytes { get; set; }
        public string ErrorMessage { get; set; }
        
        // 产品相关信息
        public string ProductSN { get; set; }
        public string ProductModel { get; set; }
        public DateTime? ProductionDate { get; set; }
    }
}
