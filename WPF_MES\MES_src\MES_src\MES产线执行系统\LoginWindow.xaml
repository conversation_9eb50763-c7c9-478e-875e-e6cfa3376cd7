﻿<Window x:Class="MMIS.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="LoginWindow" Height="300" Width="300" WindowStartupLocation="CenterScreen">
    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#330033" Offset="0"/>
            <GradientStop Color="#330033" Offset="0.93"/>
        </LinearGradientBrush>
    </Window.Background>
    <Grid>
        <Label Content="用户名:" Foreground="White" HorizontalAlignment="Left" Height="28" Margin="42,130,0,0" VerticalAlignment="Top" Width="57"/>
        <Label Content="密码:" Foreground="White" HorizontalAlignment="Left" Height="28" Margin="42,172,0,0" VerticalAlignment="Top" Width="57"/>
        <TextBox x:Name="User" HorizontalAlignment="Left" Height="28" Margin="113,130,0,0" TextWrapping="Wrap" Text="dldz" VerticalAlignment="Top" Width="128"/>
        <PasswordBox x:Name="Password" HorizontalAlignment="Left" Height="28" Margin="113,172,0,0" VerticalAlignment="Top" Width="128"/>
        <Label Content="大连电子学校库位管理系统" FontSize="20" FontStyle="Oblique" Foreground="White" HorizontalAlignment="Left" Height="36" Margin="20,53,0,0" VerticalAlignment="Top" Width="251"/>
        <Button Content="登录" HorizontalAlignment="Left" Height="25" Margin="57,228,0,0" VerticalAlignment="Top" Width="68" Click="Button_Click"/>
        <Button Content="退出" HorizontalAlignment="Left" Height="25" Margin="163,228,0,0" VerticalAlignment="Top" Width="68" Click="Button_Click_1"/>
    </Grid>
</Window>
