{"Version": 1, "WorkspaceRootPath": "D:\\MES\\C# 上位机MES系统+二维码扫描+源码 案例\\WPF_SCADA\\WPF 上位机 MES 2\\Program\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{31CE7714-DDF1-4FB2-A479-9818B1B8D9B2}|CoreTest\\Example.csproj|d:\\mes\\c# 上位机mes系统+二维码扫描+源码 案例\\wpf_scada\\wpf 上位机 mes 2\\program\\coretest\\clientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{31CE7714-DDF1-4FB2-A479-9818B1B8D9B2}|CoreTest\\Example.csproj|solutionrelative:coretest\\clientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{31CE7714-DDF1-4FB2-A479-9818B1B8D9B2}|CoreTest\\Example.csproj|d:\\mes\\c# 上位机mes系统+二维码扫描+源码 案例\\wpf_scada\\wpf 上位机 mes 2\\program\\coretest\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{31CE7714-DDF1-4FB2-A479-9818B1B8D9B2}|CoreTest\\Example.csproj|solutionrelative:coretest\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\MES\\C# 上位机MES系统+二维码扫描+源码 案例\\WPF_SCADA\\WPF 上位机 MES 2\\Program\\CoreTest\\App.xaml.cs", "RelativeDocumentMoniker": "CoreTest\\App.xaml.cs", "ToolTip": "D:\\MES\\C# 上位机MES系统+二维码扫描+源码 案例\\WPF_SCADA\\WPF 上位机 MES 2\\Program\\CoreTest\\App.xaml.cs", "RelativeToolTip": "CoreTest\\App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T05:22:15.021Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ClientService.cs", "DocumentMoniker": "D:\\MES\\C# 上位机MES系统+二维码扫描+源码 案例\\WPF_SCADA\\WPF 上位机 MES 2\\Program\\CoreTest\\ClientService.cs", "RelativeDocumentMoniker": "CoreTest\\ClientService.cs", "ToolTip": "D:\\MES\\C# 上位机MES系统+二维码扫描+源码 案例\\WPF_SCADA\\WPF 上位机 MES 2\\Program\\CoreTest\\ClientService.cs", "RelativeToolTip": "CoreTest\\ClientService.cs", "ViewState": "AgIAAP4AAAAAAAAAAAAYwAYBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T05:22:15.052Z", "EditorCaption": ""}]}]}]}