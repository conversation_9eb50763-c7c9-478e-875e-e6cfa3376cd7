﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" 
                    xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
                    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d">
    <SolidColorBrush x:Key="TabControlNormalBorderBrush" Color="#8C8E94"/>
    <!-- 应该在此定义资源字典条目。-->
    <Style x:Key="TabControlStyle2" TargetType="{x:Type TabControl}">
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"/>
        <Setter Property="Padding" Value="4,4,4,4"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource TabControlNormalBorderBrush}"/>
        <Setter Property="Background" Value="#F9F9F9"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <Grid ClipToBounds="true" SnapsToDevicePixels="true" KeyboardNavigation.TabNavigation="Local">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition x:Name="ColumnDefinition0" Width="Auto"/>
                            <ColumnDefinition x:Name="ColumnDefinition1" Width="0"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="RowDefinition0" MaxHeight="87.5"/>
                            <RowDefinition x:Name="RowDefinition1" Height="*"/>
                        </Grid.RowDefinitions>
                        <Border x:Name="ContentPanel" BorderBrush="{TemplateBinding BorderBrush}" Grid.Column="0" KeyboardNavigation.DirectionalNavigation="Contained" Grid.Row="1" KeyboardNavigation.TabIndex="2" KeyboardNavigation.TabNavigation="Local" Margin="0">
                            <!-- Background="{TemplateBinding Background}" -->
                            <ContentPresenter x:Name="PART_SelectedContentHost" ContentSource="SelectedContent" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" Opacity="1"/>
                        </Border>
                        <DockPanel x:Name="HeaderPanel" HorizontalAlignment="Stretch" Height="Auto" Width="Auto" IsItemsHost="True"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="TabItemFocusVisual2">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="3,3,3,1" SnapsToDevicePixels="true" Stroke="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" StrokeThickness="1" StrokeDashArray="1 2"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <LinearGradientBrush x:Key="ButtonNormalBackground" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#F3F3F3" Offset="0"/>
        <GradientStop Color="#EBEBEB" Offset="0.5"/>
        <GradientStop Color="#DDDDDD" Offset="0.5"/>
        <GradientStop Color="#CDCDCD" Offset="1"/>
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="TabItemHotBackground" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#EAF6FD" Offset="0.15"/>
        <GradientStop Color="#D9F0FC" Offset=".5"/>
        <GradientStop Color="#BEE6FD" Offset=".5"/>
        <GradientStop Color="#A7D9F5" Offset="1"/>
    </LinearGradientBrush>
    <SolidColorBrush x:Key="TabItemSelectedBackground" Color="#F9F9F9"/>
    <SolidColorBrush x:Key="TabItemHotBorderBrush" Color="#3C7FB1"/>
    <SolidColorBrush x:Key="TabItemDisabledBackground" Color="#F4F4F4"/>
    <SolidColorBrush x:Key="TabItemDisabledBorderBrush" Color="#FFC9C7BA"/>
    <Style x:Key="TabItemStyle2" TargetType="{x:Type TabItem}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource TabItemFocusVisual2}"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="Padding" Value="6,1,6,1"/>
        <Setter Property="BorderBrush" Value="{StaticResource TabControlNormalBorderBrush}"/>
        <Setter Property="Background" Value="{StaticResource ButtonNormalBackground}"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Stretch"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabItem}">
                    <Grid SnapsToDevicePixels="true">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="0.69*"/>
                            <RowDefinition Height="0.31*"/>
                        </Grid.RowDefinitions>
                        <Border x:Name="Bd" BorderThickness="0" CornerRadius="3" BorderBrush="Black" Margin="0" Grid.RowSpan="2" Visibility="Hidden">
                            
                        </Border>
                        <Border x:Name="fg" BorderThickness="0" CornerRadius="3" BorderBrush="Black" Margin="0" Grid.RowSpan="2" Visibility="Hidden" RenderTransformOrigin="0.5,0.5">
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <SkewTransform/>
                                    <RotateTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </Border.RenderTransform>
                           
                        </Border>
                        <TextBlock Margin="0,0.333,0,3.833" TextWrapping="Wrap" VerticalAlignment="Stretch" d:LayoutOverrides="Height" Grid.Row="1" HorizontalAlignment="Center" Text="{TemplateBinding Header}" Foreground="White"/>
                        <Border x:Name="ico" BorderThickness="0" CornerRadius="3" BorderBrush="Black" Margin="4,4,4.25,0" Grid.RowSpan="1" HorizontalAlignment="Center" VerticalAlignment="Center" Width="48" Height="48" Background="{TemplateBinding Background}" RenderTransformOrigin="0.5,0.5">
                            <Border.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <SkewTransform/>
                                    <RotateTransform/>
                                    <TranslateTransform/>
                                </TransformGroup>
                            </Border.RenderTransform>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true"/>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter Property="Visibility" TargetName="Bd" Value="Visible"/>
                            <Setter Property="Panel.ZIndex" TargetName="ico" Value="1"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="false"/>
                                <Condition Property="IsMouseOver" Value="true"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Visibility" TargetName="fg" Value="Visible"/>
                            <Setter Property="RenderTransform" TargetName="ico">
                                <Setter.Value>
                                    <TransformGroup>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                        <SkewTransform/>
                                        <RotateTransform/>
                                        <TranslateTransform/>
                                    </TransformGroup>
                                </Setter.Value>
                            </Setter>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false"/>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>