using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;

namespace Lite3DogMES.Configuration
{
    /// <summary>
    /// 系统配置管理器
    /// </summary>
    public class SystemConfiguration
    {
        private readonly string _connectionString;
        private static SystemConfiguration _instance;
        private static readonly object _lock = new object();
        private Dictionary<string, string> _configCache;
        private DateTime _lastCacheUpdate;

        private SystemConfiguration()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["Lite3DogMES"]?.ConnectionString 
                ?? "Server=.;Database=Lite3DogMES;Integrated Security=true;";
            _configCache = new Dictionary<string, string>();
            LoadConfiguration();
        }

        public static SystemConfiguration Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new SystemConfiguration();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public string GetConfigValue(string key, string defaultValue = "")
        {
            // 检查缓存是否需要刷新（每5分钟刷新一次）
            if (DateTime.Now - _lastCacheUpdate > TimeSpan.FromMinutes(5))
            {
                LoadConfiguration();
            }

            return _configCache.TryGetValue(key, out string value) ? value : defaultValue;
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>是否成功</returns>
        public bool SetConfigValue(string key, string value)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = @"
                        IF EXISTS (SELECT 1 FROM SystemConfig WHERE ConfigKey = @Key)
                            UPDATE SystemConfig SET ConfigValue = @Value, UpdatedTime = GETDATE() WHERE ConfigKey = @Key
                        ELSE
                            INSERT INTO SystemConfig (ConfigKey, ConfigValue, Description, Category) 
                            VALUES (@Key, @Value, '', 'Custom')";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@Key", key);
                        command.Parameters.AddWithValue("@Value", value);
                        command.ExecuteNonQuery();
                    }
                }

                // 更新缓存
                _configCache[key] = value;
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var sql = "SELECT ConfigKey, ConfigValue FROM SystemConfig WHERE IsActive = 1";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        var newCache = new Dictionary<string, string>();
                        while (reader.Read())
                        {
                            newCache[reader["ConfigKey"].ToString()] = reader["ConfigValue"].ToString();
                        }
                        
                        _configCache = newCache;
                        _lastCacheUpdate = DateTime.Now;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载配置失败: {ex.Message}");
            }
        }

        // 常用配置属性
        public string QRCodePrefix => GetConfigValue("QR_CODE_PREFIX", "LITE3_");
        public string ProductSNPrefix => GetConfigValue("PRODUCT_SN_PREFIX", "L3D");
        public string DefaultLocation => GetConfigValue("DEFAULT_LOCATION", "云深处科技生产车间");
        public int QualityPassScore => int.Parse(GetConfigValue("QUALITY_PASS_SCORE", "80"));
        public int ProcessTimeoutMultiplier => int.Parse(GetConfigValue("PROCESS_TIMEOUT_MULTIPLIER", "2"));
        public bool EnableAutoBackup => bool.Parse(GetConfigValue("ENABLE_AUTO_BACKUP", "true"));
        public int BackupIntervalHours => int.Parse(GetConfigValue("BACKUP_INTERVAL_HOURS", "24"));
        public string BackupPath => GetConfigValue("BACKUP_PATH", @"C:\MESBackup");
        public int MaxConcurrentProcesses => int.Parse(GetConfigValue("MAX_CONCURRENT_PROCESSES", "10"));
        public bool EnablePerformanceLogging => bool.Parse(GetConfigValue("ENABLE_PERFORMANCE_LOGGING", "false"));
        public int LogRetentionDays => int.Parse(GetConfigValue("LOG_RETENTION_DAYS", "30"));
    }

    /// <summary>
    /// 性能优化管理器
    /// </summary>
    public class PerformanceOptimizer
    {
        private readonly string _connectionString;
        private readonly SystemConfiguration _config;

        public PerformanceOptimizer(string connectionString)
        {
            _connectionString = connectionString;
            _config = SystemConfiguration.Instance;
        }

        /// <summary>
        /// 优化数据库性能
        /// </summary>
        public void OptimizeDatabasePerformance()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 更新统计信息
                    var updateStatsCommands = new[]
                    {
                        "UPDATE STATISTICS Products",
                        "UPDATE STATISTICS ProcessRecords",
                        "UPDATE STATISTICS QualityTests",
                        "UPDATE STATISTICS ProductTraceability",
                        "UPDATE STATISTICS ScanRecords"
                    };

                    foreach (var command in updateStatsCommands)
                    {
                        using (var cmd = new SqlCommand(command, connection))
                        {
                            cmd.ExecuteNonQuery();
                        }
                    }

                    // 重建索引（如果碎片化严重）
                    var rebuildIndexCommands = new[]
                    {
                        "ALTER INDEX ALL ON Products REORGANIZE",
                        "ALTER INDEX ALL ON ProcessRecords REORGANIZE",
                        "ALTER INDEX ALL ON QualityTests REORGANIZE"
                    };

                    foreach (var command in rebuildIndexCommands)
                    {
                        try
                        {
                            using (var cmd = new SqlCommand(command, connection))
                            {
                                cmd.ExecuteNonQuery();
                            }
                        }
                        catch
                        {
                            // 忽略索引重建错误
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库性能优化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理历史数据
        /// </summary>
        public void CleanupHistoricalData()
        {
            try
            {
                var retentionDays = _config.LogRetentionDays;
                var cutoffDate = DateTime.Now.AddDays(-retentionDays);

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 清理过期的追溯记录（保留重要的生产数据）
                    var cleanupCommands = new[]
                    {
                        $"DELETE FROM ScanRecords WHERE ScanTime < '{cutoffDate:yyyy-MM-dd}' AND ScanResult = '失败'",
                        $"DELETE FROM ProductTraceability WHERE EventTime < '{cutoffDate:yyyy-MM-dd}' AND EventType = '系统日志'"
                    };

                    foreach (var command in cleanupCommands)
                    {
                        using (var cmd = new SqlCommand(command, connection))
                        {
                            var deletedRows = cmd.ExecuteNonQuery();
                            Console.WriteLine($"清理了 {deletedRows} 条历史记录");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理历史数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 备份数据库
        /// </summary>
        public bool BackupDatabase()
        {
            try
            {
                var backupPath = _config.BackupPath;
                if (!System.IO.Directory.Exists(backupPath))
                {
                    System.IO.Directory.CreateDirectory(backupPath);
                }

                var backupFileName = $"Lite3DogMES_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak";
                var fullBackupPath = System.IO.Path.Combine(backupPath, backupFileName);

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    var backupSql = $"BACKUP DATABASE Lite3DogMES TO DISK = '{fullBackupPath}'";
                    using (var command = new SqlCommand(backupSql, connection))
                    {
                        command.CommandTimeout = 300; // 5分钟超时
                        command.ExecuteNonQuery();
                    }
                }

                Console.WriteLine($"数据库备份成功: {fullBackupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据库备份失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查系统健康状态
        /// </summary>
        public SystemHealthStatus CheckSystemHealth()
        {
            var healthStatus = new SystemHealthStatus
            {
                CheckTime = DateTime.Now
            };

            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();

                    // 检查数据库连接
                    healthStatus.DatabaseConnected = true;

                    // 检查数据库大小
                    var sizeSql = @"
                        SELECT 
                            SUM(size * 8.0 / 1024) as SizeMB
                        FROM sys.master_files 
                        WHERE database_id = DB_ID('Lite3DogMES')";
                    
                    using (var command = new SqlCommand(sizeSql, connection))
                    {
                        var result = command.ExecuteScalar();
                        healthStatus.DatabaseSizeMB = result != DBNull.Value ? Convert.ToDouble(result) : 0;
                    }

                    // 检查活跃产品数量
                    var productCountSql = "SELECT COUNT(*) FROM Products WHERE IsCompleted = 0";
                    using (var command = new SqlCommand(productCountSql, connection))
                    {
                        healthStatus.ActiveProductCount = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // 检查今日生产数量
                    var todayProductionSql = @"
                        SELECT COUNT(*) FROM Products 
                        WHERE CAST(ProductionDate AS DATE) = CAST(GETDATE() AS DATE)";
                    
                    using (var command = new SqlCommand(todayProductionSql, connection))
                    {
                        healthStatus.TodayProductionCount = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // 检查异常数量
                    var exceptionSql = @"
                        SELECT COUNT(*) FROM ProcessRecords 
                        WHERE QualityResult = '不合格' 
                        AND CAST(StartTime AS DATE) = CAST(GETDATE() AS DATE)";
                    
                    using (var command = new SqlCommand(exceptionSql, connection))
                    {
                        healthStatus.TodayExceptionCount = Convert.ToInt32(command.ExecuteScalar());
                    }
                }

                // 评估整体健康状态
                healthStatus.OverallHealth = EvaluateOverallHealth(healthStatus);
            }
            catch (Exception ex)
            {
                healthStatus.DatabaseConnected = false;
                healthStatus.ErrorMessage = ex.Message;
                healthStatus.OverallHealth = HealthLevel.Critical;
            }

            return healthStatus;
        }

        private HealthLevel EvaluateOverallHealth(SystemHealthStatus status)
        {
            if (!status.DatabaseConnected)
                return HealthLevel.Critical;

            if (status.DatabaseSizeMB > 10000) // 10GB
                return HealthLevel.Warning;

            if (status.TodayExceptionCount > status.TodayProductionCount * 0.1) // 异常率超过10%
                return HealthLevel.Warning;

            return HealthLevel.Good;
        }
    }

    /// <summary>
    /// 系统健康状态
    /// </summary>
    public class SystemHealthStatus
    {
        public DateTime CheckTime { get; set; }
        public bool DatabaseConnected { get; set; }
        public double DatabaseSizeMB { get; set; }
        public int ActiveProductCount { get; set; }
        public int TodayProductionCount { get; set; }
        public int TodayExceptionCount { get; set; }
        public HealthLevel OverallHealth { get; set; }
        public string ErrorMessage { get; set; }
    }

    public enum HealthLevel
    {
        Good,
        Warning,
        Critical
    }

    /// <summary>
    /// 系统监控服务
    /// </summary>
    public class SystemMonitorService
    {
        private readonly PerformanceOptimizer _optimizer;
        private readonly SystemConfiguration _config;
        private System.Timers.Timer _monitorTimer;
        private System.Timers.Timer _backupTimer;

        public event EventHandler<SystemHealthStatus> HealthStatusChanged;
        public event EventHandler<string> SystemAlert;

        public SystemMonitorService(string connectionString)
        {
            _optimizer = new PerformanceOptimizer(connectionString);
            _config = SystemConfiguration.Instance;
            
            InitializeTimers();
        }

        private void InitializeTimers()
        {
            // 健康检查定时器（每小时检查一次）
            _monitorTimer = new System.Timers.Timer(TimeSpan.FromHours(1).TotalMilliseconds);
            _monitorTimer.Elapsed += OnMonitorTimer_Elapsed;

            // 备份定时器
            if (_config.EnableAutoBackup)
            {
                _backupTimer = new System.Timers.Timer(TimeSpan.FromHours(_config.BackupIntervalHours).TotalMilliseconds);
                _backupTimer.Elapsed += OnBackupTimer_Elapsed;
            }
        }

        public void StartMonitoring()
        {
            _monitorTimer.Start();
            _backupTimer?.Start();
        }

        public void StopMonitoring()
        {
            _monitorTimer.Stop();
            _backupTimer?.Stop();
        }

        private void OnMonitorTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                var healthStatus = _optimizer.CheckSystemHealth();
                HealthStatusChanged?.Invoke(this, healthStatus);

                // 根据健康状态发送告警
                switch (healthStatus.OverallHealth)
                {
                    case HealthLevel.Warning:
                        SystemAlert?.Invoke(this, "系统健康状态警告：请检查系统状态");
                        break;
                    case HealthLevel.Critical:
                        SystemAlert?.Invoke(this, "系统健康状态严重：需要立即处理");
                        break;
                }

                // 执行性能优化
                if (DateTime.Now.Hour == 2) // 凌晨2点执行优化
                {
                    _optimizer.OptimizeDatabasePerformance();
                    _optimizer.CleanupHistoricalData();
                }
            }
            catch (Exception ex)
            {
                SystemAlert?.Invoke(this, $"系统监控异常: {ex.Message}");
            }
        }

        private void OnBackupTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                var success = _optimizer.BackupDatabase();
                if (!success)
                {
                    SystemAlert?.Invoke(this, "数据库备份失败");
                }
            }
            catch (Exception ex)
            {
                SystemAlert?.Invoke(this, $"备份过程异常: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _monitorTimer?.Dispose();
            _backupTimer?.Dispose();
        }
    }
}
