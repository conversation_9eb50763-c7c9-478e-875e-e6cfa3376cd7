using System;
using System.Collections.Generic;

namespace Lite3DogMES.TraceabilitySystem
{
    /// <summary>
    /// 追溯事件
    /// </summary>
    public class TraceEvent
    {
        public string ProductID { get; set; }
        public string EventType { get; set; }
        public string EventDescription { get; set; }
        public DateTime EventTime { get; set; }
        public string Location { get; set; }
        public string OperatorID { get; set; }
        public object RelatedData { get; set; }
    }

    /// <summary>
    /// 产品追溯链
    /// </summary>
    public class ProductTraceChain
    {
        public ProductBasicInfo ProductInfo { get; set; }
        public List<TraceEventInfo> TraceEvents { get; set; } = new List<TraceEventInfo>();
        public List<ProcessRecordInfo> ProcessRecords { get; set; } = new List<ProcessRecordInfo>();
        public List<QualityRecordInfo> QualityRecords { get; set; } = new List<QualityRecordInfo>();
        public List<ScanRecordInfo> ScanRecords { get; set; } = new List<ScanRecordInfo>();
        public ShippingInfoDetail ShippingInfo { get; set; }
    }

    /// <summary>
    /// 产品基本信息
    /// </summary>
    public class ProductBasicInfo
    {
        public string ProductID { get; set; }
        public string ProductSN { get; set; }
        public string QRCode { get; set; }
        public string ProductModel { get; set; }
        public DateTime ProductionDate { get; set; }
        public string CurrentStatus { get; set; }
        public string CurrentLocation { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 追溯事件信息
    /// </summary>
    public class TraceEventInfo
    {
        public string EventType { get; set; }
        public string EventDescription { get; set; }
        public DateTime EventTime { get; set; }
        public string Location { get; set; }
        public string OperatorID { get; set; }
        public string RelatedData { get; set; }
    }

    /// <summary>
    /// 工序记录信息
    /// </summary>
    public class ProcessRecordInfo
    {
        public string ProcessName { get; set; }
        public string ProcessCode { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string Status { get; set; }
        public string QualityResult { get; set; }
        public string OperatorID { get; set; }
        public string Location { get; set; }
        public string Remarks { get; set; }
        
        public TimeSpan? ProcessDuration
        {
            get
            {
                if (StartTime.HasValue && EndTime.HasValue)
                    return EndTime.Value - StartTime.Value;
                return null;
            }
        }
    }

    /// <summary>
    /// 质量记录信息
    /// </summary>
    public class QualityRecordInfo
    {
        public string TestType { get; set; }
        public string TestParameter { get; set; }
        public decimal StandardValue { get; set; }
        public decimal ActualValue { get; set; }
        public string TestResult { get; set; }
        public DateTime TestTime { get; set; }
        public string TesterID { get; set; }
        public string TestEquipment { get; set; }
        public string Remarks { get; set; }
        
        public decimal DeviationPercent
        {
            get
            {
                if (StandardValue != 0)
                    return Math.Abs(ActualValue - StandardValue) / StandardValue * 100;
                return 0;
            }
        }
    }

    /// <summary>
    /// 扫描记录信息
    /// </summary>
    public class ScanRecordInfo
    {
        public DateTime ScanTime { get; set; }
        public string ScanLocation { get; set; }
        public string OperatorID { get; set; }
        public string ScanResult { get; set; }
        public string DeviceID { get; set; }
        public string Remarks { get; set; }
    }

    /// <summary>
    /// 发货信息详情
    /// </summary>
    public class ShippingInfoDetail
    {
        public string CustomerName { get; set; }
        public string CustomerAddress { get; set; }
        public DateTime? ShippingDate { get; set; }
        public string TrackingNumber { get; set; }
        public string ShippingCompany { get; set; }
        public string ShippingStatus { get; set; }
        public string OperatorID { get; set; }
        public string Remarks { get; set; }
    }

    /// <summary>
    /// 追溯记录
    /// </summary>
    public class TraceRecord
    {
        public int TraceID { get; set; }
        public string ProductID { get; set; }
        public string ProductSN { get; set; }
        public string ProductModel { get; set; }
        public string EventType { get; set; }
        public string EventDescription { get; set; }
        public DateTime EventTime { get; set; }
        public string Location { get; set; }
        public string OperatorID { get; set; }
        public string OperatorName { get; set; }
        public string RelatedData { get; set; }
    }

    /// <summary>
    /// 追溯报告
    /// </summary>
    public class TraceabilityReport
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public string ProductSN { get; set; }
        public DateTime GeneratedTime { get; set; }
        public ProductBasicInfo ProductInfo { get; set; }
        public ProcessSummary ProcessSummary { get; set; }
        public QualitySummary QualitySummary { get; set; }
        public List<TimelineEvent> Timeline { get; set; } = new List<TimelineEvent>();
        public TimeSpan? TotalProcessTime { get; set; }
    }

    /// <summary>
    /// 工序摘要
    /// </summary>
    public class ProcessSummary
    {
        public int TotalProcesses { get; set; }
        public int CompletedProcesses { get; set; }
        public int PassedProcesses { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal PassRate { get; set; }
    }

    /// <summary>
    /// 质量摘要
    /// </summary>
    public class QualitySummary
    {
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public decimal PassRate { get; set; }
        public List<TestTypeSummary> TestDetails { get; set; } = new List<TestTypeSummary>();
    }

    /// <summary>
    /// 测试类型摘要
    /// </summary>
    public class TestTypeSummary
    {
        public string TestType { get; set; }
        public int TotalCount { get; set; }
        public int PassCount { get; set; }
        public decimal PassRate { get; set; }
    }

    /// <summary>
    /// 时间线事件
    /// </summary>
    public class TimelineEvent
    {
        public DateTime EventTime { get; set; }
        public string EventType { get; set; }
        public string EventDescription { get; set; }
        public string Location { get; set; }
        public string OperatorID { get; set; }
    }

    /// <summary>
    /// 产品位置信息
    /// </summary>
    public class ProductLocation
    {
        public string ProductSN { get; set; }
        public string CurrentLocation { get; set; }
        public string CurrentStatus { get; set; }
        public DateTime UpdatedTime { get; set; }
        public DateTime? LastScanTime { get; set; }
        public string LastScanLocation { get; set; }
    }

    /// <summary>
    /// 追溯查询条件
    /// </summary>
    public class TraceabilityQuery
    {
        public string ProductSN { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string EventType { get; set; }
        public string Location { get; set; }
        public string OperatorID { get; set; }
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    /// <summary>
    /// 追溯查询结果
    /// </summary>
    public class TraceabilityQueryResult
    {
        public List<TraceRecord> Records { get; set; } = new List<TraceRecord>();
        public int TotalCount { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    /// <summary>
    /// 批量追溯结果
    /// </summary>
    public class BatchTraceabilityResult
    {
        public List<ProductTraceChain> SuccessResults { get; set; } = new List<ProductTraceChain>();
        public List<string> FailedProductSNs { get; set; } = new List<string>();
        public string ErrorMessage { get; set; }
    }
}
