﻿<Window x:Class="CoreTest.Trend"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:CoreTest"
        xmlns:wk="clr-namespace:Microsoft.Windows.Controls;assembly=WPFToolkit.Extended"
        xmlns:d3="http://research.microsoft.com/DynamicDataDisplay/1.0"
        xmlns:chart="clr-namespace:Microsoft.Research.DynamicDataDisplay.Charts;assembly=DynamicDataDisplay"
        Title="归档趋势图" TextOptions.TextFormattingMode="Display" FontFamily="SimSun" 
        RenderOptions.EdgeMode="Aliased" Closed="Window_Closed">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <ToolBar  Name="toolBar1" Height="32" FontSize="12" ItemsSource="{Binding}" ItemTemplate="{StaticResource DeleteButton}">
            <Separator/>
            <Button ContentTemplate="{StaticResource FirstButton}" ToolTip="长间隔 时间轴前移" Style="{StaticResource ButtonGeneric}" Command="{x:Static local:MyCommands.First}"/>
            <Button ContentTemplate="{StaticResource PreviousButton}" ToolTip="短间隔 时间轴前移" Style="{StaticResource ButtonGeneric}" Command="{x:Static local:MyCommands.Previous}"/>
            <Button ContentTemplate="{StaticResource NextButton}" ToolTip="短间隔 时间轴后移" Style="{StaticResource ButtonGeneric}" Command="{x:Static local:MyCommands.Next}"/>
            <Button ContentTemplate="{StaticResource LastButton}" ToolTip="长间隔 时间轴后移" Style="{StaticResource ButtonGeneric}" Command="{x:Static local:MyCommands.Last}"/>
            <Separator/>
            <TextBlock Text=" 开始日期：" Margin="0,5,0,0" ToolTip="如果选择超过范围，以起始日期为准"/>
            <wk:DateTimePicker Name="dtstart"/>
            <TextBlock Text=" 结束日期：" Margin="10,5,0,0"/>
            <wk:DateTimePicker Name="dtend"/>
            <Separator/>
            <CheckBox Name="PCheckBox"  Content="选择归档数据项" Background="LightBlue" BorderThickness="2" Margin="5,0,0,0"/>
            <Popup x:Name="popusBottom" IsOpen="{Binding ElementName=PCheckBox,Path=IsChecked}"
               PlacementTarget="{Binding ElementName=PCheckBox}"            
               AllowsTransparency="True"
               PopupAnimation="Fade"
               HorizontalOffset="5"
               VerticalOffset="10">
                <wk:CheckListBox MaxHeight="300" Name="chklist" Height="Auto" DisplayMemberPath="Value" />
            </Popup>
            <Separator/>
            <Button ContentTemplate="{StaticResource SearchButton}" ToolTip="查询历史记录（时间差限制在7日范围内，以免数据量过大影响系统运行）" Margin="10,0,10,0" Style="{StaticResource ButtonGeneric}" 
                    Command="{x:Static local:MyCommands.Query}"/>
            <Separator/>
            <ComboBox Name="comodel"  Background="LightBlue" Width="100" SelectedIndex="0" SelectionChanged="comodel_SelectionChanged">
                <ComboBoxItem Content="单Y轴"/>
                <ComboBoxItem Content="多Y轴"/>
                <ComboBoxItem Content="自动"/>
            </ComboBox>
            <CheckBox Name="chkshow" IsChecked="True" IsThreeState="False" Background="LightBlue" Click="chkshow_Click">显示十字光标</CheckBox>
            <wk:ColorPicker Name="colorpicker" Margin="5,0,0,0" Width="100" SelectedColorChanged="ColorPicker_SelectedColorChanged"/>
        </ToolBar>
        <d3:ChartPlotter  Grid.Row="1" Name="chartPlotter1" Focusable="False">
            <d3:ChartPlotter.MainHorizontalAxis>
                <d3:HorizontalDateTimeAxis Name="hTimeSpanAxis"/>
            </d3:ChartPlotter.MainHorizontalAxis>
            <d3:CursorCoordinateGraph Name="cursor"></d3:CursorCoordinateGraph>
            <d3:AxisCursorGraph x:Name="axiscuror"></d3:AxisCursorGraph>
            <d3:AxisNavigation Placement="Bottom"/>
            <d3:AxisNavigation Placement="Right"/>
        </d3:ChartPlotter>

    </Grid>
</Window>
