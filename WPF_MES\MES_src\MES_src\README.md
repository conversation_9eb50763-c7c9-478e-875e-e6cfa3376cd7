# MES
这是一个用C#写的MES系统  ，用到了supersocket通信框架，以及WPF技术


# 1.订单管理界面：
分为作业计划和生产派工两部分，作业计划部分可由操作人员下订单，然后执行订单。订单的种类包括加工订单，检测订单，拧螺丝订单，轴承压装订单4种，四种订单分别对应不同的流程操作，具体内部流程操作请见第三章。生产派工的功能主要是操作人员可以在人工操作台向立库进行上料，或者从立库下料所需的货物。
注：对于轴承托盘C和螺钉托盘D，上料的时候需要给定“数量”这一参数。
# 2.生产概况界面：
这里主要显示立库，AGV，生产区机器人，检测区机器人和装配区机器人以及人工上下料台的一些状态信息。AGV方面，显示AGV的通信状态，以及其位置状态，位置状态分为三种：已收到消息，在XXX处取货完毕，在XXX处卸货完毕。立库方面，显示立库的通信状态，以及操作状态。操作状态有四种：已收到消息，已出库，已入库，已侧边出库。对于加工区，检测区，装配区，以及人工上下料区，显示各个区域的通信状态，区域的有无托盘状态和区域机器人的工作与否的状态。
# 3.历史记录界面：
此界面可以查询到我们下发的并执行成功的订单数据，另外我们也可以查询到之前的零件的检测数据。并能够生成Excel表供工作人员打印保存。
# 4.立库管理界面：
此界面可以查看当前立库各种零件的剩余数量情况，以及各个库位存储何种零件，并可以按照当前实际零件的情况，修改零件的位置。
# 5.设备管理界面：
此界面可以单独操作立库和AGV。可以单独的让立库出某一库位的货物，或者单独的让立库入某一库位的货物，可以让AGV从地图中的任意一点进行取货和卸货。
