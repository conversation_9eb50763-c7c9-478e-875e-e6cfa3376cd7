﻿<Application x:Class="CoreTest.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:wk="clr-namespace:Microsoft.Windows.Controls;assembly=WPFToolkit.Extended"
             >
    <Application.Resources>
        <ResourceDictionary>

            <SolidColorBrush x:Key="Brush_DataGridAltRowBackground" Color="LightGreen" />
            <LinearGradientBrush x:Key="borderBackground" StartPoint="0,0" EndPoint="0,1">
                <GradientStop Color="DimGray" Offset="0" />
                <GradientStop Color="Black" Offset="0.8" />
                <GradientStop Color="DarkOrange" Offset="1.1" />
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="borderHeightLight" StartPoint="0,0" EndPoint="0,1">
                <GradientStop Color="DimGray" Offset="0" />
                <GradientStop Color="Black" Offset="0.7" />
                <GradientStop Color="WhiteSmoke" Offset="1.1" />
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="smokeBackground" StartPoint="0,0" EndPoint="0,1">
                <GradientStop Color="DimGray" Offset="0" />
                <GradientStop Color="Black" Offset="1" />
            </LinearGradientBrush>
            <Style x:Key="RecipeGridRow" TargetType="{x:Type DataGridRow}">
                <!--<Setter Property="Background" Value="#FF062735" />
            <Setter Property="Foreground" Value="DarkGoldenrod" />-->
                <!--<Setter Property="Height" Value="22" />-->
                <!--<Setter Property="FontFamily" Value="SimSun" />-->
                <Setter Property="FontSize" Value="13" />
                <Setter Property="SnapsToDevicePixels" Value="true"/>
                <Style.Triggers>
                    <Trigger Property="Validation.HasError" Value="True">
                        <Setter Property="BorderBrush"  Value="Red"/>
                        <Setter Property="BorderThickness"  Value="3"/>
                        <Setter Property="ToolTip" Value="{Binding (Validation.Errors)[0].ErrorContent, RelativeSource={RelativeSource Self}}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
            <!-- DataGrid -->

            <Style x:Key="{x:Type DataGrid}" TargetType="{x:Type DataGrid}">
                <!--<Setter Property="IsReadOnly" Value="True"/>-->
                <Setter Property="EnableRowVirtualization" Value="True" />
                <Setter Property="EnableColumnVirtualization" Value="True" />
                <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
                <Setter Property="CanUserSortColumns" Value="True" />
                <Setter Property="CanUserResizeRows" Value="False" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="MinRowHeight" Value="22" />
                <!--<Setter Property="FontFamily" Value="SimSun" />-->
                <!--<Setter Property="FontSize" Value="12" />-->
                <Setter Property="GridLinesVisibility" Value="All" />
                <Setter Property="VerticalGridLinesBrush" Value="Silver"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="AlternatingRowBackground" Value="{StaticResource Brush_DataGridAltRowBackground}" />
                <Setter Property="HorizontalGridLinesBrush" Value="Silver"/>
                <Setter Property="SelectionMode" Value="Single" />
                <Setter Property="SelectionUnit" Value="FullRow"/>
                <Style.Triggers>
                    <Trigger Property="IsGrouping" Value="true">
                        <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="RecipeGrid" TargetType="{x:Type DataGrid}">
                <!--<Setter Property="EnableRowVirtualization" Value="True" />
            <Setter Property="EnableColumnVirtualization" Value="True" />
            <Setter Property="AutoGenerateColumns" Value="False"/>-->
                <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
                <Setter Property="CanUserSortColumns" Value="False" />
                <Setter Property="CanUserAddRows" Value="False" />
                <Setter Property="CanUserDeleteRows" Value="False" />
                <Setter Property="CanUserResizeRows" Value="False" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <!--<Setter Property="Foreground" Value="Black" />-->
                <!--<Setter Property="FontFamily" Value="SimSun" />
            <Setter Property="FontSize" Value="16" />-->
                <!--<Setter Property="FontWeight" Value="Bold" />-->
                <Setter Property="MinColumnWidth" Value="60" />
                <Setter Property="GridLinesVisibility" Value="All" />
                <Setter Property="VerticalGridLinesBrush" Value="LightGray"/>
                <Setter Property="HorizontalGridLinesBrush" Value="LightGray"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
                <Setter Property="SelectionMode" Value="Extended" />
                <Setter Property="SelectionUnit" Value="FullRow"/>
                <Setter Property="RowStyle" Value="{StaticResource RecipeGridRow}" />
            </Style>

            <Style x:Key="{x:Type HeaderedContentControl}" TargetType="{x:Type HeaderedContentControl}">
                <Setter Property="Margin" Value="8"/>
                <!--<Setter Property="FontFamily" Value="SimSun" />-->
                <Setter Property="Focusable" Value="False"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type HeaderedContentControl}">
                            <DockPanel LastChildFill="False">
                                <ContentPresenter ContentSource="Header" DockPanel.Dock="Left" Focusable="False" VerticalAlignment="Center"/>
                                <ContentPresenter ContentSource="Content" MinWidth="150" Width="Auto" Margin="8,0,0,0" DockPanel.Dock="Right" VerticalAlignment="Center"/>
                            </DockPanel>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="h1" TargetType="{x:Type HeaderedContentControl}">
                <Setter Property="Margin" Value="2"/>
                <Setter Property="IsTabStop" Value="False"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type HeaderedContentControl}">
                            <DockPanel LastChildFill="False">
                                <ContentPresenter ContentSource="Header"  DockPanel.Dock="Left" Focusable="False" VerticalAlignment="Center"  />
                                <ContentPresenter ContentSource="Content" MinWidth="100" Width="Auto" Margin="2,0,0,0" DockPanel.Dock="Right" VerticalAlignment="Center"/>
                            </DockPanel>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="ButtonGeneric" TargetType="{x:Type Button}">
                <Setter Property="Background" Value="#00000000"/>
                <Setter Property="Foreground" Value="#FF000000"/>
                <Setter Property="BorderBrush" Value="#FFFFFFFF"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="1"/>
                <Setter Property="Margin" Value="3,0,3,0"/>
                <!--<Setter Property="IsTabStop" Value="False"/>-->
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Grid>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="MouseOver">
                                            <Storyboard>
                                                <ColorAnimation Duration="0" Storyboard.TargetName="OuterBtnBorder" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)" To="#FFFFFFFF"/>
                                                <ColorAnimation Duration="0" Storyboard.TargetName="InnerBtnBorder" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)" To="#7FA9A9A9"/>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <Storyboard>
                                                <ColorAnimation Duration="0" Storyboard.TargetName="InnerBtnBorder" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)" To="#7FA9A9A9"/>
                                                <ColorAnimation Duration="0" Storyboard.TargetName="InnerBtnBorder" Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" To="#7FA9A9A9"/>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Disabled">
                                            <Storyboard>
                                                <DoubleAnimation Duration="0" Storyboard.TargetName="contentPresenter" Storyboard.TargetProperty="Opacity" To="0.3"/>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="FocusStates">
                                        <VisualState x:Name="Focused">
                                            <Storyboard>
                                                <DoubleAnimation Duration="0" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="Opacity" To="0.7"/>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Unfocused"/>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                                <Border x:Name="OuterBtnBorder" BorderBrush="#00FFFFFF" Background="{TemplateBinding Background}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3">
                                    <Border x:Name="InnerBtnBorder" BorderBrush="#00A9A9A9" Background="#00A9A9A9" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2">
                                        <ContentPresenter x:Name="contentPresenter" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" Height="Auto" Width="Auto" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </Border>
                                <Border x:Name="FocusVisualElement" BorderBrush="#FF6DBDD1" Background="{TemplateBinding Background}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2" Margin="1" Opacity="0"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <Style TargetType="wk:DateTimePicker">
                <Setter Property="MinWidth" Value="160"/>
                <Setter Property="Background" Value="LightBlue"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Height" Value="24"/>
                <!--<Setter Property="TextAlignment" Value="Center"/>-->
                <!--<Setter Property="FontWeight" Value="Bold"/>-->
            </Style>
            
            <DataTemplate x:Key="SearchButton">
                <Canvas  Width="16" Height="16" Clip="F1 M 0,0L 71,0L 71,71L 0,71L 0,0">
                    <Path  Width="16" Height="16"  Stretch="Fill" StrokeLineJoin="Round" Stroke="#CD413863" Data="F1 M 20.4059,21.2478C 25.9654,15.6883 34.9791,15.6883 40.5386,21.2478C 45.5265,26.2356 46.0393,34.0039 42.0772,39.5635L 53.8052,51.2917C 54.25,51.7364 54.3101,52.3974 53.9395,52.7681L 51.9262,54.7813C 51.5556,55.1519 50.8946,55.0918 50.4498,54.6471L 38.7217,42.9189C 33.1621,46.8811 25.3938,46.3683 20.4059,41.3804C 14.8465,35.821 14.8465,26.8073 20.4059,21.2478 Z M 23.7614,24.6032C 20.0551,28.3096 20.0551,34.3187 23.7614,38.025C 27.4677,41.7314 33.4769,41.7313 37.1832,38.025C 40.8895,34.3187 40.8895,28.3096 37.1832,24.6032C 33.4769,20.8969 27.4677,20.8969 23.7614,24.6032 Z ">
                        <Path.Fill>
                            <LinearGradientBrush StartPoint="0.260124,-0.0984299" EndPoint="0.559712,0.700474">
                                <GradientStop Color="#CD322448" Offset="0"/>
                                <GradientStop Color="#CD796A94" Offset="1"/>
                            </LinearGradientBrush>
                        </Path.Fill>
                    </Path>
                </Canvas>
            </DataTemplate>

            <DataTemplate x:Key="ResetButton">
                <Grid VerticalAlignment="Center">
                    <Path HorizontalAlignment="Left" VerticalAlignment="Top" Width="16.575" Height="15.013" Fill="#FFFF0000" Stretch="Fill" Data="M2.1923326,13.126918 C2.1923326,13.126918 9.1242888,3.5218058 11.44987,4.1032011 13.775452,4.6845965 11.006801,6.2345933 11.006801,6.2345933 L2.7678984,12.676864 z" Margin="0,1.221,0,0"/>
                    <Path HorizontalAlignment="Left" VerticalAlignment="Top" Width="12.077" Height="14.755" Fill="#FFFF0000" Stretch="Fill" Data="M4.5860118,1.5306513 C4.1381923,2.7258072 11.531427,12.355297 15.344682,14.995039 17.329125,16.368778 11.249546,6.4430705 10.861957,6.4430705 10.474368,6.4430705 5.1923887,-0.087668306 4.5860118,1.5306513 z" Margin="2.832,1.183,0,0"/>
                    <Path HorizontalAlignment="Left" Margin="10.032,2.432,0,0" VerticalAlignment="Top" Width="6.52" Height="5.383" Fill="#FFAB0404" Stretch="Fill" Data="M8.3585482,7.6118008 C8.3585482,7.6118008 9.3829609,6.4793429 10.484053,5.7423106 13.2862,3.8666516 12.129617,5.3933616 12.129617,5.3933616 L8.6703291,8.0523064 z"/>
                    <Path HorizontalAlignment="Left" Margin="0.287,8.6,0,0" VerticalAlignment="Top" Width="8.35" Height="7.361" Fill="#FFAB0404" Stretch="Fill" Data="M2.9934119,12.280726 C2.9934119,12.280726 5.089625,10.252776 6.167291,9.3225421 7.981912,7.7561752 7.4187557,8.9838185 7.4187557,8.9838185 L2.3691003,12.962527 z"/>
                    <Path HorizontalAlignment="Left" Margin="2.815,1.686,0,0" VerticalAlignment="Top" Width="4.308" Height="6.059" Fill="#FFAB0404" Stretch="Fill" Data="M9.0912287,5.4815006 C9.0912287,5.4815006 8.3470667,4.9998377 8.3478827,3.5762162 8.3480212,3.3346328 10.995948,6.9120685 10.995948,6.9120685 L10.684377,7.2172575 z"/>
                    <Path HorizontalAlignment="Left" Margin="7.841,8.657,0,0" VerticalAlignment="Top" Width="6.912" Height="7.304" Fill="#FFAB0404" Stretch="Fill" Data="M11.86076,8.6826086 C11.86076,8.6826086 10.862828,7.9143165 11.929675,7.7667842 12.168981,7.733691 15.684906,12.169333 15.684906,12.169333 L14.649119,11.36494 z"/>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="SaveButton">
                <Image Height="16" Source="Resources/Save.ico" Stretch="Fill" Width="16"/>
            </DataTemplate>

            <DataTemplate x:Key="EditButton">
                <Grid Height="19" Width="19" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Canvas HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="2,2,0,0">
                        <Path Width="10.56" Height="10.5" Canvas.Left="2.53" Canvas.Top="3.01" Stretch="Fill" Data="F1 M 2.53,9.96L 4.96,11.1L 6.08,13.50L 13.09,6.50L 9.6,3L 2.53,9.96 Z " Fill="Black">
                            <Path.OpacityMask>
                                <LinearGradientBrush StartPoint="0.21,0.18" EndPoint="0.76,0.77">
                                    <GradientStop Color="#7F000000" Offset="0"/>
                                    <GradientStop Color="#59000000" Offset="0.106"/>
                                    <GradientStop Color="#3F000000" Offset="0.387"/>
                                    <GradientStop Color="#99000000" Offset="0.703"/>
                                    <GradientStop Color="#CC000000" Offset="1"/>
                                </LinearGradientBrush>
                            </Path.OpacityMask>
                        </Path>
                        <Path Fill="Black"  Stretch="Fill" Height="6.74" Width="6.81" Canvas.Left="7.47" Canvas.Top="1.89" Data="M1.41,1.83 L0.71,2.54 L4.20,6.03 L4.91,5.33 z M3.24,0 L6.81,3.42 L3.5,6.74 L0,3.24 z"/>
                        <Path Width="12.43" Height="12.5" Canvas.Left="2.03" Canvas.Top="1.50" Stretch="Fill" Data="F1 M 13.77,4.22L 11.73,2.18C 11.12,1.78 10.46,2.15 10,2.5L 3.04,9.45C 2.65,9.86 2.53,10.47 2.53,10.73L 2.53,13.5L 5.32,13.5C 5.57,13.5 6.33,13.36 6.64,12.93L 13.54,6.03C 13.86,5.72 14.17,4.74 13.77,4.22 Z" StrokeLineJoin="Round" Stroke="Black" />
                        <Path Width="2.33" Height="2.29" Canvas.Left="2.58" Canvas.Top="11.18" Stretch="Fill" Fill="Black"  Data="F1 M 2.033,11.71L 4.33,14L 2.62,14C 2.17,13.93 2.09,13.79 2,13.31L 2.03,11.71 Z"/>
                    </Canvas>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="NewButton">
                <Path Stretch="Fill" Height="9" Margin="5" Width="9" Data="F1 M18.41,61.67 L19.56,60.52 L21.92,62.88 L24.22,60.57 L25.36,61.72 L23.06,64.03 L25.43,66.4 L24.22,67.60 L21.87,65.25 L19.56,67.56 L18.41,66.41 L20.73,64.08 L18.41,61.67 z" Fill="Black" Opacity="0.85" RenderTransformOrigin="0.5,0.5" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <ScaleTransform/>
                            <SkewTransform/>
                            <RotateTransform Angle="45"/>
                            <TranslateTransform X="0.18" Y="-0.07"/>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
            </DataTemplate>

            <DataTemplate x:Key="DeleteButton">
                <Path Stretch="Fill" Height="9" Margin="5,5,5,5" Width="9" Data="F1 M24.32,60.48 L25.60,61.76 L19.65,67.75 L18.37,66.46 z" Fill="Black" Opacity="0.85" RenderTransformOrigin="0.5,0.5" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <RotateTransform Angle="45"/>
                            <TranslateTransform X="0.06" Y="-0.01"/>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
            </DataTemplate>

            <DataTemplate x:Key="PreviousButton">
                <Path  Stretch="Fill" Height="9" Margin="6,5,8,5" Width="5" Data="M1,0 L1,2 L0,1 Z" Opacity="0.85" Fill="Black"/>
            </DataTemplate>

            <DataTemplate x:Key="NextButton">
                <Path Stretch="Fill" Height="9" Margin="8,5,6,5" Width="5" Data="M0,0 L1,1 L0,2 Z" Opacity="0.85" Fill="Black"/>
            </DataTemplate>

            <DataTemplate x:Key="FirstButton">
                <Grid Height="9" Margin="6,5,5,5" Width="8" Opacity="1">
                    <Path Stretch="Fill" Height="9" HorizontalAlignment="Right" Margin="0" Width="5" Data="M1,0 L1,2 L0,1 Z" Fill="Black" Opacity="0.85"/>
                    <Rectangle HorizontalAlignment="Left" Width="2" Fill="Black" Opacity="0.85"/>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="LastButton">
                <Grid Height="9" Margin="6,5,5,5" Width="8" Opacity="1">
                    <Path Stretch="Fill" Height="9" HorizontalAlignment="Left" Width="5" Data="M0,0 L1,1 L0,2 Z" Fill="Black" Opacity="0.85" />
                    <Rectangle HorizontalAlignment="Right" Width="2" Fill="Black" Opacity="0.85"/>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="UpButton">
                <Path  Stretch="Fill" Height="5" Margin="5,5,8,5" Width="9" Data="M0,2 L1,1 L2,2 Z" Opacity="0.85" Fill="Black"/>
            </DataTemplate>

            <DataTemplate x:Key="DownButton">
                <Path Stretch="Fill" Height="5" Margin="5,5,5,5" Width="9" Data="M0,0 L1,1 L2,0 Z" Opacity="0.85" Fill="Black"/>
            </DataTemplate>

            <DataTemplate x:Key="TopButton">
                <Grid Height="9" Margin="5,5,5,5" Width="9" Opacity="1">
                    <Path Stretch="Fill" Height="5" VerticalAlignment="Bottom" Margin="0" Width="9" Data="M0,2 L1,1 L2,2 Z" Fill="Black" Opacity="0.85"/>
                    <Rectangle VerticalAlignment="Top" Height="2" Fill="Black" Opacity="0.85"/>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BottomButton">
                <Grid Height="9" Margin="5,5,5,5" Width="9" Opacity="1">
                    <Path Stretch="Fill" Height="5" VerticalAlignment="Top" Width="9" Data="M0,0 L1,1 L2,0 Z" Fill="Black" Opacity="0.85" />
                    <Rectangle VerticalAlignment="Bottom" Height="2" Fill="Black" Opacity="0.85"/>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="ToolButton">
                <Path Height="16" Width="16" Data="F1M545.412,316.709C545.206,316.781,544.916,316.716,544.764,316.564L537.733,309.534C537.582,309.382,537.518,309.093,537.587,308.89L538.373,306.647C538.441,306.443,538.407,306.127,538.292,305.946L535.774,301.941C535.662,301.757,535.392,301.591,535.177,301.565L531.752,301.18C531.539,301.153,531.361,301.311,531.361,301.526L531.361,303.76C531.361,303.972,531.539,304.167,531.752,304.192L533.565,304.398C533.779,304.422,534.049,304.591,534.162,304.772L535.219,306.452C535.334,306.633,535.369,306.95,535.294,307.151L534.941,308.167C534.87,308.37,534.688,308.66,534.536,308.811L533.931,309.417C533.777,309.569,533.488,309.753,533.286,309.825L532.271,310.179C532.067,310.249,531.753,310.214,531.571,310.099L529.89,309.045C529.708,308.928,529.539,308.66,529.514,308.448L529.295,306.476C529.27,306.262,529.252,306.084,529.253,306.082C529.256,306.08,529.256,306.073,529.252,306.069C529.247,306.062,529.066,306.06,528.853,306.06L526.712,306.06C526.498,306.06,526.343,306.236,526.366,306.448L526.764,309.98C526.787,310.194,526.955,310.463,527.139,310.577L531.146,313.095C531.329,313.209,531.643,313.244,531.844,313.175L534.087,312.392C534.288,312.318,534.58,312.385,534.731,312.538L541.762,319.564C541.913,319.718,541.978,320.01,541.909,320.211L541.124,322.452C541.054,322.655,541.087,322.971,541.204,323.153L543.721,327.157C543.833,327.341,544.104,327.51,544.315,327.534L547.745,327.921C547.959,327.945,548.134,327.79,548.134,327.573L548.134,325.344C548.134,325.127,547.959,324.932,547.745,324.906L545.931,324.703C545.714,324.682,545.447,324.51,545.333,324.328L544.276,322.649C544.165,322.464,544.131,322.151,544.197,321.947L544.853,320.075C544.924,319.872,545.15,319.646,545.351,319.578L547.226,318.922C547.428,318.85,547.742,318.885,547.927,319.001L549.604,320.058C549.786,320.17,549.957,320.439,549.98,320.651L550.2,322.625C550.225,322.837,550.244,323.014,550.243,323.019C550.238,323.021,550.24,323.027,550.244,323.032C550.248,323.036,550.429,323.041,550.643,323.041L552.783,323.041C552.998,323.041,553.153,322.865,553.131,322.651L552.731,319.118C552.708,318.904,552.541,318.635,552.357,318.521L548.35,316.005C548.169,315.892,547.854,315.854,547.649,315.926z" Fill="Black" Stretch="Fill"/>
            </DataTemplate>

            <DataTemplate x:Key="DownloadButton">
                <Path Data="F1M170.114,316.258L178.202,305.739L173.087,305.739L173.087,296.123L167.14,296.123L167.14,305.739L162.025,305.739z" Height="16" Stretch="Fill" Fill="Black" Width="16"/>
            </DataTemplate>

            <DataTemplate x:Key="RetryButton">
                <Path Data="F1M552.616,598.453C552.616,596.21,552.05,593.99,550.978,592.035C550.977,592.034,550.769,591.657,550.769,591.657L550.523,591.213L550.083,591.465L549.709,591.679C549.708,591.679,546.635,593.44,546.635,593.44C546.637,593.439,546.279,593.643,546.279,593.643L545.854,593.885L546.087,594.314L546.283,594.678C546.906,595.834,547.235,597.139,547.235,598.453C547.235,602.87,543.642,606.464,539.224,606.464C534.806,606.464,531.212,602.87,531.212,598.453C531.212,594.659,533.911,591.361,537.629,590.611L537.03,590.121L537.03,592.33L537.03,593.193L537.779,592.764L538.416,592.398C538.417,592.397,545.068,588.553,545.068,588.553C545.069,588.553,545.7,588.187,545.7,588.187L546.446,587.754L545.7,587.321L545.066,586.954C545.065,586.954,538.417,583.116,538.417,583.116C538.416,583.115,537.779,582.75,537.779,582.75L537.03,582.32L537.03,583.184L537.03,585.675L537.466,585.179C530.833,586.04,525.831,591.746,525.831,598.453C525.831,605.839,531.839,611.848,539.224,611.848C546.608,611.848,552.616,605.839,552.616,598.453" Fill="#FF636466" Height="16" Stretch="Fill" Width="16"/>
            </DataTemplate>

        </ResourceDictionary>
    </Application.Resources>
</Application>
