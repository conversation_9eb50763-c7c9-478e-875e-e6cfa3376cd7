﻿[2019/10/16 10:07:56] 一般信息:成功启动服务器
[2019/10/16 10:07:56] 一般信息:窗口加载成功
[2019/10/16 10:07:58] 一般信息:加工区PLC连接成功
[2019/10/16 10:07:58] 一般信息:装配区PLC连接成功
[2019/10/16 10:07:58] 一般信息:检测区PLC连接成功
[2019/10/16 10:10:02] 一般信息:已关闭服务器
[2019/10/16 10:10:02] 警告:检测区PLC断开连接
[2019/10/16 10:10:02] 警告:加工区PLC断开连接
[2019/10/16 10:10:02] 警告:装配区PLC断开连接
[2019/10/16 10:10:02] 一般信息:窗口关闭
[2019/10/16 10:16:00] 一般信息:成功启动服务器
[2019/10/16 10:16:00] 一般信息:窗口加载成功
[2019/10/16 10:16:01] 一般信息:加工区PLC连接成功
[2019/10/16 10:16:01] 一般信息:装配区PLC连接成功
[2019/10/16 10:16:01] 一般信息:检测区PLC连接成功
[2019/10/16 10:39:15] 警告:立库连接断开
[2019/10/16 10:39:15] 一般信息:立库连接成功
[2019/10/16 10:39:15] 一般信息:立库连接成功
[2019/10/16 10:39:15] 一般信息:立库连接成功
[2019/10/16 10:39:16] 一般信息:AGV连接成功
[2019/10/16 10:40:27] 一般信息:开启执行订单模式
[2019/10/16 10:40:27] 一般信息:向立库发送指令：头30003 流水号1010 库位号6 托盘ID2
[2019/10/16 10:40:27] 一般信息:向立库发送指令：头30003 流水号1010 库位号6 托盘ID2
[2019/10/16 10:40:27] 一般信息:已收到侧边出库命令
[2019/10/16 10:44:30] 一般信息:已侧边出库
[2019/10/16 10:44:30] 一般信息:向立库发送指令：头10003 流水号1011 库位号1 托盘ID1
[2019/10/16 10:44:30] 一般信息:向立库发送指令：头10003 流水号1011 库位号1 托盘ID1
[2019/10/16 10:44:33] 一般信息:向立库发送指令：头10003 流水号1011 库位号1 托盘ID1
[2019/10/16 10:44:33] 一般信息:向立库发送指令：头10003 流水号1011 库位号1 托盘ID1
[2019/10/16 10:44:36] 一般信息:向立库发送指令：头10003 流水号1011 库位号1 托盘ID1
[2019/10/16 10:44:36] 一般信息:向立库发送指令：头10003 流水号1011 库位号1 托盘ID1
[2019/10/16 10:44:36] 一般信息:已收到出库指令
[2019/10/16 10:46:07] 一般信息:向立库发送指令：头29000 流水号0 库位号0 托盘ID0
[2019/10/16 10:46:07] 一般信息:写码完成
[2019/10/16 10:46:08] 一般信息:向AGV发送指令：头10003 任务号1000 取货点34 卸货点32 急停信号0
[2019/10/16 10:46:08] 一般信息:AGV已收到消息
[2019/10/16 10:46:19] 一般信息:已出库
[2019/10/16 10:48:08] 一般信息:侧边出库区处取货完毕
[2019/10/16 10:48:08] 一般信息:向AGV发送指令：头10012 任务号0 取货点0 卸货点0 急停信号6
[2019/10/16 10:49:00] 一般信息:立库入库区处卸货完毕
[2019/10/16 10:49:02] 一般信息:向AGV发送指令：头10003 任务号1001 取货点29 卸货点25 急停信号0
[2019/10/16 10:49:02] 一般信息:AGV已收到消息
[2019/10/16 10:49:04] 一般信息:向立库发送指令：头20003 流水号1012 库位号1 托盘ID1
[2019/10/16 10:49:04] 一般信息:向立库发送指令：头20003 流水号1012 库位号1 托盘ID1
[2019/10/16 10:49:04] 一般信息:已收到入库指令
[2019/10/16 10:49:43] 一般信息:立库出库区处取货完毕
[2019/10/16 10:50:33] 一般信息:加工区1处卸货完毕
[2019/10/16 10:50:49] 一般信息:已入库
[2019/10/16 10:50:52] 一般信息:向立库发送指令：头10003 流水号1013 库位号4 托盘ID3
[2019/10/16 10:50:52] 一般信息:向立库发送指令：头10003 流水号1013 库位号4 托盘ID3
[2019/10/16 10:50:55] 一般信息:向立库发送指令：头10003 流水号1013 库位号4 托盘ID3
[2019/10/16 10:50:55] 一般信息:向立库发送指令：头10003 流水号1013 库位号4 托盘ID3
[2019/10/16 10:50:55] 一般信息:已收到出库指令
[2019/10/16 10:53:31] 一般信息:已出库
[2019/10/16 10:53:32] 一般信息:向AGV发送指令：头10003 任务号1002 取货点29 卸货点0 急停信号0
[2019/10/16 10:53:32] 一般信息:AGV已收到消息
[2019/10/16 10:55:31] 一般信息:立库出库区处取货完毕
[2019/10/16 10:57:50] 一般信息:检测区1处卸货完毕
[2019/10/16 10:57:55] 一般信息:向立库发送指令：头10003 流水号1014 库位号2 托盘ID9
[2019/10/16 10:57:55] 一般信息:向立库发送指令：头10003 流水号1014 库位号2 托盘ID9
[2019/10/16 10:57:55] 一般信息:已收到出库指令
[2019/10/16 11:00:03] 一般信息:已出库
[2019/10/16 11:00:05] 一般信息:向AGV发送指令：头10003 任务号1003 取货点29 卸货点11 急停信号0
[2019/10/16 11:00:05] 一般信息:AGV已收到消息
[2019/10/16 11:02:06] 一般信息:立库出库区处取货完毕
[2019/10/16 11:03:54] 一般信息:拧螺丝处卸货完毕
[2019/10/16 11:03:56] 一般信息:向AGV发送指令：头10003 任务号1004 取货点25 卸货点32 急停信号0
[2019/10/16 11:03:56] 一般信息:AGV已收到消息
[2019/10/16 11:04:02] 一般信息:向立库发送指令：头10003 流水号1015 库位号15 托盘ID5
[2019/10/16 11:04:02] 一般信息:向立库发送指令：头10003 流水号1015 库位号15 托盘ID5
[2019/10/16 11:04:02] 一般信息:已收到出库指令
[2019/10/16 11:05:22] 一般信息:加工区1处取货完毕
[2019/10/16 11:05:22] 一般信息:向AGV发送指令：头10012 任务号0 取货点0 卸货点0 急停信号6
[2019/10/16 11:06:18] 一般信息:立库入库区处卸货完毕
[2019/10/16 11:06:20] 一般信息:向AGV发送指令：头10003 任务号1005 取货点0 卸货点32 急停信号0
[2019/10/16 11:06:20] 一般信息:AGV已收到消息
[2019/10/16 11:07:25] 一般信息:已出库
[2019/10/16 11:07:26] 一般信息:向立库发送指令：头20003 流水号1016 库位号2 托盘ID3
[2019/10/16 11:07:26] 一般信息:向立库发送指令：头20003 流水号1016 库位号2 托盘ID3
[2019/10/16 11:07:29] 一般信息:向立库发送指令：头20003 流水号1016 库位号2 托盘ID3
[2019/10/16 11:07:29] 一般信息:向立库发送指令：头20003 流水号1016 库位号2 托盘ID3
[2019/10/16 11:07:32] 一般信息:向立库发送指令：头20003 流水号1016 库位号2 托盘ID3
[2019/10/16 11:07:32] 一般信息:向立库发送指令：头20003 流水号1016 库位号2 托盘ID3
[2019/10/16 11:07:32] 一般信息:已收到入库指令
[2019/10/16 11:08:25] 一般信息:检测区1处取货完毕
[2019/10/16 11:08:25] 一般信息:向AGV发送指令：头10012 任务号0 取货点0 卸货点0 急停信号6
[2019/10/16 11:09:35] 一般信息:已入库
[2019/10/16 11:09:38] 一般信息:向立库发送指令：头30003 流水号1017 库位号9 托盘ID2
[2019/10/16 11:09:38] 一般信息:向立库发送指令：头30003 流水号1017 库位号9 托盘ID2
[2019/10/16 11:09:41] 一般信息:向立库发送指令：头30003 流水号1017 库位号9 托盘ID2
[2019/10/16 11:09:41] 一般信息:向立库发送指令：头30003 流水号1017 库位号9 托盘ID2
[2019/10/16 11:09:41] 一般信息:已收到侧边出库命令
[2019/10/16 11:10:50] 一般信息:立库入库区处卸货完毕
[2019/10/16 11:10:50] 一般信息:向AGV发送指令：头10003 任务号1006 取货点29 卸货点13 急停信号0
[2019/10/16 11:10:50] 一般信息:AGV已收到消息
[2019/10/16 11:11:31] 一般信息:立库出库区处取货完毕
[2019/10/16 11:13:09] 一般信息:轴承压装处卸货完毕
[2019/10/16 11:13:16] 一般信息:已侧边出库
[2019/10/16 11:13:17] 一般信息:向立库发送指令：头10003 流水号1018 库位号1 托盘ID1
[2019/10/16 11:13:17] 一般信息:向立库发送指令：头10003 流水号1018 库位号1 托盘ID1
[2019/10/16 11:13:20] 一般信息:向立库发送指令：头10003 流水号1018 库位号1 托盘ID1
[2019/10/16 11:13:20] 一般信息:向立库发送指令：头10003 流水号1018 库位号1 托盘ID1
[2019/10/16 11:13:23] 一般信息:向立库发送指令：头10003 流水号1018 库位号1 托盘ID1
[2019/10/16 11:13:23] 一般信息:向立库发送指令：头10003 流水号1018 库位号1 托盘ID1
[2019/10/16 11:13:23] 一般信息:已收到出库指令
[2019/10/16 11:14:01] 一般信息:向立库发送指令：头29000 流水号0 库位号0 托盘ID0
[2019/10/16 11:14:01] 一般信息:写码完成
[2019/10/16 11:14:02] 一般信息:向AGV发送指令：头10003 任务号1007 取货点34 卸货点32 急停信号0
[2019/10/16 11:14:02] 一般信息:AGV已收到消息
[2019/10/16 11:15:06] 一般信息:已出库
[2019/10/16 11:15:08] 一般信息:向立库发送指令：头20003 流水号1019 库位号4 托盘ID4
[2019/10/16 11:15:08] 一般信息:向立库发送指令：头20003 流水号1019 库位号4 托盘ID4
[2019/10/16 11:15:11] 一般信息:向立库发送指令：头20003 流水号1019 库位号4 托盘ID4
[2019/10/16 11:15:11] 一般信息:向立库发送指令：头20003 流水号1019 库位号4 托盘ID4
[2019/10/16 11:15:11] 一般信息:已收到入库指令
[2019/10/16 11:16:05] 一般信息:侧边出库区处取货完毕
[2019/10/16 11:16:05] 一般信息:向AGV发送指令：头10012 任务号0 取货点0 卸货点0 急停信号6
[2019/10/16 11:16:57] 一般信息:立库入库区处卸货完毕
[2019/10/16 11:16:59] 一般信息:向AGV发送指令：头10003 任务号1009 取货点29 卸货点25 急停信号0
[2019/10/16 11:16:59] 一般信息:AGV已收到消息
[2019/10/16 11:17:33] 一般信息:已入库
[2019/10/16 11:17:35] 一般信息:向立库发送指令：头10003 流水号1020 库位号2 托盘ID3
[2019/10/16 11:17:35] 一般信息:向立库发送指令：头10003 流水号1020 库位号2 托盘ID3
[2019/10/16 11:17:37] 一般信息:立库出库区处取货完毕
[2019/10/16 11:17:38] 一般信息:向立库发送指令：头10003 流水号1020 库位号2 托盘ID3
[2019/10/16 11:17:38] 一般信息:向立库发送指令：头10003 流水号1020 库位号2 托盘ID3
[2019/10/16 11:17:38] 一般信息:已收到出库指令
[2019/10/16 11:18:27] 一般信息:加工区1处卸货完毕
[2019/10/16 11:18:29] 一般信息:向AGV发送指令：头10003 任务号1008 取货点11 卸货点32 急停信号0
[2019/10/16 11:18:29] 一般信息:AGV已收到消息
[2019/10/16 11:19:40] 一般信息:已出库
[2019/10/16 11:19:41] 一般信息:向立库发送指令：头20003 流水号1021 库位号1 托盘ID1
[2019/10/16 11:19:41] 一般信息:向立库发送指令：头20003 流水号1021 库位号1 托盘ID1
[2019/10/16 11:19:44] 一般信息:向立库发送指令：头20003 流水号1021 库位号1 托盘ID1
[2019/10/16 11:19:44] 一般信息:向立库发送指令：头20003 流水号1021 库位号1 托盘ID1
[2019/10/16 11:19:47] 一般信息:向立库发送指令：头20003 流水号1021 库位号1 托盘ID1
[2019/10/16 11:19:47] 一般信息:向立库发送指令：头20003 流水号1021 库位号1 托盘ID1
[2019/10/16 11:19:47] 一般信息:已收到入库指令
[2019/10/16 11:19:49] 一般信息:拧螺丝处取货完毕
[2019/10/16 11:19:49] 一般信息:向AGV发送指令：头10012 任务号0 取货点0 卸货点0 急停信号6
[2019/10/16 11:21:24] 一般信息:已入库
[2019/10/16 11:21:26] 一般信息:向立库发送指令：头10003 流水号1022 库位号7 托盘ID3
[2019/10/16 11:21:26] 一般信息:向立库发送指令：头10003 流水号1022 库位号7 托盘ID3
[2019/10/16 11:21:29] 一般信息:向立库发送指令：头10003 流水号1022 库位号7 托盘ID3
[2019/10/16 11:21:29] 一般信息:向立库发送指令：头10003 流水号1022 库位号7 托盘ID3
[2019/10/16 11:21:29] 一般信息:已收到出库指令
[2019/10/16 11:21:42] 一般信息:立库入库区处卸货完毕
[2019/10/16 11:21:44] 一般信息:向AGV发送指令：头10003 任务号1011 取货点29 卸货点0 急停信号0
[2019/10/16 11:21:44] 一般信息:AGV已收到消息
[2019/10/16 11:22:22] 一般信息:立库出库区处取货完毕
[2019/10/16 11:23:55] 一般信息:已出库
[2019/10/16 11:23:56] 一般信息:向立库发送指令：头30003 流水号1023 库位号13 托盘ID2
[2019/10/16 11:23:56] 一般信息:向立库发送指令：头30003 流水号1023 库位号13 托盘ID2
[2019/10/16 11:23:59] 一般信息:向立库发送指令：头30003 流水号1023 库位号13 托盘ID2
[2019/10/16 11:23:59] 一般信息:向立库发送指令：头30003 流水号1023 库位号13 托盘ID2
[2019/10/16 11:24:02] 一般信息:向立库发送指令：头30003 流水号1023 库位号13 托盘ID2
[2019/10/16 11:24:02] 一般信息:向立库发送指令：头30003 流水号1023 库位号13 托盘ID2
[2019/10/16 11:24:02] 一般信息:已收到侧边出库命令
[2019/10/16 11:24:42] 一般信息:检测区1处卸货完毕
[2019/10/16 11:24:44] 一般信息:向AGV发送指令：头10003 任务号1012 取货点29 卸货点0 急停信号0
[2019/10/16 11:24:44] 一般信息:AGV已收到消息
[2019/10/16 11:26:40] 一般信息:立库出库区处取货完毕
[2019/10/16 11:28:00] 一般信息:已侧边出库
[2019/10/16 11:28:02] 一般信息:向立库发送指令：头10003 流水号1024 库位号3 托盘ID1
[2019/10/16 11:28:02] 一般信息:向立库发送指令：头10003 流水号1024 库位号3 托盘ID1
[2019/10/16 11:28:05] 一般信息:向立库发送指令：头10003 流水号1024 库位号3 托盘ID1
[2019/10/16 11:28:05] 一般信息:向立库发送指令：头10003 流水号1024 库位号3 托盘ID1
[2019/10/16 11:28:05] 一般信息:已收到出库指令
[2019/10/16 11:28:45] 一般信息:向立库发送指令：头29000 流水号0 库位号0 托盘ID0
[2019/10/16 11:28:45] 一般信息:写码完成
[2019/10/16 12:12:45] 一般信息:成功启动服务器
[2019/10/16 12:12:45] 一般信息:窗口加载成功
[2019/10/16 12:12:46] 一般信息:加工区PLC连接成功
[2019/10/16 12:12:46] 一般信息:装配区PLC连接成功
[2019/10/16 12:12:47] 一般信息:检测区PLC连接成功
[2019/10/16 12:33:53] 一般信息:已关闭服务器
[2019/10/16 12:33:53] 警告:加工区PLC断开连接
[2019/10/16 12:33:53] 警告:装配区PLC断开连接
[2019/10/16 12:33:53] 警告:检测区PLC断开连接
[2019/10/16 12:33:53] 一般信息:窗口关闭
[2019/10/16 19:13:01] 一般信息:成功启动服务器
[2019/10/16 19:13:01] 一般信息:窗口加载成功
[2019/10/16 19:13:03] 一般信息:已关闭服务器
[2019/10/16 19:13:03] 一般信息:窗口关闭
[2019/10/16 19:13:47] 一般信息:成功启动服务器
[2019/10/16 19:13:47] 一般信息:窗口加载成功
[2019/10/16 19:14:03] 一般信息:重置按钮被按下
[2019/10/16 19:14:30] 一般信息:下检测订单2
[2019/10/16 19:14:34] 一般信息:AGV连接成功
[2019/10/16 19:16:39] 一般信息:重置按钮被按下
[2019/10/16 19:16:41] 一般信息:重置按钮被按下
[2019/10/16 19:17:09] 警告:AGV连接断开
[2019/10/16 19:18:16] 一般信息:成功启动服务器
[2019/10/16 19:18:16] 一般信息:窗口加载成功
[2019/10/16 19:18:21] 警告:立库连接断开
[2019/10/16 19:18:21] 一般信息:立库连接成功
[2019/10/16 19:18:21] 一般信息:立库连接成功
[2019/10/16 19:18:21] 一般信息:立库连接成功
[2019/10/16 19:18:40] 一般信息:下检测订单2
[2019/10/16 19:18:43] 一般信息:AGV连接成功
[2019/10/16 19:19:56] 一般信息:重置按钮被按下
[2019/10/16 19:19:58] 一般信息:下检测订单2
[2019/10/16 19:20:11] 一般信息:已关闭服务器
[2019/10/16 19:20:11] 警告:立库连接断开
[2019/10/16 19:20:11] 警告:立库连接断开
[2019/10/16 19:20:11] 警告:AGV连接断开
[2019/10/16 19:20:11] 一般信息:窗口关闭
[2019/10/16 19:20:49] 一般信息:成功启动服务器
[2019/10/16 19:20:49] 一般信息:窗口加载成功
[2019/10/16 19:20:51] 警告:立库连接断开
[2019/10/16 19:20:52] 一般信息:立库连接成功
[2019/10/16 19:20:52] 一般信息:立库连接成功
[2019/10/16 19:20:52] 一般信息:立库连接成功
[2019/10/16 19:20:52] 一般信息:AGV连接成功
[2019/10/16 19:20:59] 一般信息:下检测订单2
[2019/10/16 19:24:17] 一般信息:成功启动服务器
[2019/10/16 19:24:17] 一般信息:窗口加载成功
[2019/10/16 19:24:19] 警告:立库连接断开
[2019/10/16 19:24:20] 一般信息:立库连接成功
[2019/10/16 19:24:20] 一般信息:立库连接成功
[2019/10/16 19:24:27] 一般信息:成功启动服务器
[2019/10/16 19:24:27] 一般信息:窗口加载成功
[2019/10/16 19:24:29] 警告:立库连接断开
[2019/10/16 19:24:30] 一般信息:立库连接成功
[2019/10/16 19:24:30] 一般信息:立库连接成功
[2019/10/16 19:24:30] 一般信息:立库连接成功
[2019/10/16 19:24:31] 一般信息:AGV连接成功
[2019/10/16 19:24:41] 一般信息:下检测订单2
[2019/10/16 19:25:12] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:12] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:15] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:15] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:18] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:18] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:21] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:21] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:24] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:24] 一般信息:向立库发送指令：头30003 流水号1008 库位号1 托盘ID2
[2019/10/16 19:25:26] 警告:AGV连接断开
[2019/10/16 19:25:26] 警告:立库连接断开
[2019/10/16 19:25:26] 警告:立库连接断开
[2019/10/16 19:32:59] 一般信息:成功启动服务器
[2019/10/16 19:32:59] 一般信息:窗口加载成功
[2019/10/16 19:33:14] 一般信息:下检测订单2
立库连接错误
[2019/10/16 19:33:20] 警告:AGV连接出错
[2019/10/16 19:33:23] 警告:AGV连接出错
[2019/10/16 19:33:23] 警告:AGV连接出错
[2019/10/16 19:33:50] 一般信息:立库连接成功
[2019/10/16 19:33:50] 一般信息:立库连接成功
[2019/10/16 19:33:51] 一般信息:AGV连接成功
[2019/10/16 19:33:51] 一般信息:AGV连接成功
[2019/10/16 19:33:51] 一般信息:AGV连接成功
[2019/10/16 19:34:20] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:23] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:26] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:29] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:32] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:35] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:38] 一般信息:向立库发送指令：头10003 流水号1007 库位号1 托盘ID1
[2019/10/16 19:34:39] 警告:AGV连接断开
[2019/10/16 19:34:39] 警告:立库连接断开
[2019/10/16 19:34:39] 警告:AGV连接断开
[2019/10/16 19:34:39] 警告:AGV连接断开
[2019/10/16 19:34:39] 警告:立库连接断开
[2019/10/16 19:35:03] 一般信息:成功启动服务器
[2019/10/16 19:35:03] 一般信息:窗口加载成功
[2019/10/16 19:35:31] 一般信息:成功启动服务器
[2019/10/16 19:35:31] 一般信息:窗口加载成功
[2019/10/16 19:35:34] 警告:立库连接断开
[2019/10/16 19:35:34] 一般信息:立库连接成功
[2019/10/16 19:35:34] 一般信息:立库连接成功
[2019/10/16 19:35:34] 一般信息:立库连接成功
[2019/10/16 19:35:35] 一般信息:AGV连接成功
[2019/10/16 19:35:42] 一般信息:下检测订单2
[2019/10/16 19:36:07] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:07] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:10] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:10] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:13] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:13] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:16] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:16] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:19] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:19] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:22] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:22] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:25] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:25] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:28] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:28] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:31] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:31] 一般信息:向立库发送指令：头30003 流水号1006 库位号2 托盘ID2
[2019/10/16 19:36:32] 警告:AGV连接断开
[2019/10/16 19:36:32] 警告:立库连接断开
[2019/10/16 19:36:32] 警告:立库连接断开
[2019/10/16 19:36:33] 一般信息:已关闭服务器
[2019/10/16 19:36:33] 一般信息:窗口关闭
[2019/10/16 19:44:29] 一般信息:成功启动服务器
[2019/10/16 19:44:29] 一般信息:窗口加载成功
[2019/10/16 19:45:23] 一般信息:立库连接成功
[2019/10/16 19:45:23] 一般信息:立库连接成功
[2019/10/16 19:45:24] 一般信息:AGV连接成功
[2019/10/16 19:45:45] 一般信息:下检测订单2
[2019/10/16 19:46:11] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:11] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:14] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:14] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:17] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:17] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:20] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:20] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:23] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:23] 一般信息:向立库发送指令：头10003 流水号1005 库位号12 托盘ID1
[2019/10/16 19:46:25] 警告:AGV连接断开
[2019/10/16 19:46:25] 警告:立库连接断开
[2019/10/16 19:46:31] 一般信息:已关闭服务器
[2019/10/16 19:46:31] 一般信息:窗口关闭
[2019/10/16 19:49:11] 一般信息:成功启动服务器
[2019/10/16 19:49:11] 一般信息:窗口加载成功
[2019/10/16 19:49:45] 一般信息:立库连接成功
[2019/10/16 19:49:45] 一般信息:立库连接成功
[2019/10/16 19:49:46] 一般信息:AGV连接成功
[2019/10/16 19:49:56] 一般信息:下检测订单2
[2019/10/16 19:50:21] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:21] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:24] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:24] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:27] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:27] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:30] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:30] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:33] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:33] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:36] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:36] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:39] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:39] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:42] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:42] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:45] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:45] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:48] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:48] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:51] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:51] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:54] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:54] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:57] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:50:57] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:51:00] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:51:00] 一般信息:向立库发送指令：头30003 流水号1004 库位号1 托盘ID2
[2019/10/16 19:51:00] 警告:立库连接断开
[2019/10/16 20:20:04] 一般信息:成功启动服务器
[2019/10/16 20:20:04] 一般信息:窗口加载成功
[2019/10/16 20:20:08] 警告:立库连接断开
[2019/10/16 20:20:08] 一般信息:立库连接成功
[2019/10/16 20:20:08] 一般信息:立库连接成功
[2019/10/16 20:20:09] 一般信息:AGV连接成功
[2019/10/16 20:20:21] 一般信息:下检测订单2
[2019/10/16 20:20:48] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:20:51] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:20:54] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:20:57] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:00] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:03] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:06] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:09] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:12] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:15] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:18] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:21] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:24] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:27] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:30] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:33] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:36] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:39] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:42] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:45] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:48] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:51] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:54] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:21:57] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:00] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:03] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:06] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:09] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:12] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:15] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:18] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:21] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:24] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:27] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:30] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:33] 一般信息:向立库发送指令：头10003 流水号1003 库位号2 托盘ID1
[2019/10/16 20:22:46] 一般信息:成功启动服务器
[2019/10/16 20:22:46] 一般信息:窗口加载成功
[2019/10/16 20:22:49] 警告:立库连接断开
[2019/10/16 20:22:50] 一般信息:立库连接成功
[2019/10/16 20:22:50] 一般信息:立库连接成功
[2019/10/16 20:22:50] 一般信息:立库连接成功
[2019/10/16 20:22:50] 一般信息:AGV连接成功
[2019/10/16 20:23:00] 一般信息:下检测订单2
[2019/10/16 20:23:38] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:38] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:41] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:41] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:44] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:44] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:47] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:47] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:50] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:50] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:53] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:53] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:56] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:56] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:59] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:23:59] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:24:02] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:24:02] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:24:05] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:24:05] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:24:05] 警告:AGV连接断开
[2019/10/16 20:24:05] 警告:立库连接断开
[2019/10/16 20:24:05] 警告:立库连接断开
[2019/10/16 20:24:23] 一般信息:已关闭服务器
[2019/10/16 20:24:23] 一般信息:窗口关闭
[2019/10/16 20:25:00] 一般信息:成功启动服务器
[2019/10/16 20:25:00] 一般信息:窗口加载成功
立库连接错误
立库连接错误
立库连接错误
立库连接错误
[2019/10/16 20:25:44] 警告:AGV连接出错
[2019/10/16 20:26:15] 警告:立库连接断开
[2019/10/16 20:26:15] 警告:立库连接断开
[2019/10/16 20:26:15] 警告:立库连接断开
[2019/10/16 20:26:16] 一般信息:立库连接成功
[2019/10/16 20:26:16] 一般信息:立库连接成功
[2019/10/16 20:26:16] 一般信息:立库连接成功
[2019/10/16 20:26:16] 一般信息:立库连接成功
[2019/10/16 20:26:16] 一般信息:AGV连接成功
[2019/10/16 20:26:16] 一般信息:AGV连接成功
[2019/10/16 20:26:28] 一般信息:下检测订单2
[2019/10/16 20:26:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:26:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:26:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:26:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:26:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:26:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:27:28] 警告:立库连接断开
[2019/10/16 20:27:30] 警告:立库连接断开
[2019/10/16 20:27:31] 一般信息:立库连接成功
[2019/10/16 20:27:31] 一般信息:立库连接成功
[2019/10/16 20:28:34] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:34] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:34] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:37] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:37] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:37] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:40] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:40] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:40] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:43] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:43] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:43] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:46] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:46] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:46] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:49] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:49] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:49] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:52] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:52] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:52] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:28:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:11] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:14] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:31] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:34] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:37] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:40] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:43] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:46] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:49] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:52] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:29:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:11] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:11] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:11] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:14] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:14] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:14] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:31] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:34] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:37] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:40] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:43] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:46] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:49] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:52] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:55] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:56] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:58] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:30:59] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:01] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:02] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:04] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:05] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:07] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:08] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:31:10] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:31:11] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:31:11] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:31:11] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:31:13] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:14] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:14] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:14] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:16] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:17] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:17] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:17] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:31:44] 警告:立库连接断开
[2019/10/16 20:31:45] 警告:AGV连接断开
[2019/10/16 20:31:45] 警告:AGV连接断开
[2019/10/16 20:31:50] 一般信息:重置按钮被按下
[2019/10/16 20:32:18] 警告:立库连接断开
[2019/10/16 20:32:19] 一般信息:立库连接成功
[2019/10/16 20:32:19] 一般信息:立库连接成功
[2019/10/16 20:32:20] 一般信息:AGV连接成功
[2019/10/16 20:32:20] 一般信息:AGV连接成功
[2019/10/16 20:32:20] 一般信息:AGV连接成功
[2019/10/16 20:32:22] 一般信息:下检测订单2
[2019/10/16 20:32:37] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:38] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:38] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:40] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:40] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:41] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:41] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:43] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:43] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:44] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:44] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:46] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:46] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:47] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:47] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:49] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:49] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:50] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:50] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:52] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:52] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:53] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:53] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:55] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:56] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:56] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:56] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:58] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:59] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:59] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:32:59] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:01] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:02] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:02] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:02] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:04] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:05] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:05] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:05] 一般信息:向立库发送指令：头30003 流水号1003 库位号23 托盘ID2
[2019/10/16 20:33:06] 警告:立库连接断开
[2019/10/16 20:33:07] 警告:AGV连接断开
[2019/10/16 20:33:07] 警告:AGV连接断开
[2019/10/16 20:33:07] 警告:AGV连接断开
[2019/10/16 20:33:11] 一般信息:已关闭服务器
[2019/10/16 20:33:11] 一般信息:窗口关闭
[2019/10/16 20:33:51] 一般信息:成功启动服务器
[2019/10/16 20:33:51] 一般信息:窗口加载成功
[2019/10/16 20:33:52] 一般信息:已关闭服务器
[2019/10/16 20:33:52] 一般信息:窗口关闭
[2019/10/16 20:34:51] 一般信息:成功启动服务器
[2019/10/16 20:34:51] 一般信息:窗口加载成功
[2019/10/16 20:35:15] 一般信息:立库连接成功
[2019/10/16 20:35:16] 一般信息:AGV连接成功
[2019/10/16 20:35:24] 一般信息:下检测订单2
[2019/10/16 20:35:57] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:00] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:03] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:06] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:09] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:12] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:15] 一般信息:向立库发送指令：头30003 流水号1002 库位号75 托盘ID2
[2019/10/16 20:36:15] 警告:AGV连接断开
[2019/10/16 20:36:18] 警告:立库连接断开
[2019/10/16 20:36:20] 一般信息:立库连接成功
[2019/10/16 20:36:20] 一般信息:立库连接成功
[2019/10/16 20:36:45] 一般信息:重置按钮被按下
[2019/10/16 20:36:47] 一般信息:下检测订单2
[2019/10/16 20:36:51] 一般信息:AGV连接成功
[2019/10/16 20:36:51] 一般信息:AGV连接成功
[2019/10/16 20:37:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:35] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:38] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:41] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:44] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号74 托盘ID2
[2019/10/16 20:37:47] 警告:立库连接断开
[2019/10/16 20:37:47] 警告:立库连接断开
[2019/10/16 20:37:50] 一般信息:立库连接成功
[2019/10/16 20:37:50] 一般信息:立库连接成功
[2019/10/16 20:37:50] 一般信息:立库连接成功
[2019/10/16 20:37:54] 一般信息:重置按钮被按下
[2019/10/16 20:38:00] 一般信息:下检测订单2
[2019/10/16 20:38:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:47] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:50] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:53] 一般信息:向立库发送指令：头30003 流水号1001 库位号3 托盘ID2
[2019/10/16 20:38:55] 警告:立库连接断开
[2019/10/16 20:38:55] 警告:立库连接断开
[2019/10/16 20:38:55] 警告:立库连接断开
[2019/10/16 20:40:37] 一般信息:立库连接成功
[2019/10/16 20:40:37] 一般信息:立库连接成功
[2019/10/16 20:40:37] 一般信息:立库连接成功
[2019/10/16 20:40:37] 一般信息:立库连接成功
[2019/10/16 20:40:44] 一般信息:重置按钮被按下
[2019/10/16 20:40:46] 一般信息:下检测订单2
[2019/10/16 20:41:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:17] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:20] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:23] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:26] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:29] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:31] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:31] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:31] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:31] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:32] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:41:34] 警告:立库连接断开
[2019/10/16 20:41:34] 警告:立库连接断开
[2019/10/16 20:41:34] 警告:立库连接断开
[2019/10/16 20:41:34] 警告:立库连接断开
[2019/10/16 20:42:13] 警告:AGV连接断开
[2019/10/16 20:42:13] 警告:AGV连接断开
[2019/10/16 20:42:15] 一般信息:已关闭服务器
[2019/10/16 20:42:15] 一般信息:窗口关闭
[2019/10/16 20:44:20] 一般信息:成功启动服务器
[2019/10/16 20:44:20] 一般信息:窗口加载成功
[2019/10/16 20:47:02] 一般信息:下检测订单2
[2019/10/16 20:47:04] 一般信息:立库连接成功
[2019/10/16 20:47:05] 一般信息:AGV连接成功
[2019/10/16 20:47:07] 一般信息:向立库发送指令：头30003 流水号1004 库位号5 托盘ID3
[2019/10/16 20:47:10] 一般信息:向立库发送指令：头30003 流水号1004 库位号5 托盘ID3
[2019/10/16 20:47:13] 一般信息:向立库发送指令：头30003 流水号1004 库位号5 托盘ID3
[2019/10/16 20:47:16] 一般信息:向立库发送指令：头30003 流水号1004 库位号5 托盘ID3
[2019/10/16 20:47:18] 警告:立库连接断开
[2019/10/16 20:47:31] 一般信息:立库连接成功
[2019/10/16 20:47:35] 一般信息:重置按钮被按下
[2019/10/16 20:48:06] 一般信息:下检测订单2
[2019/10/16 20:48:10] 一般信息:向立库发送指令：头30003 流水号1003 库位号4 托盘ID2
[2019/10/16 20:48:13] 一般信息:向立库发送指令：头30003 流水号1003 库位号4 托盘ID2
[2019/10/16 20:48:16] 一般信息:向立库发送指令：头30003 流水号1003 库位号4 托盘ID2
[2019/10/16 20:48:19] 一般信息:向立库发送指令：头30003 流水号1003 库位号4 托盘ID2
[2019/10/16 20:48:22] 一般信息:向立库发送指令：头30003 流水号1003 库位号4 托盘ID2
[2019/10/16 20:48:25] 一般信息:向立库发送指令：头30003 流水号1003 库位号4 托盘ID2
[2019/10/16 20:48:28] 警告:立库连接断开
[2019/10/16 20:48:47] 一般信息:重置按钮被按下
[2019/10/16 20:49:04] 一般信息:下检测订单2
[2019/10/16 20:49:19] 一般信息:立库连接成功
[2019/10/16 20:49:43] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:49:46] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:49:49] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:49:52] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:49:55] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:49:58] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:50:01] 一般信息:向立库发送指令：头30003 流水号1002 库位号3 托盘ID2
[2019/10/16 20:50:03] 警告:立库连接断开
[2019/10/16 20:54:05] 一般信息:成功启动服务器
[2019/10/16 20:54:05] 一般信息:窗口加载成功
[2019/10/16 20:55:19] 警告:立库连接断开
[2019/10/16 20:55:20] 一般信息:立库连接成功
[2019/10/16 20:55:21] 一般信息:立库连接成功
[2019/10/16 20:55:22] 一般信息:AGV连接成功
[2019/10/16 20:55:32] 一般信息:下检测订单2
[2019/10/16 20:56:13] 一般信息:重置按钮被按下
[2019/10/16 20:56:17] 一般信息:下检测订单2
[2019/10/16 20:56:57] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:57:00] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:57:03] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:57:06] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:57:07] 警告:立库连接断开
[2019/10/16 20:57:11] 警告:立库连接断开
[2019/10/16 20:57:12] 一般信息:立库连接成功
[2019/10/16 20:57:12] 一般信息:立库连接成功
[2019/10/16 20:57:14] 一般信息:重置按钮被按下
[2019/10/16 20:57:16] 一般信息:下检测订单2
[2019/10/16 20:58:43] 一般信息:重置按钮被按下
[2019/10/16 20:58:46] 一般信息:下检测订单2
[2019/10/16 20:59:18] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:21] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:24] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:27] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:30] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:33] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:36] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 20:59:39] 警告:立库连接断开
[2019/10/16 20:59:41] 警告:立库连接断开
[2019/10/16 20:59:42] 一般信息:立库连接成功
[2019/10/16 20:59:43] 一般信息:立库连接成功
[2019/10/16 20:59:47] 一般信息:重置按钮被按下
[2019/10/16 20:59:48] 一般信息:下检测订单2
[2019/10/16 21:00:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:00:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:00:22] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:00:25] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:00:28] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:00:28] 警告:立库连接断开
[2019/10/16 21:00:39] 警告:立库连接断开
[2019/10/16 21:00:40] 一般信息:立库连接成功
[2019/10/16 21:00:40] 一般信息:立库连接成功
[2019/10/16 21:00:43] 一般信息:重置按钮被按下
[2019/10/16 21:00:45] 一般信息:下检测订单2
[2019/10/16 21:01:10] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:01:13] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:01:16] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:01:19] 一般信息:向立库发送指令：头30003 流水号1001 库位号2 托盘ID2
[2019/10/16 21:01:20] 警告:立库连接断开
[2019/10/16 21:01:26] 一般信息:重置按钮被按下
[2019/10/16 21:01:27] 一般信息:已关闭服务器
[2019/10/16 21:01:27] 一般信息:窗口关闭
