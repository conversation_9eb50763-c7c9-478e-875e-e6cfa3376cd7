-- 云深处科技lite3小狗MES系统数据库设计
-- 创建数据库
CREATE DATABASE Lite3DogMES;
USE Lite3DogMES;

-- 1. 产品主表 - 存储每只小狗的基本信息
CREATE TABLE Products (
    ProductID NVARCHAR(50) PRIMARY KEY,           -- 产品唯一标识
    ProductSN NVARCHAR(50) UNIQUE NOT NULL,       -- 产品序列号
    QRCode NVARCHAR(200) UNIQUE NOT NULL,         -- 二维码内容
    ProductModel NVARCHAR(50) DEFAULT 'Lite3',    -- 产品型号
    ProductionDate DATETIME DEFAULT GETDATE(),    -- 生产日期
    CurrentStatus NVARCHAR(50) DEFAULT '装配',     -- 当前状态
    CurrentLocation NVARCHAR(100),                -- 当前位置
    IsCompleted BIT DEFAULT 0,                    -- 是否完成全部流程
    CreatedTime DATETIME DEFAULT GETDATE(),       -- 创建时间
    UpdatedTime DATETIME DEFAULT GETDATE()        -- 更新时间
);

-- 2. 工序定义表 - 定义所有工序类型
CREATE TABLE ProcessDefinitions (
    ProcessID INT IDENTITY(1,1) PRIMARY KEY,      -- 工序ID
    ProcessName NVARCHAR(50) NOT NULL,            -- 工序名称
    ProcessCode NVARCHAR(20) NOT NULL UNIQUE,     -- 工序代码
    ProcessOrder INT NOT NULL,                    -- 工序顺序
    Description NVARCHAR(200),                    -- 工序描述
    RequiredTime INT DEFAULT 0,                   -- 标准用时(分钟)
    QualityCheckRequired BIT DEFAULT 0,           -- 是否需要质量检查
    IsActive BIT DEFAULT 1                        -- 是否启用
);

-- 插入工序定义数据
INSERT INTO ProcessDefinitions (ProcessName, ProcessCode, ProcessOrder, Description, RequiredTime, QualityCheckRequired) VALUES
('装配', 'ASSEMBLY', 1, '产品装配完成，分配序号并生成二维码', 30, 1),
('标零', 'CALIBRATION', 2, '一测区域标零操作', 10, 0),
('一测', 'FIRST_TEST', 3, '第一次测试', 20, 1),
('维护', 'MAINTENANCE', 4, '对一测产品进行维护', 15, 0),
('二测', 'SECOND_TEST', 5, '第二次测试', 20, 1),
('打包', 'PACKAGING', 6, '产品打包', 10, 0),
('发货', 'SHIPPING', 7, '产品发货', 5, 0);

-- 3. 工序记录表 - 记录每个产品的工序执行情况
CREATE TABLE ProcessRecords (
    RecordID INT IDENTITY(1,1) PRIMARY KEY,       -- 记录ID
    ProductID NVARCHAR(50) NOT NULL,              -- 产品ID
    ProcessID INT NOT NULL,                       -- 工序ID
    StartTime DATETIME,                           -- 开始时间
    EndTime DATETIME,                             -- 结束时间
    Status NVARCHAR(20) DEFAULT '进行中',          -- 状态：进行中、完成、失败
    QualityResult NVARCHAR(20),                   -- 质量结果：合格、不合格
    OperatorID NVARCHAR(50),                      -- 操作员ID
    Location NVARCHAR(100),                       -- 操作位置
    Remarks NVARCHAR(500),                        -- 备注
    CreatedTime DATETIME DEFAULT GETDATE(),       -- 创建时间
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    FOREIGN KEY (ProcessID) REFERENCES ProcessDefinitions(ProcessID)
);

-- 4. 二维码信息表 - 存储二维码相关信息
CREATE TABLE QRCodeInfo (
    QRCodeID INT IDENTITY(1,1) PRIMARY KEY,       -- 二维码ID
    ProductID NVARCHAR(50) NOT NULL,              -- 产品ID
    QRCodeContent NVARCHAR(500) NOT NULL,         -- 二维码内容
    QRCodeImage VARBINARY(MAX),                   -- 二维码图片
    GeneratedTime DATETIME DEFAULT GETDATE(),     -- 生成时间
    PrintedTime DATETIME,                         -- 打印时间
    LastScanTime DATETIME,                        -- 最后扫描时间
    ScanCount INT DEFAULT 0,                      -- 扫描次数
    IsActive BIT DEFAULT 1,                       -- 是否有效
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- 5. 扫描记录表 - 记录所有二维码扫描操作
CREATE TABLE ScanRecords (
    ScanID INT IDENTITY(1,1) PRIMARY KEY,         -- 扫描ID
    ProductID NVARCHAR(50) NOT NULL,              -- 产品ID
    ProcessID INT,                                -- 关联工序ID
    ScanTime DATETIME DEFAULT GETDATE(),          -- 扫描时间
    ScanLocation NVARCHAR(100),                   -- 扫描位置
    OperatorID NVARCHAR(50),                      -- 操作员ID
    ScanResult NVARCHAR(20) DEFAULT '成功',        -- 扫描结果
    DeviceID NVARCHAR(50),                        -- 扫描设备ID
    Remarks NVARCHAR(200),                        -- 备注
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    FOREIGN KEY (ProcessID) REFERENCES ProcessDefinitions(ProcessID)
);

-- 6. 质量检测表 - 存储质量检测数据
CREATE TABLE QualityTests (
    TestID INT IDENTITY(1,1) PRIMARY KEY,         -- 测试ID
    ProductID NVARCHAR(50) NOT NULL,              -- 产品ID
    ProcessID INT NOT NULL,                       -- 工序ID
    TestType NVARCHAR(50),                        -- 测试类型
    TestParameter NVARCHAR(100),                  -- 测试参数
    StandardValue DECIMAL(10,4),                  -- 标准值
    ActualValue DECIMAL(10,4),                    -- 实际值
    TestResult NVARCHAR(20),                      -- 测试结果：合格、不合格
    TestTime DATETIME DEFAULT GETDATE(),          -- 测试时间
    TesterID NVARCHAR(50),                        -- 测试员ID
    TestEquipment NVARCHAR(100),                  -- 测试设备
    Remarks NVARCHAR(500),                        -- 备注
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    FOREIGN KEY (ProcessID) REFERENCES ProcessDefinitions(ProcessID)
);

-- 7. 产品追溯表 - 完整的产品追溯信息
CREATE TABLE ProductTraceability (
    TraceID INT IDENTITY(1,1) PRIMARY KEY,        -- 追溯ID
    ProductID NVARCHAR(50) NOT NULL,              -- 产品ID
    EventType NVARCHAR(50) NOT NULL,              -- 事件类型
    EventDescription NVARCHAR(200),               -- 事件描述
    EventTime DATETIME DEFAULT GETDATE(),         -- 事件时间
    Location NVARCHAR(100),                       -- 位置
    OperatorID NVARCHAR(50),                      -- 操作员
    RelatedData NVARCHAR(MAX),                    -- 相关数据(JSON格式)
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- 8. 发货信息表 - 记录发货详细信息
CREATE TABLE ShippingInfo (
    ShippingID INT IDENTITY(1,1) PRIMARY KEY,     -- 发货ID
    ProductID NVARCHAR(50) NOT NULL,              -- 产品ID
    CustomerName NVARCHAR(100),                   -- 客户名称
    CustomerAddress NVARCHAR(500),                -- 客户地址
    ShippingDate DATETIME DEFAULT GETDATE(),      -- 发货日期
    TrackingNumber NVARCHAR(100),                 -- 快递单号
    ShippingCompany NVARCHAR(100),                -- 快递公司
    ShippingStatus NVARCHAR(50) DEFAULT '已发货', -- 发货状态
    OperatorID NVARCHAR(50),                      -- 操作员ID
    Remarks NVARCHAR(500),                        -- 备注
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- 9. 用户表 - 系统用户管理
CREATE TABLE Users (
    UserID NVARCHAR(50) PRIMARY KEY,              -- 用户ID
    UserName NVARCHAR(100) NOT NULL,              -- 用户名
    Password NVARCHAR(200) NOT NULL,              -- 密码(加密)
    RealName NVARCHAR(100),                       -- 真实姓名
    Department NVARCHAR(100),                     -- 部门
    Position NVARCHAR(100),                       -- 职位
    Role NVARCHAR(50) DEFAULT 'Operator',         -- 角色：Admin、Manager、Operator
    IsActive BIT DEFAULT 1,                       -- 是否启用
    CreatedTime DATETIME DEFAULT GETDATE(),       -- 创建时间
    LastLoginTime DATETIME                        -- 最后登录时间
);

-- 插入默认管理员用户
INSERT INTO Users (UserID, UserName, Password, RealName, Department, Position, Role) VALUES
('admin', 'admin', 'admin123', '系统管理员', 'IT部', '系统管理员', 'Admin'),
('operator1', 'operator1', 'op123', '操作员1', '生产部', '生产操作员', 'Operator');

-- 10. 系统配置表 - 存储系统配置信息
CREATE TABLE SystemConfig (
    ConfigID INT IDENTITY(1,1) PRIMARY KEY,       -- 配置ID
    ConfigKey NVARCHAR(100) NOT NULL UNIQUE,      -- 配置键
    ConfigValue NVARCHAR(500),                    -- 配置值
    Description NVARCHAR(200),                    -- 描述
    Category NVARCHAR(50),                        -- 分类
    IsActive BIT DEFAULT 1,                       -- 是否启用
    UpdatedTime DATETIME DEFAULT GETDATE()        -- 更新时间
);

-- 插入系统配置
INSERT INTO SystemConfig (ConfigKey, ConfigValue, Description, Category) VALUES
('QR_CODE_PREFIX', 'LITE3_', '二维码前缀', 'QRCode'),
('PRODUCT_SN_PREFIX', 'L3D', '产品序列号前缀', 'Product'),
('DEFAULT_LOCATION', '云深处科技生产车间', '默认生产位置', 'Location'),
('QUALITY_PASS_SCORE', '80', '质量合格分数', 'Quality');

-- 创建视图
-- 1. 产品状态视图 - 显示产品当前状态和进度
CREATE VIEW V_ProductStatus AS
SELECT
    p.ProductID,
    p.ProductSN,
    p.QRCode,
    p.ProductModel,
    p.ProductionDate,
    p.CurrentStatus,
    p.CurrentLocation,
    p.IsCompleted,
    ISNULL(pr.CompletedProcesses, 0) as CompletedProcesses,
    (SELECT COUNT(*) FROM ProcessDefinitions WHERE IsActive = 1) as TotalProcesses,
    CAST(ISNULL(pr.CompletedProcesses, 0) * 100.0 / (SELECT COUNT(*) FROM ProcessDefinitions WHERE IsActive = 1) AS DECIMAL(5,2)) as CompletionRate
FROM Products p
LEFT JOIN (
    SELECT
        ProductID,
        COUNT(DISTINCT ProcessID) as CompletedProcesses
    FROM ProcessRecords
    WHERE Status = '完成'
    GROUP BY ProductID
) pr ON p.ProductID = pr.ProductID;

-- 2. 工序进度视图 - 显示每个工序的执行情况
CREATE VIEW V_ProcessProgress AS
SELECT
    pr.ProductID,
    p.ProductSN,
    pd.ProcessName,
    pd.ProcessCode,
    pd.ProcessOrder,
    pr.StartTime,
    pr.EndTime,
    pr.Status,
    pr.QualityResult,
    pr.OperatorID,
    pr.Location,
    CASE
        WHEN pr.EndTime IS NOT NULL AND pr.StartTime IS NOT NULL
        THEN DATEDIFF(MINUTE, pr.StartTime, pr.EndTime)
        ELSE NULL
    END as ActualTime,
    pd.RequiredTime,
    pr.Remarks
FROM ProcessRecords pr
INNER JOIN Products p ON pr.ProductID = p.ProductID
INNER JOIN ProcessDefinitions pd ON pr.ProcessID = pd.ProcessID;

-- 3. 质量统计视图 - 质量检测统计
CREATE VIEW V_QualityStatistics AS
SELECT
    qt.ProductID,
    p.ProductSN,
    pd.ProcessName,
    qt.TestType,
    qt.TestParameter,
    qt.StandardValue,
    qt.ActualValue,
    qt.TestResult,
    qt.TestTime,
    qt.TesterID,
    CASE
        WHEN qt.StandardValue > 0
        THEN ABS(qt.ActualValue - qt.StandardValue) / qt.StandardValue * 100
        ELSE 0
    END as DeviationPercent
FROM QualityTests qt
INNER JOIN Products p ON qt.ProductID = p.ProductID
INNER JOIN ProcessDefinitions pd ON qt.ProcessID = pd.ProcessID;

-- 4. 追溯信息视图 - 完整追溯链
CREATE VIEW V_ProductTrace AS
SELECT
    pt.ProductID,
    p.ProductSN,
    p.QRCode,
    pt.EventType,
    pt.EventDescription,
    pt.EventTime,
    pt.Location,
    pt.OperatorID,
    u.RealName as OperatorName,
    pt.RelatedData,
    ROW_NUMBER() OVER (PARTITION BY pt.ProductID ORDER BY pt.EventTime) as EventSequence
FROM ProductTraceability pt
INNER JOIN Products p ON pt.ProductID = p.ProductID
LEFT JOIN Users u ON pt.OperatorID = u.UserID;

-- 存储过程
-- 1. 创建新产品
CREATE PROCEDURE SP_CreateProduct
    @ProductSN NVARCHAR(50),
    @ProductModel NVARCHAR(50) = 'Lite3',
    @OperatorID NVARCHAR(50)
AS
BEGIN
    DECLARE @ProductID NVARCHAR(50)
    DECLARE @QRCode NVARCHAR(200)

    -- 生成产品ID
    SET @ProductID = NEWID()

    -- 生成二维码内容
    SET @QRCode = (SELECT ConfigValue FROM SystemConfig WHERE ConfigKey = 'QR_CODE_PREFIX') + @ProductSN

    BEGIN TRANSACTION

    -- 插入产品记录
    INSERT INTO Products (ProductID, ProductSN, QRCode, ProductModel, CurrentStatus, CurrentLocation)
    VALUES (@ProductID, @ProductSN, @QRCode, @ProductModel, '装配',
            (SELECT ConfigValue FROM SystemConfig WHERE ConfigKey = 'DEFAULT_LOCATION'))

    -- 插入二维码信息
    INSERT INTO QRCodeInfo (ProductID, QRCodeContent)
    VALUES (@ProductID, @QRCode)

    -- 记录追溯信息
    INSERT INTO ProductTraceability (ProductID, EventType, EventDescription, OperatorID)
    VALUES (@ProductID, '产品创建', '创建新产品: ' + @ProductSN, @OperatorID)

    COMMIT TRANSACTION

    SELECT @ProductID as ProductID, @QRCode as QRCode
END

-- 2. 开始工序
CREATE PROCEDURE SP_StartProcess
    @ProductID NVARCHAR(50),
    @ProcessCode NVARCHAR(20),
    @OperatorID NVARCHAR(50),
    @Location NVARCHAR(100) = NULL
AS
BEGIN
    DECLARE @ProcessID INT
    DECLARE @ProcessName NVARCHAR(50)

    -- 获取工序信息
    SELECT @ProcessID = ProcessID, @ProcessName = ProcessName
    FROM ProcessDefinitions
    WHERE ProcessCode = @ProcessCode AND IsActive = 1

    IF @ProcessID IS NULL
    BEGIN
        RAISERROR('工序代码不存在或已禁用', 16, 1)
        RETURN
    END

    BEGIN TRANSACTION

    -- 插入工序记录
    INSERT INTO ProcessRecords (ProductID, ProcessID, StartTime, Status, OperatorID, Location)
    VALUES (@ProductID, @ProcessID, GETDATE(), '进行中', @OperatorID, @Location)

    -- 更新产品状态
    UPDATE Products
    SET CurrentStatus = @ProcessName,
        CurrentLocation = ISNULL(@Location, CurrentLocation),
        UpdatedTime = GETDATE()
    WHERE ProductID = @ProductID

    -- 记录追溯信息
    INSERT INTO ProductTraceability (ProductID, EventType, EventDescription, Location, OperatorID)
    VALUES (@ProductID, '工序开始', '开始工序: ' + @ProcessName, @Location, @OperatorID)

    COMMIT TRANSACTION
END

-- 3. 完成工序
CREATE PROCEDURE SP_CompleteProcess
    @ProductID NVARCHAR(50),
    @ProcessCode NVARCHAR(20),
    @QualityResult NVARCHAR(20) = '合格',
    @OperatorID NVARCHAR(50),
    @Remarks NVARCHAR(500) = NULL
AS
BEGIN
    DECLARE @ProcessID INT
    DECLARE @ProcessName NVARCHAR(50)
    DECLARE @RecordID INT

    -- 获取工序信息
    SELECT @ProcessID = ProcessID, @ProcessName = ProcessName
    FROM ProcessDefinitions
    WHERE ProcessCode = @ProcessCode AND IsActive = 1

    -- 获取进行中的工序记录
    SELECT @RecordID = RecordID
    FROM ProcessRecords
    WHERE ProductID = @ProductID AND ProcessID = @ProcessID AND Status = '进行中'

    IF @RecordID IS NULL
    BEGIN
        RAISERROR('未找到进行中的工序记录', 16, 1)
        RETURN
    END

    BEGIN TRANSACTION

    -- 更新工序记录
    UPDATE ProcessRecords
    SET EndTime = GETDATE(),
        Status = '完成',
        QualityResult = @QualityResult,
        Remarks = @Remarks
    WHERE RecordID = @RecordID

    -- 记录追溯信息
    INSERT INTO ProductTraceability (ProductID, EventType, EventDescription, OperatorID, RelatedData)
    VALUES (@ProductID, '工序完成', '完成工序: ' + @ProcessName + ', 质量结果: ' + @QualityResult,
            @OperatorID, '{"QualityResult":"' + @QualityResult + '","Remarks":"' + ISNULL(@Remarks, '') + '"}')

    COMMIT TRANSACTION
END
